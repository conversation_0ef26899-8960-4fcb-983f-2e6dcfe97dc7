[14:13:23.852] INFO     | audio_action_controller.py:136 - Modbus 功能已禁用（pymodbus 未安装）
[14:13:25.965] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[14:13:25.966] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[14:13:25.966] INFO     | audio_action_controller.py:292 - 正在加载音频: ./asserts/ding.wav
[14:13:25.966] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[14:13:25.966] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.9675963521003723秒
[14:13:25.968] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:13:25.968] INFO     | audio_action_controller.py:327 - 开始播放音频
[14:13:25.968] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[14:13:25.968] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:25.968] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[14:13:25.968] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:26.089] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:26.120] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:26.210] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:26.271] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:26.331] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:26.422] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:26.451] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:26.572] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:26.573] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:26.693] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:26.723] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:26.814] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:26.874] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:26.975] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:13:26.975] INFO     | audio_action_controller.py:348 - 音频播放完成
[14:13:27.026] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:27.026] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[14:13:27.027] INFO     | audio_action_controller.py:67 - 检测到MP3文件，已应用针对压缩音频的优化设置
[14:13:27.027] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/tts/dog_ok.mp3
[14:13:27.033] INFO     | audio_action_controller.py:42 - 音频音量设置为: 85.0% (针对WAV无损格式优化)
[14:13:27.033] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 4.439773082733154秒
[14:13:27.033] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:13:27.034] INFO     | audio_action_controller.py:327 - 开始播放音频
[14:13:27.034] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[14:13:27.034] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:27.035] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[14:13:27.035] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:27.156] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:27.186] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:27.277] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:27.338] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:27.398] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:27.488] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:27.519] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:27.639] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:27.639] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:27.760] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:27.790] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:27.881] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:27.941] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:28.001] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:28.092] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:28.122] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:28.242] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:28.243] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:28.364] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:28.393] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:28.484] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:28.544] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:28.605] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:28.695] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:28.726] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:28.846] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:28.846] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:28.967] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:28.997] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:29.087] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:29.148] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:29.208] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:29.298] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:29.329] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:29.449] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:29.450] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:29.571] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:29.600] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:29.691] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:29.751] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:29.812] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:29.902] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:29.933] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:30.052] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:30.054] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:30.175] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:30.204] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:30.296] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:30.355] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:30.417] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:30.506] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:30.537] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:30.656] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:30.658] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:30.779] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:30.807] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:30.900] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:30.958] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:31.021] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:31.109] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:31.142] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:31.260] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:31.263] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:31.410] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:31.561] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:31.566] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:13:31.566] INFO     | audio_action_controller.py:348 - 音频播放完成
[14:13:31.712] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:31.863] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:32.014] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:32.165] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:13:32.315] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
