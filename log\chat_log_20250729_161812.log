2025-07-29 16:18:13.580 - chat_with_robot - chat_with_robot.py - <module> - line 658 - INFO - use_action: dont
2025-07-29 16:18:13.580 - chat_with_robot - chat_with_robot.py - <module> - line 659 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-29 16:18:13.586 - chat_with_robot - chat_with_robot.py - init_websocket - line 326 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1753777093587&accessNonce=094f3452-8f0f-4157-8058-3e9d4ddcd93f&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=924a1d49-6c54-11f0-9eb5-dc4546c07870&requestId=76cb52fa-8721-4979-bd6c-693f799561fa_joyinside&accessSign=43687a3662bc980ede068ec5db7ef160, request_id: 76cb52fa-8721-4979-bd6c-693f799561fa_joyinside
2025-07-29 16:18:13.587 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-29 16:18:13.587 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-29 16:18:14.048 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-29 16:18:14.096 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-07-29 16:18:15.536 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-29 16:18:15.536 - chat_with_robot - voice.py - _setup_audio_stream - line 336 - INFO - 使用音频设备: 1
2025-07-29 16:18:15.536 - chat_with_robot - voice.py - _setup_audio_stream - line 337 - INFO - channels: 4 <class 'int'>
2025-07-29 16:18:15.536 - chat_with_robot - voice.py - _setup_audio_stream - line 338 - INFO - rate: 44100.0 <class 'float'>
2025-07-29 16:18:15.606 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-29 16:18:15.606 - chat_with_robot - voice.py - init_wakeup - line 323 - INFO - 本地流式KWS检测器启动成功
2025-07-29 16:18:16.608 - chat_with_robot - chat_with_robot.py - play_audio - line 535 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-29 16:18:16.608 - chat_with_robot - chat_with_robot.py - play_audio - line 543 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-07-29 16:18:19.610 - chat_with_robot - chat_with_robot.py - play_audio - line 545 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-07-29 16:18:19.612 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 82 - INFO - 使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-07-29 16:18:19.612 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 85 - INFO - 统一音频控制器播放完成: asserts/tts/dog_ok.mp3
2025-07-29 16:18:19.646 - chat_with_robot - voice.py - detect_callback - line 438 - INFO - [wakeup] 检测到唤醒词
2025-07-29 16:18:19.646 - chat_with_robot - voice.py - end_streaming - line 237 - INFO - [end recording]...
2025-07-29 16:18:19.711 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-29 16:18:19.711 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-29 16:18:19.712 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-07-29 16:18:19.712 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-07-29 16:18:19.712 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-07-29 16:18:19.712 - chat_with_robot - voice.py - start_streaming - line 233 - INFO - [start recording]...
2025-07-29 16:18:19.717 - chat_with_robot - voice.py - run - line 496 - INFO - [run] 持续监听状态...
2025-07-29 16:18:21.638 - chat_with_robot - voice.py - detect_callback - line 438 - INFO - [wakeup] 检测到唤醒词
2025-07-29 16:18:21.638 - chat_with_robot - voice.py - end_streaming - line 237 - INFO - [end recording]...
2025-07-29 16:18:21.700 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-29 16:18:21.700 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-29 16:18:21.701 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-07-29 16:18:21.701 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-07-29 16:18:21.701 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-07-29 16:18:21.701 - chat_with_robot - voice.py - start_streaming - line 233 - INFO - [start recording]...
2025-07-29 16:18:21.760 - chat_with_robot - voice.py - run - line 496 - INFO - [run] 持续监听状态...
2025-07-29 16:18:22.260 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-29 16:18:22.620000
2025-07-29 16:18:22.518 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-29 16:18:22.520 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-29 16:18:22.524 - chat_with_robot - chat_with_robot.py - _task_worker - line 401 - INFO - session_id: 924a1d49-6c54-11f0-9eb5-dc4546c07870; requestId: 76cb52fa-8721-4979-bd6c-693f799561fa_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-29 16:18:22.524 - chat_with_robot - chat_with_robot.py - _task_worker - line 403 - INFO - 等待控制完成
2025-07-29 16:18:22.524 - chat_with_robot - chat_with_robot.py - _task_worker - line 408 - INFO - 等待音频播放完成
2025-07-29 16:18:22.524 - chat_with_robot - chat_with_robot.py - _task_worker - line 418 - INFO - 任务完成，继续
2025-07-29 16:18:25.981 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我想知道回锅肉怎么做, 时间戳: 2025-07-29 16:18:26.343000
2025-07-29 16:18:27.002 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-29 16:18:27.343000
2025-07-29 16:18:27.002 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1000
2025-07-29 16:18:27.017 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:18:27.026 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:18:27.026 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-29 16:18:27.026 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-07-29 16:18:27.027 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-07-29 16:18:27.027 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11448 bytes, 持续时间: 2.0
2025-07-29 16:18:27.027 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-07-29 16:18:27.332 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:18:27.342 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:18:27.658 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:18:27.660 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:18:27.974 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:18:27.976 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:18:28.255 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:18:28.259 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:18:28.548 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:18:28.550 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-29 16:18:28.553 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:18:28.564 - chat_with_robot - chat_with_robot.py - _task_worker - line 401 - INFO - session_id: 924a1d49-6c54-11f0-9eb5-dc4546c07870; requestId: 76cb52fa-8721-4979-bd6c-693f799561fa_joyinside; asr: ，我想知道回锅肉怎么做; 响应时间: 0; JD机器人回复: 哇～你也喜欢做回锅肉呀！我记得妈妈教过我，要先煮五花肉，然后切成薄片～接着锅里放油，滋啦啦地炒香蒜和豆瓣酱，再把肉片倒进去翻炒到卷起来～最后放青蒜和调料，超级香的！你想试试看嘛？
2025-07-29 16:18:28.564 - chat_with_robot - chat_with_robot.py - _task_worker - line 403 - INFO - 等待控制完成
2025-07-29 16:18:28.564 - chat_with_robot - chat_with_robot.py - _task_worker - line 408 - INFO - 等待音频播放完成
2025-07-29 16:18:29.457 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-07-29 16:18:29.457 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-07-29 16:18:29.457 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-29 16:18:29.457 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-07-29 16:18:29.457 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-07-29 16:18:29.458 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15552 bytes, 持续时间: 2.0
2025-07-29 16:18:29.458 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-07-29 16:18:30.347 - chat_with_robot - voice.py - detect_callback - line 438 - INFO - [wakeup] 检测到唤醒词
2025-07-29 16:18:30.347 - chat_with_robot - voice.py - end_streaming - line 237 - INFO - [end recording]...
2025-07-29 16:18:30.412 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-29 16:18:30.412 - chat_with_robot - chat_with_robot.py - _task_worker - line 418 - INFO - 任务完成，继续
2025-07-29 16:18:30.412 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-29 16:18:30.412 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-07-29 16:18:30.412 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-07-29 16:18:30.412 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-07-29 16:18:30.412 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-07-29 16:18:30.413 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-07-29 16:18:30.413 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-07-29 16:18:30.413 - chat_with_robot - voice.py - start_streaming - line 233 - INFO - [start recording]...
2025-07-29 16:18:30.470 - chat_with_robot - voice.py - run - line 496 - INFO - [run] 持续监听状态...
2025-07-29 16:18:31.198 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好，东东, 时间戳: 2025-07-29 16:18:31.558000
2025-07-29 16:18:31.453 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-29 16:18:31.454 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-29 16:18:31.459 - chat_with_robot - chat_with_robot.py - _task_worker - line 401 - INFO - session_id: 924a1d49-6c54-11f0-9eb5-dc4546c07870; requestId: 76cb52fa-8721-4979-bd6c-693f799561fa_joyinside; asr: ，你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-29 16:18:31.459 - chat_with_robot - chat_with_robot.py - _task_worker - line 403 - INFO - 等待控制完成
2025-07-29 16:18:31.459 - chat_with_robot - chat_with_robot.py - _task_worker - line 408 - INFO - 等待音频播放完成
2025-07-29 16:18:31.459 - chat_with_robot - chat_with_robot.py - _task_worker - line 418 - INFO - 任务完成，继续
2025-07-29 16:18:32.748 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退出, 时间戳: 2025-07-29 16:18:33.103000
2025-07-29 16:18:32.759 - chat_with_robot - chat_with_robot.py - play_audio - line 535 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-29 16:18:32.759 - chat_with_robot - chat_with_robot.py - play_audio - line 543 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-07-29 16:18:32.760 - chat_with_robot - chat_with_robot.py - play_audio - line 545 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-07-29 16:18:33.798 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-29 16:18:34.133000
2025-07-29 16:18:33.798 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1030
2025-07-29 16:18:33.805 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:18:34.127 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:18:34.130 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-29 16:18:42.898 - chat_with_robot - voice.py - detect_callback - line 438 - INFO - [wakeup] 检测到唤醒词
2025-07-29 16:18:42.898 - chat_with_robot - voice.py - end_streaming - line 237 - INFO - [end recording]...
2025-07-29 16:18:42.960 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-29 16:18:42.960 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-29 16:18:42.960 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-07-29 16:18:42.960 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-07-29 16:18:42.961 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-07-29 16:18:42.961 - chat_with_robot - voice.py - start_streaming - line 233 - INFO - [start recording]...
2025-07-29 16:18:42.969 - chat_with_robot - voice.py - run - line 496 - INFO - [run] 持续监听状态...
2025-07-29 16:18:45.886 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，退出，退出, 时间戳: 2025-07-29 16:18:46.246000
2025-07-29 16:18:45.896 - chat_with_robot - chat_with_robot.py - play_audio - line 535 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-29 16:18:45.896 - chat_with_robot - chat_with_robot.py - play_audio - line 543 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-07-29 16:18:45.897 - chat_with_robot - chat_with_robot.py - play_audio - line 545 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-07-29 16:18:46.883 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-07-29 16:18:46.884 - chat_with_robot - voice.py - stop - line 433 - INFO - 已停止local_streaming检测器
2025-07-29 16:18:46.969 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-29 16:18:47.302000
2025-07-29 16:18:46.970 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1056
2025-07-29 16:18:46.978 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:18:47.259 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:18:47.259 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
