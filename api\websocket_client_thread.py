import websocket
import threading
import json
import queue
import time
from concurrent.futures import Thread<PERSON>oolExecutor
from typing import Optional, Dict, Any
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from util.logger import logger
import uuid
import base64
import copy
from datetime import datetime

class WebSocketClientThread:
    def __init__(self, url: str, headers: Dict[str, str]):
        self.url = url
        self.headers = headers
        self.ws: Optional[websocket.WebSocketApp] = None
        self.connected = False
        self.send_queue = queue.Queue()
        self.receive_queue = queue.Queue()
        self.executor = ThreadPoolExecutor(max_workers=1)  # 一个发送线程
        self.stop_flag = False
        self.full_audio_base64 = []
        self.llm_text = ""
        self.ws_thread = None
        self.interrupt_flag = False
        self.chat_num = 0

        self.heartbeat_interval = 3  # 30秒发送一次心跳
        self.last_heartbeat_time = time.time()
        self.heartbeat_thread = None

        self.index_num = 0
        self.start_time = time.time()
        self.response_time = 0
        self.receive_tts_time = 0


        self.wakeup_flag = False
        self.wakeup_num = 0

        # 重新定义时间测量变量
        self.asr_start_time = 0        # ASR API调用开始时间
        self.asr_end_time = 0          # 获取ASR文本时间
        self.llm_audio_start_time = 0  # 提供ASR文本给LLM的时间
        self.llm_audio_end_time = 0    # 收到LLM音频的时间
        
        # 是否已获取到对应数据
        self.asr_text_received = False
        self.llm_audio_received = False
        
        # 计时结果
        self.asr_response_time = 0     # ASR响应时间
        self.llm_to_audio_time = 0     # LLM生成音频时间
        self.total_response_time = 0   # 总响应时间
        
        self.summary_printed = False   # 防止重复打印汇总

        self.task_num = 0
        self.asr_text = ""  # 存储ASR文本结果

        self.asr_timestamp = 0

        self.code = 200
        self.dict_message_id = {}
        self.llm_interrupt_flag = False
        self.is_playing = False
    
    def start_heartbeat(self):
        """启动心跳线程"""
        self.heartbeat_thread = threading.Thread(target=self._heartbeat_worker)
        self.heartbeat_thread.daemon = True
        self.heartbeat_thread.start()

    def _heartbeat_worker(self):
        """心跳维护线程"""
        while not self.stop_flag:
            try:
                if self.connected:
                    current_time = time.time()
                    if current_time - self.last_heartbeat_time >= self.heartbeat_interval:
                        # 发送心跳消息
                        # heartbeat_msg = {
                        #     "requestId": str(uuid.uuid1()),
                        #     "contentType": "PING",
                        #     "msgId": str(uuid.uuid1()),
                        #     "needAudio": True,
                        #     "userId": "qixiaolei",
                        # }
                        heartbeat_msg = {
                            "mid": str(uuid.uuid1()),
                            "contentType": "PING",
                            "uid": str(uuid.uuid1()),
                            "content": {},
                        }
                        if self.ws:
                            self.ws.send(json.dumps(heartbeat_msg))
                            logger.debug("发送心跳")
                            self.last_heartbeat_time = current_time
                time.sleep(1)  # 检查间隔
            except Exception as e:
                logger.error(f"心跳发送失败: {e}")
                time.sleep(5)  # 发生错误时等待longer

    def connect(self) -> bool:
        """建立WebSocket连接"""
        try:
            if not self.connected:
                self._cleanup()
                # 创建WebSocket连接
                self.ws = websocket.WebSocketApp(
                    self.url,
                    header=self.headers,
                    on_message=self._on_message,
                    on_error=self._on_error,
                    on_close=self._on_close,
                    on_open=self._on_open,
                )
                #print("创建连接")
                # 启动WebSocket连接线程
                self.ws_thread = threading.Thread(target=self.ws.run_forever)
                self.ws_thread.daemon = True
                self.ws_thread.start()

                # 启动发送消息的线程
                self.send_thread = self.executor.submit(self._send_worker)
                
                # 等待连接建立
                timeout = 5
                start_time = time.time()
                while not self.connected and time.time() - start_time < timeout:
                    time.sleep(0.1)
                # 启动心跳
                self.start_heartbeat()
                return self.connected
        except Exception as e:
            logger.error(f"WebSocket连接失败: {e}")
            return False

    def _on_message(self, ws, message):
        """处理接收到的消息"""
        try:
            response = json.loads(message)

            # if self.task_num > 1:
            # logger.info(f"收到消息: {response}")

            requestId = response.get("requestId", "")

            #print(f"收到消息: {response}")
            #logger.info(f"收到消息: {response}")
            # 处理响应
            try:
                code = response.get("code")
                self.code = code
                if code is not None and int(code) != 200:
                    self.index_num = 0
                    self.wakeup_flag = False
                    self.is_playing = False
                    logger.warning(f"响应code不是200: code={code}")

                    # if int(code) == 50001:
                    #     self.receive_queue.put((2, None, None, None, None, "ASRError.wav", requestId))
                    # elif int(code) == 50010:
                    #     self.receive_queue.put((2, None, None, None, None, "ConversationError.wav", requestId))
                    return
            except ValueError as e:
                logger.error(f"code值异常: {code}")
                return
            
            content = response.get("content", "")
            #requestId = response.get("requestId", "")
            timestamp = response.get("t", "")
            if content:
                text = content.get("content", "")
                audioBase64 = content.get("audioBase64", "")
                finish = content.get("finish", False) ### 代表一句话结束
                audioDuration = content.get("audioDuration", 0)
                audioAue = content.get("audioAue", "")
                eventType = content.get("eventType", "")
                eventData = content.get("eventData", "")



                if eventType == "EMPTY_CONTENT":
                    logger.info("message response: EMPTY_CONTENT")
                    self.wakeup_flag = False
                    #self.receive_queue.put((2, None, None, None, None, "EmptyContent.wav", requestId))
                    return

                if eventType == "CALL_AGENT_INTERRUPTED":
                    logger.info("message response: CALL_AGENT_INTERRUPTED")
                    self.llm_interrupt_flag = True
                    #return


                if eventType == "COMPLETE":
                    logger.info("message response: COMPLETE")
                    #self.receive_queue.put((None, None, None, None, None, requestId))
                    self.receive_queue.put((None, None, None, None, None, None, requestId))
                    self.full_audio_base64 = []
                    self.llm_text = ""
                    self.index_num = 0
                    # 重置时间测量变量
                    self.asr_text_received = False
                    self.llm_audio_received = False
                    self.summary_printed = False
                    self.wakeup_flag = False
                    self.is_playing = False
                    return
                

                
                
                # 处理ASR文本结果
                asr_text = content.get("text", "")
                text_type = content.get("textType", "")

                if text_type == 'NON_SPEECH':
                    self.wakeup_flag = False
                    logger.info("message response: NON_SPEECH")
                    #self.receive_queue.put((2, None, None, None, None, "InvalidAudio.wav", requestId))
                    return

                # if self.wakeup_flag and self.wakeup_num > 1: #  and self.task_num > 0 and self.is_playing
                #     logger.info("唤醒打断，不处理message response")
                #     return
                

                if text_type == "IS_FINAL" and len(asr_text.strip()) > 0:
                    if timestamp:
                        # 转换为datetime对象
                        dt = datetime.fromtimestamp(timestamp/1000)
                        # 格式化输出
                        formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S.%f')
                        self.asr_timestamp = timestamp
                        logger.info(f"收到ASR文本: {asr_text}, 时间戳: {formatted_time}")
                    self.asr_text = asr_text

                    self.dict_message_id[requestId+"-asr_text"] = asr_text
                    self.asr_text_received = True
                    self.asr_response_time = time.time() - self.asr_start_time
                    
                    # 设置LLM处理开始时间
                    self.llm_audio_start_time = time.time()

                    #self.wakeup_flag = False
                    
                    
                    #self.receive_queue.put((0, asr_text, None, None, None, requestId))

                    self.receive_queue.put((0, asr_text, None, None, None, None, requestId))
                elif text_type == "IS_FINAL" and len(asr_text.strip()) == 0:
                    logger.info("收到空ASR文本，不处理")
                    
                    self.wakeup_flag = False
                    return
                
                
                # 处理LLM文本响应
                if text:
                    self.llm_text += text
                
                # 处理LLM音频响应
                if audioBase64:

                    if self.index_num == 0:
                        if timestamp:
                            # 转换为datetime对象
                            dt = datetime.fromtimestamp(timestamp/1000)
                            # 格式化输出
                            formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S.%f')
                            logger.info(f"收到首个TTS数据包, 时间戳: {formatted_time}")
                            #print(("收到首个TTS数据包"))
                            logger.info(f"从ASR-TTS返回, 时间戳: {timestamp-self.asr_timestamp}")
                        self.task_num += 1
                        self.is_playing = True

                    self.index_num +=1

                    self.full_audio_base64.append(audioBase64)

                
                if finish:
                    if self.chat_num == 1:
                        self.interrupt_flag = True
                    
                    # if self.index_num == 0:
                    #     print(f"---------- [TTS] 响应完成(收到最后数据包), index_num: {self.index_num} ----------")
                        # self.response_time = time.time() - self.start_time
                        # self.dict_message_id[requestId+"-response_time"] = time.time() - self.dict_message_id[requestId+"-start_time"]
                        
                    audio_data = b""
                    for audio_base64 in self.full_audio_base64:
                        audio_data += base64.b64decode(audio_base64)


                    #print("---------- 收到数据包 ----------")
                    #self.receive_tts_time = time.time()
                    #self.receive_queue.put((1, self.asr_text, self.llm_text, audio_data, audioDuration, requestId))
                    logger.info("收到TTS数据包，放入队列")

                    self.receive_queue.put((1, self.asr_text, self.llm_text, audio_data, audioDuration, None, requestId))
                    self.full_audio_base64 = []
                    self.llm_text = ""
                    
                
               
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
        except Exception as e:
            logger.error(f"处理消息出错: {e}")

    def _on_error(self, ws, error):
        """处理错误"""
        logger.error(f"WebSocket错误: {error}")
        self.connected = False

    def _on_close(self, ws, close_status_code, close_msg):
        """处理连接关闭"""
        logger.info("WebSocket连接关闭")
        self.connected = False
        self.connect()

    def _on_open(self, ws):
        """处理连接建立"""
        logger.info("WebSocket连接建立")
        self.connected = True

    def _send_worker(self):
        """发送消息的工作线程"""
        while not self.stop_flag:
            try:
                if not self.connected:
                    time.sleep(0.1)
                    continue

                # 从队列获取消息
                try:
                    message = self.send_queue.get(timeout=0.1)
                except queue.Empty:
                    continue

                # 发送消息
                if self.ws and self.connected:
                    self.ws.send(json.dumps(message))
                    #print(f"发送消息成功: {message}")
                self.send_queue.task_done()

            except Exception as e:
                logger.error(f"发送消息失败: {e}")
                time.sleep(1)

    def send_message(self, message: Dict[str, Any]) -> bool:
        """发送消息"""
        try:
            if not self.connected:
                logger.error("WebSocket未连接")
                return False
            
            # 重置计时器和状态
            self.asr_start_time = time.time()  # 假设这是开始调用ASR API的时间
            #self.start_time = time.time()      # 兼容旧代码
            
            # 重置所有时间变量
            self.asr_end_time = 0
            self.llm_audio_start_time = 0
            self.llm_audio_end_time = 0
            self.asr_text_received = False
            self.llm_audio_received = False
            self.asr_response_time = 0
            self.llm_to_audio_time = 0
            self.total_response_time = 0
            
            
            self.send_queue.put(message)
            return True
        except Exception as e:
            logger.error(f"消息放入队列失败: {e}")
            return False
    
    def receive_message(self):
        """接收消息"""
        return self.receive_queue.get_nowait()
    

    def stop(self):
        """停止WebSocket客户端"""
        self.stop_flag = True
        if self.ws:
            self.ws.close()
        self.executor.shutdown(wait=True)
        if self.ws_thread:
            self.ws_thread.join()
        if self.heartbeat_thread:
            self.heartbeat_thread.join()
        logger.info("WebSocket客户端已停止")
    
    def _cleanup(self):
        """清理现有连接和线程"""
        logger.info("开始清理旧连接...")
        self.stop_flag = True  # 停止发送线程
        
        # 关闭现有WebSocket连接
        if self.ws:
            self.ws.close()
            self.ws = None
            
        # 等待WebSocket线程结束
        if self.ws_thread and self.ws_thread.is_alive():
            self.ws_thread.join(timeout=2)
        
        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            self.heartbeat_thread.join(timeout=2)
            
        # 关闭并重新创建线程池
        if self.executor:
            self.executor.shutdown(wait=True)
            self.executor = ThreadPoolExecutor(max_workers=1)
            
        self.connected = False
        self.stop_flag = False
        logger.info("清理完成")






##############测试用########################
def split_wav_file(file_path, chunk_size):
    try:
        # 打开 WAV 文件并读取内容
        with open(file_path, "rb") as file:
            wav_data = file.read()

        # 计算总字节数
        total_bytes = len(wav_data)
        print(f"Total bytes: {total_bytes}")

        # 按指定字节大小切分文件内容
        chunks = [wav_data[i:i + chunk_size] for i in range(0, total_bytes, chunk_size)]

        print(f"Total chunks: {len(chunks)}")

        # 将每个切分块转换为 Base64 编码的字符串
        base64_chunks = [base64.b64encode(chunk).decode("utf-8") for chunk in chunks]

        return base64_chunks
    except Exception as e:
        print(f"Error: {e}")
        return []


# 使用示例
if __name__ == "__main__":
    # WebSocket连接配置

    #ws_url = "ws://topen-soa.jdfmgt.com/topen/v1/chat/voiceCall?chatId=zhangjinqi9-20250403-1&agentId=b282f73fec1d4379adcdbd793cac6220&userId=zhangjinqi9&needAudio=true"  # 服务器的 WebSocket 地址
    ws_url = "wss://joyinside.jd.com/soulmate/voiceCall/v4?chatId=zhangjinqi9-20250403-1&agentId=b282f73fec1d4379adcdbd793cac6220&userId=zhangjinqi9&requestId="+str(uuid.uuid4()) 
    headers = {"topen-tenant-code": "jcjs", "topen-business-code": "JCPG"}
    # 创建WebSocket客户端
    client = WebSocketClientThread(ws_url, headers)

    try:
        # 建立连接
        print("建立连接")
        if client.connect():
            print("连接成功")

            msg_id = str(uuid.uuid4())
            list = split_wav_file('/home/<USER>/HardwareAIAgent_deploy_v4/asserts/a107cffad36b1c3e3593e54b42f4b63765.wav', 6400)
            
            for i in range(len(list)):
                is_final = 1 if i == len(list) - 1 else 0
                base64_data = list[i]
                index = i + 1
                message = {
                    "requestId": str(uuid.uuid4()),
                    "contentType": "AUDIO",
                    "content": {
                        "msgId": msg_id,
                        "sendStatus": is_final,
                        "audioBase64": base64_data,
                        "index": index
                    }
                }
                print(f"发送消息: {message}")
                client.send_message(message)
            client.start_time = time.time()
            # 保持程序运行
            while True:
                time.sleep(1)
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    finally:
        client.stop()
