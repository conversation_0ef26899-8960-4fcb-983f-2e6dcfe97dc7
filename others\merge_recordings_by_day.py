import os
import glob
import re
import argparse
from datetime import datetime
import wave
import numpy as np
from collections import defaultdict

def get_timestamp_from_filename(filename):
    """从文件名中提取时间戳"""
    match = re.search(r'recording_(\d{8}_\d{6}_\d+)\.wav', os.path.basename(filename))
    if match:
        return match.group(1)
    return os.path.basename(filename)  # 如果无法提取则返回文件名

def get_date_from_filename(filename):
    """从文件名中提取日期"""
    match = re.search(r'recording_(\d{8})_', os.path.basename(filename))
    if match:
        return match.group(1)
    return None

def merge_wav_files_by_date(input_dir, output_dir):
    """按日期合并WAV文件"""
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs("others", exist_ok=True)
    
    # 查找所有WAV文件
    wav_files = glob.glob(os.path.join(input_dir, '*.wav'))
    
    if not wav_files:
        print(f"在 {input_dir} 中没有找到WAV文件")
        return False
    
    # 按日期分组文件
    files_by_date = defaultdict(list)
    for wav_file in wav_files:
        date = get_date_from_filename(wav_file)
        if date:
            files_by_date[date].append(wav_file)
    
    print(f"找到 {len(wav_files)} 个WAV文件，分为 {len(files_by_date)} 个日期组")
    
    # 记录成功合并的文件
    successful_files = []
    
    # 按日期分别处理
    sorted_dates = sorted(files_by_date.keys())
    for i, date in enumerate(sorted_dates):
        date_files = files_by_date[date]
        output_file = os.path.join(output_dir, f"merged_{date}.wav")
        print(f"\n正在处理 {date} 的 {len(date_files)} 个文件...")
        
        # 按时间戳排序
        date_files.sort(key=get_timestamp_from_filename)
        
        # 打开第一个文件获取参数 (已经是按日期获取参数)
        try:
            with wave.open(date_files[0], 'rb') as first_wav:
                params = first_wav.getparams()
        except Exception as e:
            print(f"错误: 无法打开第一个文件 {date_files[0]}: {str(e)}")
            continue
            
        # 创建输出文件
        try:
            with wave.open(output_file, 'wb') as output_wav:
                output_wav.setparams(params)
                
                # 处理当天的每个文件
                this_day_successful = []
                for wav_file in date_files:
                    try:
                        print(f"处理: {os.path.basename(wav_file)}")
                        with wave.open(wav_file, 'rb') as input_wav:
                            # 确保文件格式兼容
                            if input_wav.getparams() != params:
                                print(f"警告: {wav_file} 参数与目标不匹配，跳过")
                                continue
                            
                            # 读取音频数据
                            data = input_wav.readframes(input_wav.getnframes())
                            # 写入输出文件
                            output_wav.writeframes(data)
                            
                            # 添加到成功列表
                            this_day_successful.append(wav_file)
                    except Exception as e:
                        print(f"错误: 处理 {wav_file} 失败: {str(e)}")
                
                # 将当天成功的文件添加到总列表
                successful_files.extend(this_day_successful)
                
            print(f"完成 {date} 的合并，已保存到: {output_file}")
            print(f"该日期成功合并了 {len(this_day_successful)}/{len(date_files)} 个文件")
        except Exception as e:
            print(f"错误: 合并 {date} 的文件失败: {str(e)}")
        
        # 询问用户是否继续处理下一天
        if i < len(sorted_dates) - 1:
            next_date = sorted_dates[i + 1]
            print(f"\n已完成 {date} 的合并，下一个日期是 {next_date}")
            response = input("是否继续处理下一天？(y/n): ")
            if response.lower() not in ['y', 'yes']:
                print("用户中断，停止处理后续日期")
                break
    
    # 记录成功合并的文件（按日期组织）
    # success_log_path = os.path.join("others", "successful_merges.txt")
    current_date = datetime.now().strftime('%Y%m%d')
    success_log_path = os.path.join("others", f"successful_merges_{current_date}.txt")
    with open(success_log_path, 'w') as f:
        f.write(f"合并时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总共处理: {len(wav_files)} 个文件\n")
        f.write(f"成功合并: {len(successful_files)} 个文件\n\n")
        
        # 按日期组织成功文件
        successful_by_date = defaultdict(list)
        for file in successful_files:
            date = get_date_from_filename(file)
            if date:
                successful_by_date[date].append(file)
        
        # 写入按日期组织的文件列表
        f.write("成功合并的文件列表（按日期）:\n")
        for date in sorted(successful_by_date.keys()):
            f.write(f"\n日期: {date}\n")
            f.write(f"该日期成功合并文件数: {len(successful_by_date[date])}\n")
            for file in successful_by_date[date]:
                f.write(f"  - {file}\n")
    
    print(f"\n合并完成，成功合并了 {len(successful_files)}/{len(wav_files)} 个文件")
    print(f"成功合并的文件列表已保存至: {success_log_path}")
    return True

def main():
    parser = argparse.ArgumentParser(description='合并WAV录音文件')
    parser.add_argument('--input', '-i', default='recordings', 
                        help='包含WAV文件的目录路径 (默认: recordings)')
    parser.add_argument('--output-dir', '-o', default='merged', 
                        help='输出目录路径 (默认: merged)')
    
    args = parser.parse_args()
    
    # 确保输入目录存在
    if not os.path.isdir(args.input):
        print(f"错误: 目录 {args.input} 不存在")
        return 1
    
    # 合并文件
    success = merge_wav_files_by_date(args.input, args.output_dir)
    
    return 0 if success else 1

if __name__ == '__main__':
    exit(main())