#!/usr/bin/env python3
"""
测试KWS系统
"""
import sys
import os
import time
import argparse
import numpy as np
from asr.kws_wrapper import KWSDetector
import pyaudio
import wave
import threading

def record_audio(detector):
    """从麦克风录制音频并发送到检测器"""
    p = pyaudio.PyAudio()
    stream = p.open(
        format=pyaudio.paInt16,
        channels=1,
        rate=16000,
        input=True,
        frames_per_buffer=2048
    )
    
    print("开始录音，按Ctrl+C停止...")
    try:
        while True:
            audio_data = stream.read(2048, exception_on_overflow=False)
            detector.add_audio(audio_data)
            
            # 添加音量监测
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            volume = np.abs(audio_array).mean()
            if volume > 500:  # 调整阈值
                print(f"当前音量: {volume}")
                
            time.sleep(0.001)  # 小睡避免CPU过载
    except KeyboardInterrupt:
        print("停止录音")
    except Exception as e:
        print(f"录音过程中发生错误: {e}")
    finally:
        stream.stop_stream()
        stream.close()
        p.terminate()

def on_wakeup():
    """唤醒回调函数"""
    print("检测到唤醒词！")
    # 播放提示音
    try:
        import subprocess
        temp_file = '/home/<USER>/Possessed_AI/asserts/ding.wav'
        if os.path.exists(temp_file):
            subprocess.run(['ffplay', '-nodisp', '-autoexit', '-i', temp_file], 
                         stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL)
        else:
            print(f"警告: 提示音文件不存在: {temp_file}")
    except Exception as e:
        print(f"播放提示音失败: {e}")

def test_from_file(args):
    """从文件测试KWS系统"""
    print("\n==== 从文件测试KWS ====")
    
    # 获取文件的绝对路径
    if not os.path.isabs(args.file):
        abs_file_path = os.path.abspath(args.file)
    else:
        abs_file_path = args.file
    
    print(f"测试文件绝对路径: {abs_file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(abs_file_path):
        print(f"错误: 文件不存在: {abs_file_path}")
        return
    
    # 创建检测器
    try:
        detector = KWSDetector()
        print("成功创建KWS检测器")
        
        # 执行检测
        print(f"开始检测文件: {abs_file_path}")
        result = detector.detect_from_file(abs_file_path)
        
        if result:
            print("从文件中检测到唤醒词！")
            on_wakeup()
        else:
            print("未检测到唤醒词。")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

def test_from_mic():
    """从麦克风测试KWS系统"""
    print("\n==== 从麦克风测试KWS ====")
    
    try:
        detector = KWSDetector()
        print("成功创建KWS检测器")
        
        detector.set_detection_callback(on_wakeup)
        detector.start()
        
        # 启动录音线程
        record_thread = threading.Thread(target=record_audio, args=(detector,))
        record_thread.daemon = True
        record_thread.start()
        
        print("录音线程已启动，等待唤醒词...")
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("程序退出")
        finally:
            detector.stop()
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="测试KWS系统")
    parser.add_argument("--file", "-f", help="从音频文件测试，不指定则从麦克风录制")
    
    args = parser.parse_args()
    
    # 显示当前工作目录
    print(f"当前工作目录: {os.getcwd()}")
    
    if args.file:
        test_from_file(args)
    else:
        test_from_mic() 