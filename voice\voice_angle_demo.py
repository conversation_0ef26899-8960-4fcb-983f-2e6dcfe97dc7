import serial
import time
import binascii

# 串口配置
PORT = '/dev/ttyUSB0'  # 根据实际情况修改
BAUDRATE = 1000000        # 根据设备设置

number_to_angle = {
    0: 180,
    1: 210,
    2: 240,
    3: 270,
    4: 300,
    5: 330,
    6: 0,
    7: 30,
    8: 60,
    9: 90,
    10: 120,
    11: 150
}

def read_from_serial(port, baudrate):
    try:
        ser = serial.Serial(
            port=port,
            baudrate=baudrate,
            bytesize=serial.EIGHTBITS,    # 数据位
            parity=serial.PARITY_NONE,    # 无奇偶校验
            stopbits=serial.STOPBITS_ONE, # 停止位
            timeout=1
        )
        print(f"已连接到串口：{port}，波特率：{baudrate}")

        while True:
            if ser.in_waiting:
                data = ser.readline()
                print(f"接收到的数据：{data}")
                # 尝试使用不同的编码解码
                for encoding in ['utf-8', 'latin1', 'gbk']:
                    try:
                        data_str = data.decode(encoding).strip()
                        # print(f"接收到的数据（编码 {encoding}）：{data_str}")
                        
                        # 如果解码成功，则跳出循环
                        break
                    except UnicodeDecodeError:
                        continue
                
                # 在这里可以对接收到的数据进行解析
                parse_data(data_str)
            else:
                time.sleep(0.1)
    except serial.SerialException as e:
        print(f"串口错误：{e}")
    except KeyboardInterrupt:
        print("程序已停止")
    finally:
        if 'ser' in locals() and ser.is_open:
            ser.close()
            print("串口已关闭")

def parse_data(data_str):
    # 判断数据是否为我们需要的格式
    if "Go-CMD read PASS with Addr=" in data_str:
        try:
            # 提取地址和数据
            parts = data_str.split('Addr=')[1].split(', Data=')
            addr_str = parts[0]
            data_value_str = parts[1]
            addr = int(addr_str, 16)
            data_value = int(data_value_str, 16)
            data_value_hex_str = f"{data_value:08X}"
            last_hex_digit = data_value_hex_str[-1]

            # 将十六进制字符转换为十进制数字
            last_digit_decimal = int(last_hex_digit, 16)
            direction = number_to_angle.get(last_digit_decimal, None)
            print(f"方向={direction}")
            # 在这里可以对提取的数据进行进一步处理
        except (IndexError, ValueError) as e:
            print(f"解析数据时出现错误：{e}")


if __name__ == '__main__':
    read_from_serial(PORT, BAUDRATE)