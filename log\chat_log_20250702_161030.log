2025-07-02 16:10:31.598 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-07-02 16:10:31.598 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-02 16:10:31.616 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751443831616&accessNonce=04b84e15-33b7-4f97-a952-f6d311059c23&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=05c7c055-571c-11f0-b0d8-dc4546c07870&requestId=bb9cce7c-c79a-4f6b-9905-35abe1541308_joyinside&accessSign=7641aeb72bf93fbc94f96ea9d275a7c8, request_id: bb9cce7c-c79a-4f6b-9905-35abe1541308_joyinside
2025-07-02 16:10:31.618 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-02 16:10:31.618 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-02 16:10:31.997 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-02 16:10:32.300 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-07-02 16:10:33.687 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-02 16:10:48.903 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-07-02 16:10:48.903 - chat_with_robot - voice.py - stop - line 401 - INFO - 已停止local_streaming检测器
