# 状态码常量定义
FSM_PASSIVE = 0       # 被动状态
FSM_PURE_DAMPER = 1   # 电机阻尼
FSM_RECOVERY_STAND = 2 # 恢复站立
FSM_BALANCE_STAND = 3  # 平衡站立
FSM_TROT = 4          # 对脚小跑
FSM_LOW_LEVEL_CTRL = 5 # 电机控制模式
FSM_BROAD_JUMP = 6    # 原地跳远
FSM_TWO_LEG_STAND = 7  # 双腿站立
FSM_BACK_FLIP = 8     # 后空翻
FSM_SHAKE_HAND = 9    # 握左手
FSM_WIGGLE_HIP = 10   # 扭屁股
FSM_SIT_DOWN = 11     # 坐下
FSM_PEE = 12          # 撒尿
FSM_SHAKE_BODY = 13   # 摇晃身体
FSM_STRETCH_LEG = 14  # 伸展腿
FSM_SWAG_HIP = 15     # 摇屁股
FSM_SHAKE_LEFT_HAND =16 # 握左手
FSM_PUSH_UP = 17      # 俯卧撑
FSM_FIGHTING = 18     # 打拳
FSM_KICK = 19         # 击掌
FSM_TICKLING = 20     # 挠痒痒
FSM_TWO_LEG_UPDOWN = 21 # 双腿站立拜年
FSM_TWO_LEG_BOXING = 22 # 双腿站立打拳
FSM_SPIN_JUMP_LEFT = 23 # 旋转左跳
FSM_SPIN_JUMP_RIGHT =24 # 旋转右跳
FSM_JUMP_HIGH = 25    # 跳高
FSM_JUMP_FRONT = 26   # 前跳
FSM_FLIP_SIDE_POS = 27 # 向右侧空翻
FSM_FLIP_SIDE_NEG = 28 # 向左侧空翻

# 状态名称映射（用于日志输出）
STATE_NAMES = {
    FSM_PASSIVE: "Passive",
    FSM_PURE_DAMPER: "PureDamper",
    FSM_RECOVERY_STAND: "RecoveryStand",
    FSM_BALANCE_STAND: "BalanceStand",
    FSM_TROT: "Trot",
    FSM_LOW_LEVEL_CTRL: "LowLevelControl",
    FSM_BROAD_JUMP: "BroadJump",
    FSM_TWO_LEG_STAND: "TwoLegStand",
    FSM_BACK_FLIP: "BackFlip",
    FSM_SHAKE_HAND: "ShakeHand",
    FSM_WIGGLE_HIP: "WiggleHip",
    FSM_SIT_DOWN: "SitDown",
    FSM_PEE: "Pee",
    FSM_SHAKE_BODY: "ShakeBody",
    FSM_STRETCH_LEG: "StretchLeg",
    FSM_SWAG_HIP: "SwagHip",
    FSM_SHAKE_LEFT_HAND: "ShakeLeftHand",
    FSM_PUSH_UP: "PushUp",
    FSM_FIGHTING: "Fighting",
    FSM_KICK: "Kick",
    FSM_TICKLING: "Tickling",
    FSM_TWO_LEG_UPDOWN: "TwoLegUpdown",
    FSM_TWO_LEG_BOXING: "TwoLegBoxing",
    FSM_SPIN_JUMP_LEFT: "SpinJumpLeft",
    FSM_SPIN_JUMP_RIGHT: "SpinJumpRight",
    FSM_JUMP_HIGH: "JumpHigh",
    FSM_JUMP_FRONT: "JumpFront",
    FSM_FLIP_SIDE_POS: "FlipSidePositive",
    FSM_FLIP_SIDE_NEG: "FlipSideNegative"
}

# 状态转移规则定义（源状态: 允许的目标状态列表）
STATE_TRANSITIONS = {
    FSM_PASSIVE: [FSM_RECOVERY_STAND, FSM_LOW_LEVEL_CTRL],
    FSM_PURE_DAMPER: [FSM_PASSIVE],
    FSM_RECOVERY_STAND: list(range(0, 29)),  # 允许转移到所有状态
    FSM_BALANCE_STAND: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_TROT] + list(range(7, 29)),
    FSM_TROT: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_BALANCE_STAND],
    FSM_LOW_LEVEL_CTRL: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND],
    FSM_BROAD_JUMP: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_TWO_LEG_STAND: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_BACK_FLIP: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_SHAKE_HAND: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_WIGGLE_HIP: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_SIT_DOWN: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_PEE: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_SHAKE_BODY: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_STRETCH_LEG: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_SWAG_HIP: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_SHAKE_LEFT_HAND: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_PUSH_UP: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_FIGHTING: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_KICK: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_TICKLING: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_TWO_LEG_UPDOWN: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_TWO_LEG_BOXING: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_SPIN_JUMP_LEFT: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_SPIN_JUMP_RIGHT: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_JUMP_HIGH: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_JUMP_FRONT: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_FLIP_SIDE_POS: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND],
    FSM_FLIP_SIDE_NEG: [FSM_PASSIVE, FSM_PURE_DAMPER, FSM_RECOVERY_STAND, FSM_BALANCE_STAND]
}