import pyaudio
audio = pyaudio.PyAudio()

# 列出所有音频设备
# 打印出所有的输入设备名字
input_devices = []
for i in range(audio.get_device_count()):
    device_info = audio.get_device_info_by_index(i)
    if device_info.get('maxInputChannels') > 0:  # 只显示输入设备
        input_devices.append((i, device_info))
        device_name = device_info.get('name')
        print(f"{len(input_devices)-1}) {device_name}")

        # 打印详细信息
        # print(f"\n设备 {i}:")
        # print(f"    名称: {device_name}")
        # print(f"    输入通道数: {channels}")
        # print(f"    默认采样率: {sample_rate}Hz")
        # print(f"    设备索引: {i}")