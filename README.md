# 1. 安装环境

```bash

##方式1：一键式安装库
pip install -r requirements.txt

##方式2：关键库

sudo apt-get update
sudo apt install tmux
sudo apt-get install python3-dev
sudo apt-get install portaudio19-dev
sudo apt-get install python3-pyaudio
sudo apt-get install lib  asound-dev
sudo apt-get install libportaudio2
sudo apt-get install libportaudiocpp0
sudo apt-get install ffmpeg
sudo apt-get install libpulse-dev
sudo apt-get install build-essential
sudo apt-get install sox libsox-fmt-all
sudo apt-get install unzip
# 安装ros https://zhuanlan.zhihu.com/p/29818376176
sudo sh -c '. /etc/lsb-release && echo "deb http://mirrors.tuna.tsinghua.edu.cn/ros/ubuntu/ `lsb_release -cs` main" > /etc/apt/sources.list.d/ros-latest.list'
sudo apt-key adv --keyserver 'hkp://keyserver.ubuntu.com:80' --recv-key C1CF6E31E6BADE8868B172B4F42ED6FBAB17C654
sudo apt install ros-noetic-ros-base ros-noetic-std-msgs
source /opt/ros/noetic/setup.bash
echo "source /opt/ros/noetic/setup.bash" >> ~/.bashrc
source ~/.bashrc

# 库目录添加
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:$HOME/Possessed_AI/aec/lib
sudo apt install python3-rospkg
pip install rospkg catkin_pkg


pip install pyaudio -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install pygame -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install openai -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install sherpa_onnx -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install numpy -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install simpleaudio -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install pydub -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install sounddevice -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install requests -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install websocket-client -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install serial -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install ~/Possessed_AI/kws/kws_v5_demo/sherpa_onnx-1.11.3-cp38-cp38-linux_aarch64.whl
```

# 2. 音频输出设置

让系统的默认输出设备为你想要的输出设备。

# 3. 音频输入设置

找到系统的输入设备号，然后设置为你的输入设备。

**打印出所有的输入设备：**
```bash
python3 others/pyaudio_test.py
```

在 `voice/voice.py` 文件中，添加你的输入设备的名字：

```python
def _select_input_device_auto(self) -> int:
    """持续检测直到找到目标设备"""
    target_device_name = ["mic", "CP900: USB Audio", "spdif", "dsnooper", "speaker", "你的输入设备名字"]
```

# 4. 运行测试脚本

```bash
python chat_with_robot.py --kws local_streaming --use_action dont --echo_cancel True
```
```