"""
统一音频控制器 - 合并 audio_player.py 和 audio_action_controller.py 的功能
支持音频播放、队列管理、动作控制（嘴巴、脖子、眼睛）
"""

import sys
import threading
import os
import time
import random
import pygame
import yaml
import queue
import io
import uuid
import tempfile
from loguru import logger

# 可选导入 pymodbus，如果没有安装则禁用 Modbus 功能
try:
    from pymodbus.client import ModbusTcpClient
    MODBUS_AVAILABLE = True
except ImportError:
    logger.warning("pymodbus 未安装，Modbus 控制功能将被禁用")
    ModbusTcpClient = None
    MODBUS_AVAILABLE = False


class UnifiedAudioController:
    """统一音频控制器 - 集成音频播放和动作控制"""
    
    def __init__(self, modbus_ip="************", modbus_port=502):
        # 音频播放相关
        self.audio_queue = queue.Queue()
        self.is_playing = False
        self.play_thread = None
        self.stop_event = threading.Event()
        self.tasks_completed = threading.Event()
        self.tasks_completed.set()
        self.interrupt_flag = False
        
        # 初始化pygame
        pygame.mixer.init(frequency=44100, size=-16, channels=2, buffer=2048)
        logger.info("音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048")
        
        # Modbus 连接
        self.client = None
        self.modbus_ip = modbus_ip
        self.modbus_port = modbus_port
        self._init_modbus()
        
        # 动作控制参数
        self._init_action_params()
        
    def _init_modbus(self):
        """初始化 Modbus 连接"""
        if MODBUS_AVAILABLE:
            try:
                self.client = ModbusTcpClient(self.modbus_ip, port=self.modbus_port)
                connection_result = self.client.connect()
                if connection_result:
                    logger.info(f"Modbus 客户端连接成功: {self.modbus_ip}:{self.modbus_port}")
                else:
                    logger.error(f"Modbus 客户端连接失败: {self.modbus_ip}:{self.modbus_port}")
                    self.client = None
            except Exception as e:
                logger.error(f"Modbus 客户端连接异常: {e}")
                self.client = None
        else:
            logger.info("Modbus 功能已禁用（pymodbus 未安装）")
    
    def _init_action_params(self):
        """初始化动作控制参数"""
        # 嘴巴控制参数
        self.mouth_mode = '1031-2'
        self.mouth_angle_open = '1032-100'
        self.mouth_angle_close = '1032-0'
        self.mouth_speed = '1033-100'  # 与audio_action_controller.py保持一致
        
        # 脖子控制参数（与audio_action_controller.py保持一致）
        self.neck_reg = '1034-0'
        self.neck_reg1 = '1034-100'
        self.neck_reg_speed = '1035-60'
        
        # 眼睛控制参数
        self.eyes_reg = '7999'
    
    def start(self):
        """启动音频播放线程"""
        if not self.is_playing:
            self.is_playing = True
            self.stop_event.clear()
            self.tasks_completed.clear()
            self.play_thread = threading.Thread(target=self._play_loop, daemon=True)
            self.play_thread.start()
            logger.info("统一音频控制器已启动")
    
    def stop(self):
        """停止音频播放线程"""
        self.is_playing = False
        self.stop_event.set()
        self.interrupt()
        if self.play_thread:
            self.play_thread.join()
        logger.info("统一音频控制器已停止")
    
    def interrupt(self):
        """打断当前播放并清空队列"""
        logger.info("收到音频打断信号")
        self.interrupt_flag = True
        
        # 停止当前播放
        try:
            pygame.mixer.stop()
            logger.info("已停止当前音频播放")
        except:
            pass
        
        # 清空队列
        try:
            while not self.audio_queue.empty():
                self.audio_queue.get_nowait()
            logger.info("已清空音频播放队列")
        except:
            pass
        
        self.tasks_completed.set()
    
    def add_audio_file(self, audio_path, duration=None):
        """添加音频文件到播放队列"""
        self.tasks_completed.clear()
        self.audio_queue.put(('file', audio_path, duration))
        logger.info(f"音频文件已添加到队列: {audio_path}")
    
    def add_audio_data(self, audio_data, duration=None):
        """添加音频数据到播放队列"""
        self.tasks_completed.clear()
        self.audio_queue.put(('data', audio_data, duration))
        logger.info("音频数据已添加到队列")
    
    def wait_for_completion(self, timeout=None):
        """等待所有任务完成"""
        return self.tasks_completed.wait(timeout=timeout)
    
    def _play_loop(self):
        """播放循环"""
        while self.is_playing and not self.stop_event.is_set():
            try:
                # 从队列获取音频任务
                audio_task = self.audio_queue.get(timeout=1.0)
                if audio_task is None:
                    break
                
                audio_type, audio_content, duration = audio_task
                
                # 重置打断标志
                self.interrupt_flag = False
                
                if audio_type == 'file':
                    self._play_audio_file(audio_content, duration)
                elif audio_type == 'data':
                    self._play_audio_data(audio_content, duration)
                
                # 检查队列是否为空
                if self.audio_queue.empty():
                    self.tasks_completed.set()
                    
            except queue.Empty:
                self.tasks_completed.set()
                continue
            except Exception as e:
                logger.error(f"音频播放循环出错: {e}")
                continue
    
    def _play_audio_file(self, audio_path, duration=None):
        """播放音频文件（带动作控制）"""
        try:
            logger.info(f"开始播放音频文件: {audio_path}")
            
            # 检查音频格式并优化
            audio_format = self._check_audio_format(audio_path)
            
            # 加载音频
            sound = pygame.mixer.Sound(audio_path)
            
            # 优化音量
            if audio_format == 'lossless':
                self._optimize_audio_volume(sound, 0.92)
            elif audio_format == 'mp3':
                self._optimize_audio_volume(sound, 0.85)
            else:
                self._optimize_audio_volume(sound, 0.9)
            
            # 获取音频长度
            if duration is None:
                audio_length = sound.get_length()
                logger.info(f"音频长度(从文件获取): {audio_length}秒")
            else:
                audio_length = duration
                logger.info(f"音频长度(从参数获取): {audio_length}秒")
            
            # 播放音频并控制动作
            self._play_with_actions(sound, audio_length)
            
        except Exception as e:
            logger.error(f"播放音频文件失败: {e}")
    
    def _play_audio_data(self, audio_data, duration=None):
        """播放音频数据（带动作控制）"""
        try:
            # 对于小音频片段，直接使用pygame播放，减少临时文件开销
            if len(audio_data) < 8192:  # 小于8KB，可能是流式片段
                logger.debug("播放流式音频片段")
                self._play_audio_chunk_direct(audio_data, duration)
            else:
                logger.info("播放完整音频数据")
                # 创建临时文件
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                temp_filename = f"temp_audio_{uuid.uuid4().hex[:8]}.mp3"
                temp_audio_file = os.path.join(project_root, temp_filename)

                # 写入音频数据
                with open(temp_audio_file, 'wb') as f:
                    f.write(audio_data)

                try:
                    # 播放临时文件
                    self._play_audio_file(temp_audio_file, duration)
                finally:
                    # 延迟清理临时文件
                    def delayed_cleanup():
                        time.sleep(2)
                        try:
                            if os.path.exists(temp_audio_file):
                                os.remove(temp_audio_file)
                                logger.debug(f"已清理临时文件: {temp_audio_file}")
                        except Exception as e:
                            logger.warning(f"清理临时文件失败: {e}")

                    cleanup_thread = threading.Thread(target=delayed_cleanup, daemon=True)
                    cleanup_thread.start()

        except Exception as e:
            logger.error(f"播放音频数据失败: {e}")

    def _play_audio_chunk_direct(self, audio_data, duration=None):
        """直接播放小音频片段（流式播放优化）"""
        try:
            # 直接使用pygame播放，避免临时文件开销
            audio_file = io.BytesIO(audio_data)
            pygame.mixer.music.load(audio_file)

            # 获取音频长度
            if duration is None or duration == 0:
                # 对于小片段，使用简单估算
                duration = max(0.1, len(audio_data) / (16 * 1024))

            # 启动动作控制（简化版，适合短片段）
            if duration > 0.1:  # 只有足够长的片段才启动动作
                mouth_thread = threading.Thread(target=self._mouth_action_wait, args=(duration,))
                mouth_thread.daemon = True
                mouth_thread.start()

            # 播放音频
            pygame.mixer.music.play()

            # 等待播放完成
            while pygame.mixer.music.get_busy():
                if self.interrupt_flag:
                    pygame.mixer.music.stop()
                    break
                pygame.time.wait(50)  # 更短的等待间隔，提高响应性

        except Exception as e:
            logger.error(f"直接播放音频片段失败: {e}")
    
    def _play_with_actions(self, sound, audio_length):
        """播放音频并控制动作"""
        try:
            # 启动眼睛控制
            self._eyes_action(1)
            
            # 启动动作控制线程
            mouth_thread = threading.Thread(target=self._mouth_action_wait, args=(audio_length,), daemon=True)
            neck_thread = threading.Thread(target=self._neck_action_wait, args=(audio_length,), daemon=True)
            
            # 播放音频
            channel = sound.play()
            logger.info("开始播放音频")
            
            # 启动动作线程
            try:
                mouth_thread.start()
                logger.info("嘴部动作控制线程已启动")
                neck_thread.start()
                logger.info("脖子动作控制线程已启动")
            except Exception as e:
                logger.error(f"启动动作控制线程失败: {e}")
            
            # 等待播放完成
            while channel.get_busy():
                if self.interrupt_flag:
                    logger.info("音频播放被打断")
                    pygame.mixer.stop()
                    break
                pygame.time.wait(100)

            # 等待动作线程完成
            try:
                mouth_thread.join(timeout=2.0)  # 最多等待2秒
                neck_thread.join(timeout=2.0)   # 最多等待2秒
                logger.info("动作控制线程已完成")
            except Exception as e:
                logger.error(f"等待动作线程完成时出错: {e}")

            # 关闭眼睛
            self._eyes_action(0)

            if not self.interrupt_flag:
                logger.info("音频播放完成，所有动作已结束")
            else:
                logger.info("音频播放被打断结束，所有动作已停止")
                
        except Exception as e:
            logger.error(f"播放音频时出错: {e}")

    def _check_audio_format(self, audio_path):
        """检查音频格式"""
        ext = os.path.splitext(audio_path)[1].lower()
        if ext in ['.wav', '.flac']:
            logger.info("检测到WAV/FLAC无损音频文件，已应用高质量音频优化")
            return 'lossless'
        elif ext == '.mp3':
            logger.info("检测到MP3文件，已应用针对压缩音频的优化设置")
            return 'mp3'
        else:
            return 'other'

    def _optimize_audio_volume(self, sound, volume):
        """优化音频音量"""
        sound.set_volume(volume)
        logger.info(f"音频音量设置为: {volume*100:.1f}% (针对WAV无损格式优化)")

    def _modbus_sender(self, data):
        """发送 Modbus 寄存器数据"""
        if not self.client:
            # 只在第一次失败时尝试重连，避免频繁重连
            if not hasattr(self, '_reconnect_attempted'):
                logger.warning("Modbus 客户端未连接，尝试重新连接...")
                self._init_modbus()
                self._reconnect_attempted = True
            if not self.client:
                return  # 静默跳过，避免频繁日志

        try:
            for item in data:
                addr, value = item.split('-')
                result = self.client.write_register(address=int(addr), value=int(value), no_response_expected=False)
                if hasattr(result, 'isError') and result.isError():
                    logger.error(f"写入寄存器{addr}失败: {result}")
        except Exception as e:
            # 连接可能断开，重置客户端和重连标志
            self.client = None
            if hasattr(self, '_reconnect_attempted'):
                delattr(self, '_reconnect_attempted')

    def _modbus_sender_bit(self, data):
        """发送 Modbus 线圈数据"""
        if not self.client:
            # 只在第一次失败时尝试重连
            if not hasattr(self, '_reconnect_attempted'):
                self._init_modbus()
                self._reconnect_attempted = True
            if not self.client:
                return  # 静默跳过

        try:
            for item in data:
                addr, value = item.split('-')
                result = self.client.write_coil(address=int(addr), value=int(value), no_response_expected=False)
                if hasattr(result, 'isError') and result.isError():
                    logger.error(f"写入线圈{addr}失败: {result}")
        except Exception as e:
            # 连接可能断开，重置客户端和重连标志
            self.client = None
            if hasattr(self, '_reconnect_attempted'):
                delattr(self, '_reconnect_attempted')

    def _eyes_action(self, state):
        """控制眼睛动作"""
        try:
            self._modbus_sender_bit([f"{self.eyes_reg}-{state}"])
        except Exception as e:
            logger.error(f"眼睛控制失败: {e}")

    def _mouth_action_wait(self, seconds):
        """控制嘴巴动作"""
        try:
            sleep_time = int(seconds / 0.12)

            for i in range(sleep_time):
                if self.interrupt_flag:
                    break

                # 使用随机角度模拟说话动作（与原实现一致）
                angle = random.randint(10, 45)
                data = [self.mouth_mode, f'1032-{angle}', self.mouth_speed]
                self._modbus_sender(data)
                time.sleep(0.12)

        except Exception as e:
            logger.error(f"嘴巴动作控制失败: {e}")
        finally:
            # 确保嘴巴最后闭合
            try:
                data = [self.mouth_mode, self.mouth_angle_close, self.mouth_speed]
                self._modbus_sender(data)
            except Exception as e:
                pass  # 静默处理闭合失败

    def _neck_action_wait(self, seconds):
        """控制脖子动作"""
        try:
            sleep_time = int(seconds / 0.12)

            for i in range(sleep_time):
                if self.interrupt_flag:
                    break

                neck_reg = f'1034-{random.randint(10, 45)}'
                data = [neck_reg, self.neck_reg1, self.neck_reg_speed]
                self._modbus_sender(data)
                time.sleep(0.15)

        except Exception as e:
            logger.error(f"脖子动作控制失败: {e}")
        finally:
            # 确保脖子最后复位
            try:
                data = ['1034-0', self.neck_reg1, self.neck_reg_speed]
                self._modbus_sender(data)
            except Exception as e:
                pass  # 静默处理复位失败


# 全局实例
_audio_controller = None

def get_audio_controller():
    """获取全局音频控制器实例"""
    global _audio_controller
    if _audio_controller is None:
        _audio_controller = UnifiedAudioController()
        _audio_controller.start()
    return _audio_controller

def play_audio_file(audio_path, duration=None):
    """播放音频文件（全局函数接口）"""
    controller = get_audio_controller()
    controller.add_audio_file(audio_path, duration)

def play_audio_data(audio_data, duration=None):
    """播放音频数据（全局函数接口）"""
    controller = get_audio_controller()
    controller.add_audio_data(audio_data, duration)

def interrupt_audio():
    """打断音频播放（全局函数接口）"""
    controller = get_audio_controller()
    controller.interrupt()

# 兼容性接口
def play_audio_with_action(audio_path, duration=None):
    """兼容性接口 - 播放音频文件"""
    play_audio_file(audio_path, duration)

def play_idle_audio_with_action(audio_path, duration=None):
    """兼容性接口 - 播放音频文件"""
    play_audio_file(audio_path, duration)
