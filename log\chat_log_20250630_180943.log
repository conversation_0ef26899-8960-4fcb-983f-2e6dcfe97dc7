2025-06-30 18:09:44.277 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-06-30 18:09:44.277 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-06-30 18:09:44.295 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751278184296&accessNonce=ff1bcc85-c58f-4d1f-86c2-3f3cd0aa2c2a&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=58488226-559a-11f0-9537-dc4546c07870&requestId=653faf1e-5acc-4ace-bd05-1346ee67adcc_joyinside&accessSign=3494aa53cff5ca56269be8c3fc10e7e7, request_id: 653faf1e-5acc-4ace-bd05-1346ee67adcc_joyinside
2025-06-30 18:09:44.296 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-06-30 18:09:44.296 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-06-30 18:09:44.627 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-06-30 18:09:44.835 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-06-30 18:09:46.265 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-06-30 18:09:46.267 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-06-30 18:09:46.267 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-06-30 18:09:46.267 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-06-30 18:09:46.331 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-06-30 18:09:46.331 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-06-30 18:10:56.913 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 18:10:56.913 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 18:10:56.975 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 18:10:56.975 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 18:10:58.512 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 18:10:58.514 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 18:11:00.252 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 18:11:00.252 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 18:11:03.750 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我要稍微等一下, 时间戳: 2025-06-30 18:11:09.469000
2025-06-30 18:11:04.058 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 18:11:04.059 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 18:11:04.065 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 58488226-559a-11f0-9537-dc4546c07870; requestId: 653faf1e-5acc-4ace-bd05-1346ee67adcc_joyinside; asr: 我要稍微等一下; 响应时间: 0; JD机器人回复: 
2025-06-30 18:11:04.065 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 18:11:04.065 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 18:11:04.065 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 18:11:06.969 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 18:11:23.055 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 18:11:23.056 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 18:11:40.928 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你现在有, 时间戳: 2025-06-30 18:11:46.648000
2025-06-30 18:11:41.212 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 18:11:41.212 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 18:11:41.218 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 58488226-559a-11f0-9537-dc4546c07870; requestId: 653faf1e-5acc-4ace-bd05-1346ee67adcc_joyinside; asr: ，你现在有; 响应时间: 0; JD机器人回复: 
2025-06-30 18:11:41.218 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 18:11:41.218 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 18:11:41.219 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 18:11:42.082 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 18:11:42.082 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 18:11:42.146 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 18:11:42.146 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 18:11:43.558 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 18:11:43.622 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 18:11:50.289 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 18:11:50.289 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 18:11:50.353 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 18:11:50.353 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 18:11:51.392 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 18:11:51.454 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 18:12:01.536 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 关闭聊天, 时间戳: 2025-06-30 18:12:07.256000
2025-06-30 18:12:01.844 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 18:12:01.845 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 18:12:04.843 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 18:12:04.843 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 18:12:04.904 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 18:12:04.904 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 18:12:06.178 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 18:12:06.188 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 18:12:08.756 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退出, 时间戳: 2025-06-30 18:12:14.476000
2025-06-30 18:12:09.055 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 18:12:09.056 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 18:12:22.507 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 18:12:22.507 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 18:12:22.574 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 18:12:22.574 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 18:12:23.608 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 18:12:23.618 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 18:12:30.516 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退出, 时间戳: 2025-06-30 18:12:36.236000
2025-06-30 18:12:30.806 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 18:12:30.806 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 18:12:57.219 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 18:12:57.219 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 18:12:57.282 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 18:12:57.282 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 18:12:58.314 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 18:12:58.321 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 18:13:04.699 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-06-30 18:13:04.699 - chat_with_robot - voice.py - stop - line 401 - INFO - 已停止local_streaming检测器
