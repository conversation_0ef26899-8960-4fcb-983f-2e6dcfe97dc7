import sys
import os
import threading
from datetime import datetime

# 添加项目根目录到sys.path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
from robot_agent.robot_config_magicdog import ROBOT_COMMANDS
import numpy as np
import py_magicdog_sdk
import signal
import sys
import time
from robot_agent.fsm_config import *
from util.logger import logger

# 自定义状态码常量（提高可读性）
FSM_BALANCE_STAND2 = 301
class MagicdogCommander:
    def __init__(self):
        self.is_down = True
        self.action_queue = []  # 动作队列
        self.current_action = None  # 当前执行的动作
        self.default_action = ("BalanceStand", 5.0)  # 默认状态
        self.time_remaining = 0  # 当前动作剩余时间
 
        self.running = False
        self.thread = None

        # 初始化通信接口和数据结构
        self.io_interface = py_magicdog_sdk.DataCmdExchange()
        self.data = py_magicdog_sdk.InputData()
        self.cmd = py_magicdog_sdk.OutputData()
        self.dt = 0.002  # 控制周期 (s)
        self.cnt = 0     # 控制步数计数器
        self.is_initialized = False
        self.is_running = False

        self.current_state = FSM_PASSIVE  # 跟踪当前状态

        # 安全关闭参数
        #self.shutdown_height = 0.15  # 安全倒地高度 (米)
        #self.shutdown_duration = 2.0  # 安全关闭持续时间 (秒)
        self._stop_requested = False
        self._last_state = FSM_PASSIVE  # 用于检测状态变化
        self._state_change_callbacks = []  # 状态变化回调函数列表
        self.lastParam2 = 0.0
        self.lastParam1 = 0.0
        self.lastParam0 = 0.0

        #动作打断标志
        self.interrupt = False
        # 注册信号处理函数
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """处理中断信号（Ctrl+C）"""
        if self.is_running:
            print("\n接收到中断信号，正在安全关闭机器人...")
            self.is_running = False
            self._stop_requested = True
            self._safe_shutdown()
    
    def _update_state(self):
        """更新当前状态并检测状态变化"""
        self._last_state = self.current_state
        self.current_state = self.data.robot_fsm_data
        
        # 检测状态变化
        if self.current_state != self._last_state:
            old_name = STATE_NAMES.get(self._last_state, f"未知状态({self._last_state})")
            new_name = STATE_NAMES.get(self.current_state, f"未知状态({self.current_state})")
            print(f"{self.now()} 状态变更: {old_name} → {new_name}")
            logger.info(f"状态变更: {old_name} → {new_name}")
            # 触发回调函数
            # for callback in self._state_change_callbacks:
            #     callback(self._last_state, self.current_state)

    def add_state_change_callback(self, callback):
        """注册状态变化回调函数
        
        Args:
            callback: 回调函数，接受两个参数 (old_state, new_state)
        """
        self._state_change_callbacks.append(callback)

    def initialize(self):
        """初始化机器人并连接"""
        # 清除数据结构
        self.data.clear()
        self.cmd.clear()
        
        # 尝试建立连接
        print("正在建立与机器人的连接...")
        logger.info("正在建立与机器人的连接...")
        while not self.io_interface.isConnect():
            t_start = self.io_interface.initDuration()
            self.io_interface.waitDuration(t_start, self.dt)
        
        print("连接成功!")
        
        # 确保机器人处于被动状态
        print(f"{self.now()} 正在设置机器人初始状态为 Passive...")
        logger.info(f"正在设置机器人初始状态为 Passive...")

        first_enter = True
        while first_enter:
            t_start = self.io_interface.initDuration()
            
            # 发送被动状态命令
            self.cmd.robot_fsm_cmd = FSM_PASSIVE
            self.io_interface.sendCmd(self.cmd)
            self.io_interface.waitDuration(t_start, 1.0)
            
            # 检查状态是否已更新
            self.io_interface.receiveData(self.data)
            self.current_state = self.data.robot_fsm_data

            first_enter = (self.data.robot_fsm_data != FSM_PASSIVE)
        
        print(f"机器人初始化完成，当前状态: {STATE_NAMES[self.current_state]}")
        logger.info(f"机器人初始化完成，当前状态: {STATE_NAMES[self.current_state]}")

        self.is_initialized = True
        return True
        
    def _validate_transition(self, target_state):
        """验证状态转移是否合法
        
        Args:
            target_state: 目标状态码
            
        Returns:
            bool: 转移是否合法
        """
        print(f"{self.now()} 验证状态转移是否合法，状态码 {self.current_state}->{target_state}")
        logger.info(f"验证状态转移是否合法，状态码 {self.current_state}->{target_state}")

        if target_state == self.current_state:
            return True
        if target_state not in STATE_NAMES:
            print(f"错误: 未知状态码{STATE_NAMES[self.current_state]} -> {STATE_NAMES[target_state]}")
            logger.info(f"错误: 未知状态码{STATE_NAMES[self.current_state]} -> {STATE_NAMES[target_state]}")

            return False
            
        if self.current_state not in STATE_TRANSITIONS:
            print(f"错误: 当前状态 {STATE_NAMES[self.current_state]} 没有定义转移规则")
            logger.info(f"错误: 当前状态 {STATE_NAMES[self.current_state]} 没有定义转移规则")

            return False
            
        if target_state not in STATE_TRANSITIONS[self.current_state]:
            source_name = STATE_NAMES[self.current_state]
            target_name = STATE_NAMES[target_state]
            allowed = ", ".join(STATE_NAMES[s] for s in STATE_TRANSITIONS[self.current_state])
            print(f"{self.now()} 错误: 不允许从 {source_name} 转移到 {target_name}")
            logger.info(f"错误: 不允许从 {source_name} 转移到 {target_name}")

            print(f"允许的转移: {allowed}")
            return False
        logger.info(f"状态转移合法，状态码{self.current_state}-> {target_state}")
        return True
    
     
 
    def shutdown(self):
        """安全关闭机器人连接"""
        if not self.is_initialized:
            print("机器人已经关闭")
            return
            
        print("正在执行安全关闭...")
        # 3. 切换到被动状态（此时机器人已接近地面）
        self.cmd.robot_fsm_cmd = 5
        self.io_interface.sendCmd(self.cmd)        
        print("关闭机器人连接...")
        # 这里可能需要调用 SDK 中的特定关闭函数
        self.is_initialized = False
        print("机器人已安全关闭")

    def raise_up(self) :
        """执行命令 给上层业务/语音调用（线程安全）"""
        self.io_interface.receiveData(self.data)
        self._update_state()
        if self.current_state == FSM_PURE_DAMPER or self.current_state == FSM_LOW_LEVEL_CTRL:
            self.is_down = False
            #print(f"{self.now()} 阻尼态准备到默认状态:{self.current_state} ->{default_action}")
            logger.info(f"阻尼态准备到默认状态")
            self._start_new_action("Passive",0.05)
            self._start_new_action("RecoveryStand",8)
        elif self.current_state == FSM_PASSIVE :
            self.is_down = False
            #print(f"{self.now()} PASSIVE态准备到默认状态:{self.current_state} ->{default_action}")
            logger.info(f"PASSIVE态准备到默认状态")
            self._start_new_action("RecoveryStand",8)


    def execute_command(self, action, duration=1, *params):
        """执行命令 给上层业务/语音调用（线程安全）"""
        with threading.Lock():
            """执行命令，支持队列和默认状态"""

            #主动唤醒,趴下之后才能主动唤醒
            if action == FSM_RECOVERY_STAND and self.is_down ==True:
                logger.info("主动唤醒")
                self.action_queue.clear()
                self.raise_up()
                return
            
            if self.is_down == True:
                return
            
            #"起立/趴下" 高优先级命令
            if action == FSM_LOW_LEVEL_CTRL:
                logger.info("趴下，清空动作对列")
                self.action_queue.clear()
                self.time_remaining = 0
                self.is_down = True
                self._start_new_action(action, duration, *params)
                return

            
            # 处理持续时间参数
            if duration is None:
                # 如果未提供持续时间，使用默认值或查找预设值
                duration = self._get_default_duration(action)

            # 将命令添加到队列或立即执行
            if self.time_remaining <= 0:
                # 当前没有执行中的动作，立即执行
                print(f"{self.now()} 立即执行动作 '{action}' ")
                logger.info(f"立即执行动作 '{action}' ")

                self._start_new_action(action, duration, *params)
            else:
                # 当前有动作在执行，添加到队列
                self.action_queue.append((action, duration, params))
                self.interrupt = True
                print(f"{self.now()} 已将动作 '{action} {duration} {params}' 添加到队列（剩余 {len(self.action_queue)} 个动作）")
                logger.info(f"已将动作 '{action} {duration} {params}' 添加到队列（剩余 {len(self.action_queue)} 个动作）")


    def _start_new_action(self, action, duration, *params):
        """开始执行新动作"""
        self.current_action = action
        self.time_remaining = duration
        print(f"{self.now()} 开始执行动作: {action}，持续时间: {duration}秒 {params}")
        logger.info(f"开始执行动作: {action}，持续时间: {duration}秒 {params}")
        start_time = time.time()  # 记录开始时间
        
        # 实际执行动作的逻辑（调用底层API）
        try:
            # 将参数展开并传递给实际的命令执行函数
            self._send_command_to_robot(action, duration, *params)
            end_time = time.time()    # 记录结束时间
            elapsed_time = end_time - start_time  # 计算耗时
            print(f"{self.now()} 执行动作完成: {action}，持续时间: {duration}秒 {params}，实际耗时： {elapsed_time:.6f} 秒")
            logger.info(f"执行动作完成: {action}，持续时间: {duration}秒 {params} ，实际耗时： {elapsed_time:.6f} 秒")


        except Exception as e:
            print(f"{self.now()} 执行动作失败: {str(e)}")
            logger.info(f"执行动作失败: {str(e)}")
            
            # 失败时切换到默认状态
            #self._switch_to_default_action()
    def now(self) :
        now = datetime.now()
        formatted_time = now.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]  # 保留3位毫秒
        return formatted_time # 输出: 2023-10-11 12:38:41.456
    def _update(self, dt):
        """更新函数，每帧调用，减少剩余时间并处理状态切换"""
         
        if self.action_queue:
            next_action, next_duration, next_params = self.action_queue.pop(0)
            print(f"{self.now()} 从动作队列获取新动作: {next_action}，持续时间: {next_duration}秒 {next_params}")
            logger.info(f"从动作队列获取新动作: {next_action}，持续时间: {next_duration}秒 {next_params}")

            self._start_new_action(next_action, next_duration, *next_params)

        else:
            # 队列为空，切换到默认状态
            self._switch_to_default_action()

    def _switch_to_default_action(self):
        """切换到默认状态"""
        default_action, default_duration = self.default_action

        self.io_interface.receiveData(self.data)
        self._update_state()
        #print(f"{self.now()} 自动切换到默认状态:{self.current_state} ->{default_action}")
        if self.is_down == False:
            logger.info(f"准备自动切换到默认状态: {self.current_state}->{default_action}")
            #异常状态则起立
            self.raise_up()
            self._start_new_action(default_action, default_duration)
            self._start_new_action(default_action, 2.0,FSM_BALANCE_STAND2)


    def _get_default_duration(self, action):
        """获取动作的默认持续时间"""
        # 可以根据动作类型设置不同的默认持续时间
        default_durations = {
            "RecoveryStand": 8.0,
            "BalanceStand": 3.0,
            "trot": 2.0,
            "sit": 4.0
        }
        return default_durations.get(action, 2.0)  # 默认3秒

    def _send_command_to_robot(self, action, duration, *params):
        """实际发送命令到机器人的底层函数"""
        # 这里调用机器人SDK的实际命令接口
        #print(f"_send_command_to_robot:{action} {duration} {params}")
        self._execute_command(action,duration,*params)
 
    
    def _execute_command(self, command, duration=2.0, *params):
        """执行指定的机器人命令
        
        Args:
            command: 要执行的命令 (str 或 int)
            duration: 命令执行时间 (秒)
        """
        #print(f"{self.now()} _execute_command:{command} {duration} {params}")
        logger.info(f" _execute_command:{command} {duration} {params}")

        if not self.is_initialized:
            print("错误: 机器人尚未初始化，请先调用 initialize()")
            logger.info("错误: 机器人尚未初始化，请先调用 initialize()")

            return False
            
        # 将步数转换为持续时间
        steps = int(duration / self.dt)
        
        
        # 解析命令为状态码
        if isinstance(command, str):
            # 尝试通过状态名称查找状态码
            state_code = None
            for code, name in STATE_NAMES.items():
                if name.lower() == command.lower():
                    state_code = code
                    break
            if command.isdigit() == True :
                for code, name in STATE_NAMES.items():
                    if code == int(command):
                        state_code = code
                        break
            if state_code is None:
                print(f"{self.now()} 错误: 未知命令/状态名称 '{command}'")
                logger.info(f"错误: 未知命令/状态名称 '{command}'")
                return False
        else:
            state_code = command

        if state_code >1000 :
            logger.info(f"错误: 未知命令/状态名称 '{command}'")
            return False
        fsm_cmd = state_code
        # 执行前再次确认当前状态（避免其他线程/进程修改）
        self.io_interface.receiveData(self.data)
        self._update_state()

        # 验证状态转移合法性
        if not self._validate_transition(state_code):
           return False
        
        allowed_interrupt_states = {FSM_BALANCE_STAND }

        param0, param1, param2 = (*params[:3], None, None, None)[:3]

        #if len(params) >= 0:
            #param0, param1, param2 = params[:3]
        # else :
        #     print("参数错误")
        #     return
        for _ in range(steps):
            #处于可打断状态，则打断
            if self.interrupt == True and state_code in allowed_interrupt_states: 
                print(f"{self.now()} FSM_BALANCE_STAND状态已被新动作打断")
                logger.info(f"FSM_BALANCE_STAND状态已被新动作打断")
                return
            if self.interrupt == True and state_code == FSM_TROT: 
                if param0==0 and param1 ==0 and param2 ==0:
                    print(f"{self.now()} FSM_TROT状态已被停止")
                    logger.info(f"FSM_TROT状态已被停止")
                    return

            if self._stop_requested:
                break
            #t_start = self.io_interface.initDuration()
            next_time = time.time()  # 初始化下次执行时间
            # 接收当前状态
            #t_before_receive = time.time()
            self.io_interface.receiveData(self.data)  
            #self.io_interface.receiveData(self.data)
            #t_after_receive = time.time()
            #logger.info(f"接收数据耗时: {t_after_receive - t_before_receive:.6f}秒")

            self._update_state()
            #logger.info(f"轮次{self.cnt}，状态：{self.data.robot_fsm_data} t_start {t_start}")

            # 设置状态机命令
            self.cmd.robot_fsm_cmd = fsm_cmd
            
            # 执行前检查状态是否被意外更改
            # if self.current_state != state_code:
            #     print(f"警告: 状态意外变更为 {STATE_NAMES.get(self.current_state, self.current_state)}")
                
            #     # 如果新状态不允许目标状态，终止执行
            #     if not self._validate_transition(state_code):
            #         print("错误: 当前状态已不允许执行该命令")
            #         break
                
            # 根据不同状态设置特定运动参数
            if fsm_cmd == FSM_BALANCE_STAND:
                # 添加周期性姿态扰动
                self.cmd.motion_cmd.body_height = 0.28
                if param0 == FSM_BALANCE_STAND2:
                    self.cmd.motion_cmd.rpy_des[0] = 0.2 * np.sin(1.0 * self.cnt * np.pi / 2500.0)
                    self.cmd.motion_cmd.rpy_des[1] = 0.15 * np.sin(2.0 * self.cnt * np.pi / 2500.0)
                    self.cmd.motion_cmd.rpy_des[2] = 0.2 * np.sin(3.0 * self.cnt * np.pi / 2500.0)
            elif fsm_cmd == FSM_TROT:
                
                self.cmd.motion_cmd.rpy_des[1] = 0.0  # 清除俯仰角扰动
                if param2 is not None:
                    self.lastParam2 = param2
                self.cmd.motion_cmd.v_des[2] = self.lastParam2   #   z轴线速度 转向速度
                if param1 is not None:
                    self.lastParam1 = param1
                self.cmd.motion_cmd.v_des[1] = self.lastParam1    #   y轴线速度（左右平移）
                if param0 is not None:
                    self.lastParam0 = param0 
                self.cmd.motion_cmd.v_des[0] = self.lastParam0    #    x轴线速度（前进/后退）


                self.cmd.motion_cmd.step_height = 0.05  # 设置步高
                
            # 发送命令
            self.io_interface.sendCmd(self.cmd)
            #logger.info(f"轮次{self.cnt}，状态：{self.data.robot_fsm_data} 发送命令 {fsm_cmd}")

            # 等待下一周期
            #self.io_interface.waitDuration(t_start, self.dt)
            
            
            # 计算实际需要等待的时间
            current_time = time.time()
            wait_time = next_time +self.dt - current_time
            if wait_time > 0:
                time.sleep(wait_time)  # 等待剩余时间
            #else:
                # 如果代码执行时间超过了间隔，立即执行下一次
                #print(f"警告：任务执行时间{next_time} ({-wait_time:.6f}秒)超过了间隔时间!")
            self.cnt += 1
        self.interrupt = False
        return True
    
    def _run_thread(self, dt):
        """线程主函数"""
        logger.info("动作线程已启动")
        while self.running:
            self._update(dt)
            time.sleep(dt)  # 控制循环频率

    def start(self, dt=0.02):
        """启动线程"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self._run_thread, args=(dt,))
            self.thread.daemon = True  # 设置为守护线程
            self.thread.start()

    def stop(self):
        """停止线程"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=1.0)


    def send_command(self, command,duration,*param):
        """发送命令到机器人"""
        try:
            self.execute_command(command, duration, *param) 
            return True
        except Exception as e:
            logger.error(f"发送命令失败: {str(e)}")
            return False
    
    def run(self, text):
        """运行命令"""
        if text not in ROBOT_COMMANDS:
            logger.info(f"错误: 未知命令 '{text}'，可用命令: {list(ROBOT_COMMANDS.keys())}")
            return False
        logger.info(f"执行命令: '{text}'")  # 执行命令打印log - 开关
        result = self.send_command(ROBOT_COMMANDS[text]['code'],ROBOT_COMMANDS[text].get("duration", None),ROBOT_COMMANDS[text].get("param0", None),
                                       ROBOT_COMMANDS[text].get("param1", None),
                                       ROBOT_COMMANDS[text].get("param2", None))
        return True


    def wait(self):
        """阻塞主线程，直到手动调用stop()"""
        try:
            while self.running:
                time.sleep(0.1)  # 减少CPU占用
        except KeyboardInterrupt:  # 允许用户按Ctrl+C终止
            print("\n接收到终止信号，正在停止机器人...")
            self.stop()


# if __name__ == "__main__":
#     # 创建机器人控制器实例
#     robot = MagicdogCommander()
    
#     # 初始化机器人
#     if robot.initialize():
#         print("\n=== 机器人初始化成功，进入交互控制模式 ===")
#         print("可用命令: RecoveryStand, BalanceStand, trot, balance_stand")
#         print("输入格式: 命令 持续时间(秒) [参数1] [参数2]...")
#         print("输入 'exit' 结束程序\n")
#         robot.start()
#         robot.execute_command("Passive",0.1)
#         robot.execute_command("RecoveryStand",9)
#         while True:
#             # 获取用户输入
#             user_input = input("请输入命令: ").strip()
            
#             # 检查退出条件
#             if user_input.lower() == 'exit':
#                 break
                
#             # 解析命令和参数
#             parts = user_input.split()
#             if not parts:
#                 print("错误: 命令不能为空")
#                 continue
                
#             command = parts[0]
#             params = []
            
#             # 解析持续时间参数(必填)
#             if len(parts) < 2:
#                 print("错误: 缺少持续时间参数")
#                 continue
                
#             try:
#                 duration = float(parts[1])
#                 params.append(duration)
#             except ValueError:
#                 print("错误: 持续时间必须是数字")
#                 continue
                
#             # 解析可选参数(如果有)
#             if len(parts) > 2:
#                 for param in parts[2:]:
#                     try:
#                         # 尝试转换为数字类型
#                         params.append(float(param))
#                     except ValueError:
#                         # 否则作为字符串参数
#                         params.append(param)
            
#             # 执行命令
#             try:
#                 print(f"执行命令: {command}, 参数: {params}")
#                 robot.execute_command(command, *params)
#             except Exception as e:
#                 print(f"执行命令失败: {str(e)}")
                
#         print("\n=== 程序已退出 ===")

if __name__ == "__main__":
    # 创建机器人控制器实例
    robot = MagicdogCommander()
    # 启动线程
    robot.initialize()
    robot.start()
    print("按Ctrl+C停止机器人...")

    # 初始化后直接执行一系列动作
    robot._execute_command("RecoveryStand",8)  # 使用默认持续时间
    robot._execute_command("BalanceStand",2)  # 使用默认持续时间
    robot._execute_command("4",1,0.1)  # 使用默认持续时间

    #robot._execute_command("trot",5.0,*(1, 0.5))  # 使用默认持续时间
    robot.execute_command("Passive",0.1)  # 使用默认持续时间

    robot.execute_command("RecoveryStand",9)  # 使用默认持续时间
    robot.execute_command("BalanceStand",2)  # 使用默认持续时间
    robot.execute_command("4",1,0,0,0)  # 使用默认持续时间

    #robot.execute_command("trot", 5.0, 1, 0.5)  # 指定持续时间和额外参数
    #robot.execute_command("sit")  # 添加到队列
    robot.wait()

# Passive 0.1
# RecoveryStand 8
# 4 1 0 0 0
#4 10 0 0 0
# 4 1 0.05 0 0.1   4 1 -0.05 0 0.1 

 
     