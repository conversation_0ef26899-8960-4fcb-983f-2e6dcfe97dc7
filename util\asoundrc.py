#!/usr/bin/env python3
"""
ALSA配置文件生成器 - 自动创建 ~/.asoundrc 文件
用法: python3 generate_asoundrc.py
"""

import os
import subprocess
import re
import sys

def get_terminal_width():
    """获取终端宽度"""
    try:
        return os.get_terminal_size().columns
    except:
        return 80

def print_header(text):
    """打印格式化的标题"""
    width = get_terminal_width()
    print("\n" + "=" * width)
    print(text.center(width))
    print("=" * width)

def run_command(cmd):
    """运行系统命令并返回输出"""
    try:
        result = subprocess.run(cmd, shell=True, check=True, 
                              stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                              text=True)
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {cmd}")
        print(f"错误: {e.stderr}")
        return ""

def get_sound_cards():
    """获取系统中的声卡信息"""
    print_header("检测音频设备")
    
    # 获取声卡列表
    card_output = run_command("cat /proc/asound/cards")
    if not card_output:
        card_output = run_command("aplay -l")
    
    cards = []
    card_pattern = re.compile(r'(\d+)\s+\[(\w+)\s*\]:\s*(.*?)-\s*(.*)')
    
    for line in card_output.split('\n'):
        match = card_pattern.search(line)
        if match:
            card_num = match.group(1)
            card_id = match.group(2)
            card_name = match.group(4).strip()
            cards.append({
                'number': card_num,
                'id': card_id,
                'name': card_name,
                'playback': False,
                'capture': False
            })
    
    # 检查设备的播放和录音能力
    playback_output = run_command("aplay -l")
    capture_output = run_command("arecord -l")
    
    for card in cards:
        if f"card {card['number']}:" in playback_output:
            card['playback'] = True
        if f"card {card['number']}:" in capture_output:
            card['capture'] = True
    
    # 打印检测到的设备
    print("\n检测到以下声卡设备:")
    print("-" * get_terminal_width())
    print(f"{'编号':<5}{'ID':<10}{'名称':<30}{'播放':<6}{'录音':<6}")
    print("-" * get_terminal_width())
    
    for card in cards:
        print(f"{card['number']:<5}{card['id']:<10}{card['name']:<30}" +
              f"{'是' if card['playback'] else '否':<6}" +
              f"{'是' if card['capture'] else '否':<6}")
    
    return cards

def select_devices(cards):
    """让用户选择输入和输出设备"""
    print_header("选择默认音频设备")
    
    # 筛选有效设备
    playback_cards = [card for card in cards if card['playback']]
    capture_cards = [card for card in cards if card['capture']]
    
    if not playback_cards:
        print("错误: 未检测到可用的播放设备!")
        sys.exit(1)
    
    if not capture_cards:
        print("错误: 未检测到可用的录音设备!")
        sys.exit(1)
    
    # 选择播放设备
    print("\n选择默认播放设备 (输出/扬声器):")
    for i, card in enumerate(playback_cards):
        print(f"{i}) {card['name']} (hw:{card['number']},0)")
    
    playback_choice = -1
    while playback_choice < 0 or playback_choice >= len(playback_cards):
        try:
            playback_choice = int(input("\n请输入播放设备编号: "))
        except ValueError:
            print("请输入有效的数字!")
    
    # 选择录音设备
    print("\n选择默认录音设备 (输入/麦克风):")
    for i, card in enumerate(capture_cards):
        print(f"{i}) {card['name']} (hw:{card['number']},0)")
    
    capture_choice = -1
    while capture_choice < 0 or capture_choice >= len(capture_cards):
        try:
            capture_choice = int(input("\n请输入录音设备编号: "))
        except ValueError:
            print("请输入有效的数字!")
    
    return {
        'playback': playback_cards[playback_choice],
        'capture': capture_cards[capture_choice]
    }

def get_advanced_options():
    """获取高级配置选项"""
    print_header("高级配置选项")
    
    options = {
        'use_dmix': True,
        'use_dsnoop': True,
        'sample_rate': 48000,
        'channels': 2,
        'period_size': 1024,
        'buffer_size': 4096
    }
    
    print("\n是否要配置高级选项? [y/N]: ", end="")
    if input().lower() != 'y':
        return options
    
    print("\n选择采样率:")
    print("1) 44100 Hz (CD质量)")
    print("2) 48000 Hz (DVD/专业音频)")
    print("3) 16000 Hz (语音识别优化)")
    print("4) 8000 Hz (电话质量)")
    
    rate_choice = input("\n请选择 [2]: ").strip() or "2"
    rate_map = {"1": 44100, "2": 48000, "3": 16000, "4": 8000}
    if rate_choice in rate_map:
        options['sample_rate'] = rate_map[rate_choice]
    
    print("\n选择通道数:")
    print("1) 单声道 (语音识别优化)")
    print("2) 立体声 (音乐/标准音频)")
    
    channel_choice = input("\n请选择 [2]: ").strip() or "2"
    if channel_choice == "1":
        options['channels'] = 1
    
    print("\n是否使用 dmix 插件实现多程序同时播放? [Y/n]: ", end="")
    if input().lower() == 'n':
        options['use_dmix'] = False
    
    print("\n是否使用 dsnoop 插件实现多程序同时录音? [Y/n]: ", end="")
    if input().lower() == 'n':
        options['use_dsnoop'] = False
    
    print("\n是否配置缓冲区大小?")
    print("较小的值 = 低延迟但可能爆音")
    print("较大的值 = 更稳定但延迟更高")
    print("[Y/n]: ", end="")
    
    if input().lower() != 'n':
        try:
            period = input("\n周期大小 [1024]: ").strip() or "1024"
            options['period_size'] = int(period)
            
            buffer = input("\n缓冲区大小 [4096]: ").strip() or "4096"
            options['buffer_size'] = int(buffer)
        except ValueError:
            print("使用默认值")
    
    return options

def generate_asoundrc(devices, options):
    """生成 .asoundrc 文件内容"""
    playback_device = f"hw:{devices['playback']['number']},0"
    capture_device = f"hw:{devices['capture']['number']},0"
    
    # 准备配置文件内容
    content = [
        "# ALSA配置文件 (~/.asoundrc)",
        "# 自动生成于 ALSA配置生成器",
        "",
        "# 默认设备配置",
        "pcm.!default {"
    ]
    
    # 创建设备配置
    if devices['playback']['number'] == devices['capture']['number']:
        # 同一声卡用于输入和输出
        content.extend([
            "    type plug",
            f"    slave.pcm \"hw:{devices['playback']['number']},0\"",
            "    slave {"
        ])
    else:
        # 不同声卡用于输入和输出
        content.extend([
            "    type asym",
            "    playback.pcm {"
        ])
        
        if options['use_dmix']:
            content.extend([
                "        type plug",
                f"        slave.pcm \"dmixer\"",
                "    }",
                "    capture.pcm {"
            ])
        else:
            content.extend([
                "        type plug",
                f"        slave.pcm \"{playback_device}\"",
                "    }",
                "    capture.pcm {"
            ])
        
        if options['use_dsnoop']:
            content.extend([
                "        type plug",
                f"        slave.pcm \"dsnooper\"",
                "    }"
            ])
        else:
            content.extend([
                "        type plug",
                f"        slave.pcm \"{capture_device}\"",
                "    }"
            ])
    
    # 如果是同一声卡，添加通用参数
    if devices['playback']['number'] == devices['capture']['number']:
        content.extend([
            f"        rate {options['sample_rate']}",
            f"        channels {options['channels']}",
            f"        period_size {options['period_size']}",
            f"        buffer_size {options['buffer_size']}",
            "    }",
            "}"
        ])
    else:
        content.append("}")
    
    # 添加混音器配置
    if options['use_dmix']:
        content.extend([
            "",
            "# 播放混音器 (允许多个程序同时播放)",
            "pcm.dmixer {",
            "    type dmix",
            f"    ipc_key {1000 + int(devices['playback']['number'])}",
            "    slave {",
            f"        pcm \"{playback_device}\"",
            f"        rate {options['sample_rate']}",
            f"        channels {options['channels']}",
            f"        period_size {options['period_size']}",
            f"        buffer_size {options['buffer_size']}",
            "    }",
            "}"
        ])
    
    # 添加录音共享配置
    if options['use_dsnoop']:
        content.extend([
            "",
            "# 录音共享器 (允许多个程序同时录音)",
            "pcm.dsnooper {",
            "    type dsnoop",
            f"    ipc_key {2000 + int(devices['capture']['number'])}",
            "    slave {",
            f"        pcm \"{capture_device}\"",
            f"        rate {options['sample_rate']}",
            f"        channels {options['channels']}",
            f"        period_size {options['period_size']}",
            f"        buffer_size {options['buffer_size']}",
            "    }",
            "}"
        ])
    
    # 添加控制器配置
    content.extend([
        "",
        "# 默认控制器配置",
        "ctl.!default {",
        "    type hw",
        f"    card {devices['playback']['number']}",
        "}"
    ])
    
    # 添加辅助设备
    content.extend([
        "",
        "# 辅助设备别名",
        "pcm.mic {",
        "    type plug",
        f"    slave.pcm \"{capture_device}\"",
        "}",
        "",
        "pcm.speaker {",
        "    type plug", 
        f"    slave.pcm \"{playback_device}\"",
        "}"
    ])
    
    # 返回生成的内容
    return "\n".join(content)

def save_asoundrc(content):
    """保存配置到~/.asoundrc文件"""
    print_header("保存配置文件")
    
    # 备份旧文件
    asoundrc_path = os.path.expanduser("~/.asoundrc")
    if os.path.exists(asoundrc_path):
        backup_path = os.path.expanduser("~/.asoundrc.bak")
        try:
            os.rename(asoundrc_path, backup_path)
            print(f"已备份原配置文件到: {backup_path}")
        except Exception as e:
            print(f"备份文件时出错: {e}")
    
    # 保存新配置
    try:
        with open(asoundrc_path, 'w') as f:
            f.write(content)
        print(f"配置已保存到: {asoundrc_path}")
        print("\n配置文件内容:")
        print("-" * get_terminal_width())
        print(content)
        print("-" * get_terminal_width())
        return True
    except Exception as e:
        print(f"保存配置文件时出错: {e}")
        return False

def restart_audio():
    """重启音频服务使配置生效"""
    print_header("应用配置")
    
    print("要使配置生效,需要重启音频服务.")
    print("是否现在重启音频服务? [Y/n]: ", end="")
    
    if input().lower() != 'n':
        print("\n尝试重启PulseAudio...")
        run_command("pulseaudio -k && pulseaudio --start")
        
        print("\n尝试重新加载ALSA...")
        run_command("sudo alsa force-reload")
        
        print("\n音频服务已重启,新配置已生效.")
    else:
        print("\n要手动应用配置,请运行:")
        print("  pulseaudio -k && pulseaudio --start")
        print("或")
        print("  sudo alsa force-reload")

def test_audio():
    """测试音频配置"""
    print_header("测试音频配置")
    
    print("是否测试音频配置? [Y/n]: ", end="")
    if input().lower() == 'n':
        return
    
    # 测试播放
    print("\n播放测试...")
    run_command("speaker-test -c 2 -t sine -f 440 -D default -d 2")
    
    # 测试录音
    print("\n录音测试 (录制5秒)...")
    run_command("arecord -d 5 -f cd /tmp/test_rec.wav")
    
    print("\n播放录制的音频...")
    run_command("aplay /tmp/test_rec.wav")
    
    print("\n测试完成!")

def main():
    """主函数"""
    print_header("ALSA配置生成器")
    print("本程序将帮助你创建 ~/.asoundrc 配置文件,解决音频设备访问问题。")
    
    # 检测声卡
    cards = get_sound_cards()
    if not cards:
        print("没有检测到音频设备。请确认您的音频硬件已连接并被系统识别。")
        sys.exit(1)
    
    # 选择设备
    devices = select_devices(cards)
    
    # 获取高级选项
    options = get_advanced_options()
    
    # 生成配置
    content = generate_asoundrc(devices, options)
    
    # 保存配置
    if save_asoundrc(content):
        # 重启音频
        restart_audio()
        
        # 测试配置
        test_audio()
        
        print_header("配置完成")
        print("ALSA配置已成功完成!")
        print("如果遇到问题,请编辑 ~/.asoundrc 文件进行调整。")
    else:
        print("配置保存失败,请检查权限或手动创建配置文件。")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n操作已取消。")
    except Exception as e:
        print(f"\n发生错误: {e}")
