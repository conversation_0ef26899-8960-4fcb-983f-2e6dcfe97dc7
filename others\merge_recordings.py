import os
import glob
import re
import argparse
from datetime import datetime
import wave
import numpy as np

def get_timestamp_from_filename(filename):
    """从文件名中提取时间戳"""
    match = re.search(r'recording_(\d{8}_\d{6}_\d+)\.wav', os.path.basename(filename))
    if match:
        return match.group(1)
    return os.path.basename(filename)  # 如果无法提取则返回文件名

def merge_wav_files(input_dir, output_file):
    """合并WAV文件"""
    # 查找所有WAV文件
    wav_files = glob.glob(os.path.join(input_dir, '*.wav'))
    
    if not wav_files:
        print(f"在 {input_dir} 中没有找到WAV文件")
        return False
    
    # 按时间戳排序
    wav_files.sort(key=get_timestamp_from_filename)
    print(f"找到 {len(wav_files)} 个WAV文件，准备合并...")
    
    # 打开第一个文件获取参数
    with wave.open(wav_files[0], 'rb') as first_wav:
        params = first_wav.getparams()
        
    # 创建输出文件
    with wave.open(output_file, 'wb') as output_wav:
        output_wav.setparams(params)
        
        # 读取每个文件并写入输出
        for wav_file in wav_files:
            print(f"处理: {os.path.basename(wav_file)}")
            with wave.open(wav_file, 'rb') as input_wav:
                # 确保文件格式兼容
                if input_wav.getparams() != params:
                    print(f"警告: {wav_file} 参数与目标不匹配，跳过")
                    continue
                
                # 读取音频数据
                data = input_wav.readframes(input_wav.getnframes())
                # 写入输出文件
                output_wav.writeframes(data)
    
    print(f"合并完成，已保存到: {output_file}")
    return True

def main():
    parser = argparse.ArgumentParser(description='合并WAV录音文件')
    parser.add_argument('--input', '-i', default='recordings', 
                        help='包含WAV文件的目录路径 (默认: recordings)')
    parser.add_argument('--output', '-o', default='merged_recording.wav', 
                        help='输出文件名 (默认: merged_recording.wav)')
    
    args = parser.parse_args()
    
    # 确保输入目录存在
    if not os.path.isdir(args.input):
        print(f"错误: 目录 {args.input} 不存在")
        return 1
    
    # 合并文件
    success = merge_wav_files(args.input, args.output)
    
    return 0 if success else 1

if __name__ == '__main__':
    exit(main())