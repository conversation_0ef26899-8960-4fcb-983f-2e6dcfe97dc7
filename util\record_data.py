"""
@Author: <EMAIL> <EMAIL>
@Create Date: 2025.03.28
@Description: 

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
"""
import os
import sys
import json
import io
import re
import copy
import urllib
import time
from pathlib import Path
import argparse
import threading
import pyaudio
import requests
import wave
import numpy as np
import random
import collections
import functools
sys.path.append((Path(__file__).resolve().parent/"src").__str__())
sys.path.append(Path(__file__).resolve().parent.__str__())
from asr.robot import ASR, utils
from asr.robot import logging, Player
from datetime import datetime
from asr.constants import no_alsa_error

logger = logging.getLogger(__name__)


def check_audio():
    tmp = pyaudio.PyAudio()

    # 列出所有音频设备
    for i in range(tmp.get_device_count()):
        info = tmp.get_device_info_by_index(i)
        print(f"Device {i}: {info['name']}")
        print(f"  Max Input Channels: {info['maxInputChannels']}")
    


class RingBuffer(object):
    """Ring buffer to hold audio from PortAudio"""

    def __init__(self, size=4096):
        self._buf = collections.deque(maxlen=size)

    def extend(self, data):
        """Adds data to the end of buffer"""
        self._buf.extend(data)

    def get(self):
        """Retrieves data from the beginning of buffer and clears it"""
        tmp = bytes(bytearray(self._buf))
        self._buf.clear()
        return tmp


class Chat(object):
    def __init__(self,
                 log=0,
                 silent_th=10000,
                 mode='detect',
                 save_record_file=f"{os.path.dirname(os.path.dirname(os.path.abspath(__file__)))}/asserts/record_one_data.wav"
                 ):
        super(Chat, self).__init__()
        self.CHUNK = 2048 # ~128ms # 190 ~音频读取的缓冲区大小,对应于大约11.88毫秒的音频数据
        self.FORMAT = pyaudio.paInt16  # 采样位数
        self.CHANNELS = 1  # 声道数
        self.RATE = 16000  # 采样率，每秒采集的样例数
        self._running = True

        # 最多容纳5秒的音频数据.
        self.ring_buffer = RingBuffer(
            self.CHANNELS * self.RATE * 5
        )

        self.tencent_asr = ASR.get_engine_by_slug('tencent-asr')
        self.log = log
        self.silent_th = silent_th
        self.mode = mode
        self.silent_status = True
        self.silent_temp = sys.maxsize
        self.silent_count = 0
        self.silent_count_threshold = 4

        self._interrupted = True
        self.save_record_file = save_record_file

    def start_streaming(self):
        self.ring_buffer.get()
        logger.info("[start recording]...")
        self.stream.start_stream()

    def end_streaming(self):
        logger.info("[end recording]...")
        self.stream.stop_stream()

    def save_audio(self, audio, frames, save_file):
        # 保存为wav文件
        wf = wave.open(save_file, 'wb')
        wf.setnchannels(self.CHANNELS)  # 设置声道数
        wf.setsampwidth(audio.get_sample_size(self.FORMAT))  # 设置采样宽度
        wf.setframerate(self.RATE)  # 设置采样率
        wf.writeframes(b''.join(frames))  # 写入音频数据
        wf.close()

    def start_listen(self, callbacks=None):
        if callbacks is None:
            callbacks = self._detected_callback
        self.listen(
            detected_callback=callbacks,
            interrupt_check=self._interrupt_callback,
            sleep_time=0.03
        )

    def start_wakeup(self):
        self._interrupted = False

    def stop_wakeup(self):
        self._interrupted = True

    def _interrupt_callback(self):
        return self._interrupted

    def _detected_callback(self):
        logger.info("[wakeup] 检测到唤醒词")
        time.sleep(0.02)

    def listen(self,
              interrupt_check=lambda: False,
              sleep_time=0.03,
              detected_callback=lambda: False,
              ):
        def audio_callback(in_data, frame_count, time_info, status):
            # import ipdb;ipdb.set_trace()
            if utils.isRecordable():
                self.ring_buffer.extend(in_data)
                play_data = chr(0) * len(in_data)
            else:
                play_data = chr(0)
            return play_data, pyaudio.paContinue

        with no_alsa_error():
            import warnings
            warnings.filterwarnings("ignore")
            self.audio = pyaudio.PyAudio()

        self.stream = self.audio.open(
            format=self.FORMAT,
            channels=self.CHANNELS,
            rate=self.RATE,
            input=True,
            output=False,
            frames_per_buffer=self.CHUNK,
            start=True,
            stream_callback=audio_callback,
        )

        while self._running is True:
            if interrupt_check():
                while interrupt_check() != False:
                    # 阻塞直到释放
                    continue

            if self.mode == 'detect' or self.mode == 'record':
                data = self.ring_buffer.get()
                if len(data) == 0:
                    time.sleep(sleep_time)
                    continue

                audio_data = np.frombuffer(data, dtype=np.short)
                temp = np.max(audio_data)
                timestamp = datetime.now().strftime('%H-%M-%S-%f')[:-3]

                if self.log >= 2: # DEBUG
                    logger.info("[wakeup] {}: Energy=[{}]".format(timestamp, temp))

                if temp > self.silent_th and self.mode != 'record':
                    if self.log >= 1: # INFO
                        print("[wakeup] 开始录制音频")
                    self.mode = 'record'
                    self.silent_count = 0  # 计数清零
                    self.silent_temp = sys.maxsize
                    recorded_data = []
                    recorded_energy = []

                if self.mode == 'record':
                    recorded_data.append(data)
                    recorded_energy.append(temp)

                    if temp > self.silent_th and self.silent_status:
                        # 进入有声音状态
                        self.silent_status = False
                    if temp > self.silent_th and self.silent_temp < self.silent_th:
                        self.silent_count = 0  # 重新开始计数
                        self.silent_temp = sys.maxsize  # self.silent_temp是上一次能量
                    if temp < self.silent_th and not self.silent_status:
                        # 如果能量<阈值,且不是静音状态
                        self.silent_count += 1
                        self.silent_temp = temp
                    if self.silent_count >= self.silent_count_threshold or len(recorded_data) > 50:
                        if self.log >= 1:
                            print("[wakeup] 停止录制音频...")
                        out_audio = recorded_data[:-self.silent_count_threshold + 1]
                        out_energy = recorded_energy[:-self.silent_count_threshold + 1]
                        if self.log >= 1:
                            print("[wakeup] 录制音频能量: {}".format(out_energy))

                        self.save_audio(self.audio, out_audio, save_file=self.save_record_file)

                        self.mode = 'detect'
                        self.silent_status = True

                        # 音频转文本
                        asr_text = self.tencent_asr.transcribe(self.save_record_file)
                        # print("[wakeup] 音频识别: {}".format(asr_text))
                        if self.log >= 1:
                            print("[wakeup] 音频识别: {}".format(asr_text))

                        if asr_text.count("小犀") == 2:
                            detected_callback()

    def stop(self):
        self._running = False
        logger.info("检测到中断信号，正在停止监听...")
        self.end_streaming()  # 停止监听线程
        logger.info("主程序退出")


if __name__ == '__main__':
    check_audio()
    chat = Chat(
        log=2,
        silent_th=3000,
        mode='detect',
    )
    chat.start_wakeup()
    chat.start_listen()
