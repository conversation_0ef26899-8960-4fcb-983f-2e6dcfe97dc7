2025-07-11 14:13:24.348 - chat_with_robot - chat_with_robot.py - <module> - line 640 - INFO - use_action: dont
2025-07-11 14:13:24.348 - chat_with_robot - chat_with_robot.py - <module> - line 641 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-11 14:13:24.349 - chat_with_robot - chat_with_robot.py - init_websocket - line 319 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1752214404350&accessNonce=9f6b181c-10f8-456b-82b2-606238e0a820&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=26ebbfe3-5e1e-11f0-a881-dc4546c07870&requestId=7f44064b-58cc-49f9-ab74-cd6f510e2756_joyinside&accessSign=02aee358630a02598927cac0219356c5, request_id: 7f44064b-58cc-49f9-ab74-cd6f510e2756_joyinside
2025-07-11 14:13:24.350 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-11 14:13:24.351 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-11 14:13:24.813 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-11 14:13:24.884 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-07-11 14:13:24.913 - chat_with_robot - voice.py - init_wakeup - line 314 - ERROR - 初始化本地流式KWS失败: No module named 'sherpa_onnx'
2025-07-11 14:13:25.914 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-11 14:13:25.914 - chat_with_robot - chat_with_robot.py - play_audio - line 525 - INFO - 使用 audio_action_controller 播放: ./asserts/ding.wav
2025-07-11 14:13:26.975 - chat_with_robot - chat_with_robot.py - play_audio - line 527 - INFO - audio_action_controller 播放完成: ./asserts/ding.wav
2025-07-11 14:13:26.976 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 84 - INFO - Windows 系统，使用 audio_action_controller 播放: asserts/tts/dog_ok.mp3
