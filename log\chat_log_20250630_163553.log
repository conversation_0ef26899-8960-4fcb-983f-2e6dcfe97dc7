2025-06-30 16:35:54.257 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-06-30 16:35:54.257 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-06-30 16:35:54.274 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751272554274&accessNonce=039da59f-9588-4bad-914b-b81a3263566e&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=3c87606a-558d-11f0-8a89-dc4546c07870&requestId=a083748c-323f-41af-a2fb-5566b0d2abbd_joyinside&accessSign=5eeee34c85e134c5ace682703dd468a7, request_id: a083748c-323f-41af-a2fb-5566b0d2abbd_joyinside
2025-06-30 16:35:54.274 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-06-30 16:35:54.274 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-06-30 16:35:54.560 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-06-30 16:35:54.820 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-06-30 16:35:56.249 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-06-30 16:35:56.250 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-06-30 16:35:56.250 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-06-30 16:35:56.250 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-06-30 16:35:56.277 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-06-30 16:35:56.277 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-06-30 16:35:58.402 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:35:58.403 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:35:58.470 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:35:58.470 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:35:59.506 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:35:59.517 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:36:02.603 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天天气怎么样？, 时间戳: 2025-06-30 16:36:08.358000
2025-06-30 16:36:03.811 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:36:09.563000
2025-06-30 16:36:03.812 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1205
2025-06-30 16:36:03.929 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:36:03.933 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:36:03.944 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:36:04.276 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:36:04.284 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:36:04.580 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:36:04.588 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:36:04.914 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:36:04.925 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:36:05.318 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:36:05.319 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:36:05.323 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:36:05.333 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 3c87606a-558d-11f0-8a89-dc4546c07870; requestId: a083748c-323f-41af-a2fb-5566b0d2abbd_joyinside; asr: 今天天气怎么样？; 响应时间: 0; JD机器人回复: 今天北京有雷阵雨，温度在23°到31°之间，体感温度32°，南风3级，相对湿度59%，空气质量一般。出门记得带伞，小心路滑，注意安全！
2025-06-30 16:36:05.333 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:36:05.333 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:36:09.272 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:36:12.495 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:36:12.897 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天北京有雷阵雨，温度在23度到31度之间，体感温度32度，南风三级, 时间戳: 2025-06-30 16:36:18.650000
2025-06-30 16:36:13.986 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:36:13.988 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:36:13.998 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:36:13.998 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:36:13.998 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:36:14.005 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:36:14.009 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 3c87606a-558d-11f0-8a89-dc4546c07870; requestId: a083748c-323f-41af-a2fb-5566b0d2abbd_joyinside; asr: 今天北京有雷阵雨，温度在23度到31度之间，体感温度32度，南风三级; 响应时间: 0; JD机器人回复: 
2025-06-30 16:36:14.009 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:36:14.009 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:36:14.009 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:36:14.098 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:36:14.839 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-06-30 16:36:14.839 - chat_with_robot - voice.py - stop - line 401 - INFO - 已停止local_streaming检测器
