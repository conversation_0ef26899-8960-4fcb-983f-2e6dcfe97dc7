import sys
import threading
import os
import time
import random
import pygame
import yaml
from loguru import logger
import queue

# 可选导入 pymodbus，如果没有安装则禁用 Modbus 功能
try:
    from pymodbus.client import ModbusTcpClient
    MODBUS_AVAILABLE = True
except ImportError:
    logger.warning("pymodbus 未安装，Modbus 控制功能将被禁用")
    ModbusTcpClient = None
    MODBUS_AVAILABLE = False

# 全局音频播放队列和锁
audio_queue = queue.Queue()
audio_lock = threading.Lock()
audio_thread = None
audio_thread_running = False
audio_interrupt_flag = False  # 音频打断标志

format_string = "[{time:HH:mm:ss.SSS}] {level: <8} | {file}:{line} - {message}"

logger.remove()  # 删除默认处理器
logger.add(sys.stderr, format=format_string)
logger.add(
    "logs/file_{time}.log",
    rotation="5 MB",
    retention="7 days",
    format=format_string,
    enqueue=True
)


def optimize_audio_volume(sound, target_volume=0.92):
    """优化音频音量以减少杂音
    针对WAV无损格式优化，可以使用较高音量
    Args:
        sound: pygame.mixer.Sound对象
        target_volume: 目标音量 (0.0-1.0)，默认0.92适合WAV格式
    """
    try:
        # WAV是无损格式，可以使用较高音量，只需避免100%时的削波失真
        sound.set_volume(target_volume)
        logger.info(f"音频音量设置为: {target_volume * 100}% (针对WAV无损格式优化)")
        return True
    except Exception as e:
        logger.error(f"设置音频音量失败: {e}")
        return False


def get_wav_optimization_tips():
    """为WAV格式提供优化建议"""
    tips = [
        "WAV是无损格式，音质最佳",
        "如果仍有杂音，可能是：",
        "1. 音频文件本身录制时有问题",
        "2. 系统音频驱动问题",
        "3. 硬件音频输出问题",
        "4. 音频文件的位深度或采样率与系统不匹配"
    ]
    return tips


def check_audio_format(audio_path):
    """检查音频文件格式并给出优化建议"""
    try:
        file_extension = audio_path.lower().split('.')[-1]
        if file_extension == 'mp3':
            logger.info("检测到MP3文件，已应用针对压缩音频的优化设置")
            return 'mp3'
        elif file_extension in ['wav', 'flac']:
            logger.info("检测到WAV/FLAC无损音频文件，已应用高质量音频优化")
            return 'lossless'
        else:
            logger.info(f"检测到音频格式: {file_extension}")
            return 'other'
    except Exception as e:
        logger.warning(f"无法检测音频格式: {e}")
        return 'unknown'


def init_audio_system():
    """初始化音频系统，优化参数以减少杂音"""
    try:
        # 先退出之前的mixer（如果存在）
        pygame.mixer.quit()

        # 使用高质量音频参数初始化，针对WAV无损格式优化
        # frequency: 44100Hz 匹配您的音频采样率
        # size: -16 表示16位有符号音频
        # channels: 2 立体声匹配您的音频
        # buffer: 2048 适中的缓冲区，WAV格式不需要太大缓冲区
        pygame.mixer.pre_init(frequency=44100, size=-16, channels=2, buffer=2048)
        pygame.mixer.init()

        # 设置混音器的声道数量
        pygame.mixer.set_num_channels(8)

        logger.info("音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)")
        return True
    except Exception as e:
        logger.error(f"音频系统初始化失败: {e}")
        return False


def load_config(config_path="./config/config.yaml"):
    """加载YAML配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
            logger.info(f"成功加载配置文件: {config_path}")
            return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        logger.warning("将使用默认配置")
        return None


# 全局配置 - 写死 Modbus 配置
modbus_ip = "************"
modbus_port = 502
client = None

def try_connect_modbus():
    """尝试连接Modbus服务器"""
    global client
    if not MODBUS_AVAILABLE:
        logger.info("Modbus 功能已禁用（pymodbus 未安装）")
        return False

    try:
        # 连接到Modbus TCP服务器
        client = ModbusTcpClient(modbus_ip, port=modbus_port)
        connection_result = client.connect()
        if connection_result:
            logger.info(f"Modbus 客户端连接成功: {modbus_ip}:{modbus_port}")
            return True
        else:
            logger.error(f"Modbus 客户端连接失败: {modbus_ip}:{modbus_port}")
            logger.error("请检查：1) 机器人是否开机 2) 网络连接是否正常 3) IP地址是否正确")
            client = None
            return False
    except Exception as e:
        logger.error(f"Modbus 客户端连接异常: {e}")
        client = None
        return False

# 初始连接尝试
try_connect_modbus()

def test_modbus_connection():
    """测试Modbus连接并提供诊断信息"""
    logger.info("=== Modbus连接诊断 ===")
    logger.info(f"目标IP: {modbus_ip}")
    logger.info(f"目标端口: {modbus_port}")

    # 测试网络连通性
    import socket
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((modbus_ip, modbus_port))
        sock.close()
        if result == 0:
            logger.info("✓ 网络连接正常，端口可达")
        else:
            logger.error("✗ 网络连接失败，端口不可达")
            logger.error("请检查：1) 机器人是否开机 2) 网络连接 3) IP地址是否正确")
            return False
    except Exception as e:
        logger.error(f"✗ 网络测试异常: {e}")
        return False

    # 测试Modbus连接
    if try_connect_modbus():
        logger.info("✓ Modbus连接成功")
        return True
    else:
        logger.error("✗ Modbus连接失败")
        return False

dataB = []
# 120ms
a = '1031-2'  # 寄存地址以及模式
b = '1032-0'  # 嘴巴动作角度
b1 = '1032-100'  # 嘴巴动作角度
c = '1033-70'  # 嘴巴电机速度
dataB.append(a)
dataB.append(b1)
dataB.append(c)


def start_audio_queue_thread():
    """启动音频队列处理线程"""
    global audio_thread, audio_thread_running

    if audio_thread_running:
        return

    audio_thread_running = True

    def audio_queue_worker():
        global audio_thread_running, audio_interrupt_flag
        while audio_thread_running:
            try:
                # 从队列获取音频任务
                audio_task = audio_queue.get(timeout=1.0)
                if audio_task is None:  # 停止信号
                    break

                audio_path, duration = audio_task
                logger.info(f"队列播放音频: {audio_path}")

                # 重置打断标志
                audio_interrupt_flag = False

                # 实际播放音频（阻塞式，但在独立线程中）
                _play_audio_blocking(audio_path, duration)

                audio_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"音频队列处理出错: {e}")

    audio_thread = threading.Thread(target=audio_queue_worker, daemon=True)
    audio_thread.start()
    logger.info("音频队列处理线程已启动")


def stop_audio_queue_thread():
    """停止音频队列处理线程"""
    global audio_thread_running
    audio_thread_running = False
    audio_queue.put(None)  # 发送停止信号


def interrupt_audio():
    """打断当前音频播放并清空队列"""
    global audio_interrupt_flag

    logger.info("收到音频打断信号")
    audio_interrupt_flag = True

    # 停止当前播放
    try:
        pygame.mixer.stop()
        logger.info("已停止当前音频播放")
    except:
        pass

    # 清空队列
    try:
        while not audio_queue.empty():
            audio_queue.get_nowait()
        logger.info("已清空音频播放队列")
    except:
        pass


def add_audio_to_queue(audio_path, duration=None):
    """将音频添加到播放队列"""
    start_audio_queue_thread()  # 确保线程已启动
    audio_queue.put((audio_path, duration))
    logger.info(f"音频已添加到队列: {audio_path}")


def _play_audio_blocking(audio_path, duration=None):
    """阻塞式播放音频（在独立线程中调用）"""
    try:
        # 初始化音频系统
        if not init_audio_system():
            logger.error("音频系统初始化失败，无法播放音频")
            return False

        # 检查音频格式
        audio_format = check_audio_format(audio_path)

        # 加载音频
        logger.info(f"正在加载音频: {audio_path}")
        sound = pygame.mixer.Sound(audio_path)

        # 根据音频格式优化音量
        if audio_format == 'lossless':
            optimize_audio_volume(sound, 0.92)  # WAV/FLAC无损格式可以使用较高音量
        elif audio_format == 'mp3':
            optimize_audio_volume(sound, 0.85)  # MP3使用较低音量
        else:
            optimize_audio_volume(sound, 0.9)   # 其他格式使用标准音量

        # 获取音频长度
        if duration is None:
            audio_length = sound.get_length()
            logger.info(f"音频长度(从文件获取): {audio_length}秒")
        else:
            audio_length = duration
            logger.info(f"音频长度(从参数获取): {audio_length}秒")

        # 启动动作控制线程
        eyes_thread = threading.Thread(target=eyes_action, args=(1,))
        eyes_thread.daemon = True
        eyes_thread.start()

        mouth_thread = threading.Thread(target=mouth_action_wait, args=(audio_length,))
        mouth_thread.daemon = True

        neck_thread = threading.Thread(target=neck_action_wait, args=(audio_length,))
        neck_thread.daemon = True

        # 播放音频
        channel = sound.play()
        logger.info("开始播放音频")

        # 启动动作控制线程
        try:
            mouth_thread.start()
            logger.info("嘴部动作控制线程已启动")

            neck_thread.start()
            logger.info("脖子动作控制线程已启动")
        except Exception as e:
            logger.error(f"启动动作控制线程失败: {e}")

        # 阻塞等待播放完成（在独立线程中，不影响主线程）
        while channel.get_busy():
            # 检查是否收到打断信号
            if audio_interrupt_flag:
                logger.info("音频播放被打断")
                pygame.mixer.stop()
                break
            pygame.time.wait(100)

        # 等待动作线程完成
        try:
            mouth_thread.join(timeout=2.0)  # 最多等待2秒
            neck_thread.join(timeout=2.0)   # 最多等待2秒
            logger.info("动作控制线程已完成")
        except Exception as e:
            logger.error(f"等待动作线程完成时出错: {e}")

        # 关闭眼睛
        threading.Thread(target=eyes_action, args=(0,), daemon=True).start()

        if not audio_interrupt_flag:
            logger.info("音频播放完成，所有动作已结束")
        else:
            logger.info("音频播放被打断结束，所有动作已停止")

        return True

    except Exception as e:
        logger.error(f"播放音频失败: {e}")
        return False

neck_reg = '1034-0'
neck_reg1 = "1034-100"
neck_reg_speed = "1035-60"


def modbusSender(data):
    global client

    # 如果客户端未连接，尝试重连
    if not client:
        logger.warning(f"Modbus 客户端未连接，尝试重新连接...")
        if not try_connect_modbus():
            logger.warning(f"Modbus 重连失败，跳过寄存器写入: {data}")
            logger.warning(f"请检查Modbus服务器连接状态: {modbus_ip}:{modbus_port}")
            return

    log_messages = []
    try:
        for i, item in enumerate(data):
            arr1, arr2 = item.split('-')
            log_messages.append(f"写入寄存器{arr1}，值为{arr2}")
            result = client.write_register(address=int(arr1), value=int(arr2), no_response_expected=False)
            if hasattr(result, 'isError') and result.isError():
                logger.error(f"写入寄存器{arr1}失败: {result}")
        # 批量记录日志
        if log_messages:
            logger.info("Modbus写入成功: " + ", ".join(log_messages))
    except Exception as e:
        logger.error(f"Modbus发送失败: {e}")
        # 连接可能断开，重置客户端
        client = None
        # 继续执行，不中断程序


def modbusSender_bit(data):
    if not client:
        logger.debug("Modbus 客户端未连接，跳过线圈写入")
        return

    try:
        for i, item in enumerate(data):
            arr1, arr2 = item.split('-')
            logger.info(f"写入寄存器{arr1}，值为{arr2}")
            client.write_coil(address=int(arr1), value=int(arr2), no_response_expected=False)
    except Exception as e:
        logger.error(f"Modbus发送失败: {e}")
        # 继续执行，不中断程序


def mouth_action(secend):
    try:
        sleep_time = int(secend / 0.15)
        logger.info(f"开始嘴部动作，计划执行{sleep_time}次")  # 只在开始时记录一次
        for i in range(sleep_time):
            dataA = []
            b2 = '1032-' + str(random.randint(10, 45))
            if i == sleep_time - 1:
                b2 = '1032-0'
            dataA.append(a)
            dataA.append(b2)
            dataA.append(c)
            modbusSender(dataA)
            time.sleep(0.15)
    except Exception as e:
        logger.error(f"嘴部动作控制失败: {e}")


def mouth_action_wait(secend):
    try:
        logger.info(f"开始嘴巴动作，持续{secend:.2f}秒")
        sleep_time = int(secend / 0.12)  # 计算嘴巴动作次数
        logger.info(f"计划执行{sleep_time}次嘴巴动作")

        for i in range(sleep_time):
            # 检查是否被打断
            if audio_interrupt_flag:
                logger.info("嘴巴动作被打断")
                break

            dataA = []
            b2 = '1032-' + str(random.randint(10, 45))
            dataA.append(a)
            dataA.append(b2)
            dataA.append('1033-100')
            modbusSender(dataA)
            time.sleep(0.12)

    except Exception as e:
        logger.error(f"嘴部动作控制失败: {e}")
    finally:
        # 确保嘴巴最后闭合
        try:
            dataA = []
            dataA.append(a)
            dataA.append('1032-0')  # 闭合嘴巴
            dataA.append('1033-100')
            modbusSender(dataA)
            logger.info("嘴巴动作结束，已闭合")
        except Exception as e:
            logger.error(f"嘴巴闭合失败: {e}")


# 控制眼睛亮，1是亮，0是灭，写入寄存器地址是7999
def eyes_action(bit_type: int):
    result = bit_type
    a = f'7999-{result}'  # 眼睛寄存器地址
    try:
        modbusSender_bit([a])
    except Exception as e:
        logger.error(f"眼睛动作控制失败: {e}")
        # 继续执行，不中断程序
        # pass
        #写一个关于


# 脖子运动  脖子寄存器地址角度1034  速度1035
# neck_reg = '1034-0'
# neck_reg1 = "1034-100"
# neck_reg_speed = "1035-60"
def neck_action(secend):
    try:
        sleep_time = int(secend / 0.15)
        for i in range(sleep_time):
            dataB = []
            neck_reg = '1034-' + str(random.randint(10, 45))
            if i == sleep_time - 1:
                neck_reg = '1034-0'
            dataB.append(neck_reg)
            dataB.append(neck_reg1)
            dataB.append(neck_reg_speed)
            modbusSender(dataB)
            time.sleep(0.15)
    except Exception as e:
        logger.error(f"嘴部动作控制失败: {e}")
        # pass


def neck_action_wait(secend):
    try:
        logger.info(f"开始脖子动作，持续{secend:.2f}秒")
        sleep_time = int(secend / 0.12)  # 计算脖子动作次数
        logger.info(f"计划执行{sleep_time}次脖子动作")

        for i in range(sleep_time):
            # 检查是否被打断
            if audio_interrupt_flag:
                logger.info("脖子动作被打断")
                break

            dataB = []
            neck_reg = '1034-' + str(random.randint(10, 45))
            dataB.append(neck_reg)
            dataB.append(neck_reg1)
            dataB.append(neck_reg_speed)
            modbusSender(dataB)
            time.sleep(0.15)

    except Exception as e:
        logger.error(f"脖子动作控制失败: {e}")
    finally:
        # 确保脖子最后复位
        try:
            dataB = []
            dataB.append('1034-0')  # 复位脖子
            dataB.append(neck_reg1)
            dataB.append(neck_reg_speed)
            modbusSender(dataB)
            logger.info("脖子动作结束，已复位")
        except Exception as e:
            logger.error(f"脖子复位失败: {e}")


# 根据音频长度控制嘴部动作
def play_audio_with_action(audio_path, duration=None):
    """将音频添加到播放队列（非阻塞）"""
    try:
        logger.info(f"添加音频到播放队列: {audio_path}")
        add_audio_to_queue(audio_path, duration)
        return True
    except Exception as e:
        logger.error(f"添加音频到队列失败: {e}")
        return False


def run_predefined_actions(action_sequence):
    """执行预定义的动作序列"""
    try:
        for action in action_sequence:
            if isinstance(action, dict) and 'angle' in action and 'wait_time' in action:
                angle = action['angle']
                wait_time = action['wait_time']
                logger.info(f"执行动作: 角度={angle}, 等待时间={wait_time}秒")
                modbusSender([a, f'1032-{angle}', '1033-100'])
                time.sleep(wait_time)
            elif isinstance(action, (int, float)):
                # 兼容旧格式
                logger.info(f"执行动作: 角度={action}, 使用默认等待时间0.12秒")
                modbusSender([a, f'1032-{action}', '1033-100'])
                time.sleep(0.12)
            else:
                logger.warning(f"忽略无效的动作项: {action}")
    finally:
        logger.info("预定义动作序列结束，关闭嘴巴")
        modbusSender([a, '1032-0', '1033-100'])


def run_actions(action_sequence, audio_length):
    """执行动作控制
    Args:
        action_sequence: 预定义的动作序列，如果为None则使用随机动作
        audio_length: 音频长度，用于随机动作的时间控制
    """
    try:
        if action_sequence:
            run_predefined_actions(action_sequence)
        else:
            # 创建并启动嘴部动作线程
            mouth_thread = threading.Thread(target=mouth_action_wait, args=(audio_length,))
            mouth_thread.daemon = True

            # 创建并启动脖子动作线程
            neck_thread = threading.Thread(target=neck_action_wait, args=(audio_length,))
            neck_thread.daemon = True

            # 启动两个线程
            mouth_thread.start()
            neck_thread.start()

            # 等待两个线程完成
            mouth_thread.join()
            neck_thread.join()
    finally:
        logger.info("动作结束，关闭嘴巴和脖子")
        modbusSender([a, '1032-0', '1033-100'])  # 关闭嘴巴
        modbusSender([neck_reg, neck_reg1, neck_reg_speed])  # 重置脖子位置


def play_idle_audio_with_action(audio_path, duration=None):
    """将音频添加到播放队列（支持预定义动作，非阻塞）"""
    try:
        logger.info(f"添加音频到播放队列（支持预定义动作）: {audio_path}")
        # 注意：预定义动作功能暂时简化，直接使用标准播放
        add_audio_to_queue(audio_path, duration)
        return True
    except Exception as e:
        logger.error(f"添加音频到队列失败: {e}")
        return False




if __name__ == "__main__":
    play_audio_with_action("D:/prooject_code/konglong3_2/yuyin/00001.mp3")
    # play_idle_audio_with_action("D:/prooject_code/konglong3_2/yuyin/00002.mp3")