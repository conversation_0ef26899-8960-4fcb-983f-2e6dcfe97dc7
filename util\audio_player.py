"""
@Author: <EMAIL> <EMAIL>
@Create Date: 2025.03.28
@Description: 4.9版本 with 分段调用 ffplay, 适配chat with robot 4.9版本

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
"""

import os
import sys
import queue
import threading
import time
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from util.logger import logger
from pydub import AudioSegment
from io import BytesIO
import numpy as np
import sounddevice as sd
import pygame
import io
# from utils.unified_audio_controller import play_audio_data  # 不再直接使用
#b282f73fec1d4379adcdbd793cac6220
#b1e786fc954a4ffe9b39078781249e7a

class AudioPlayer:
    """音频播放队列管理器"""
    def __init__(self):
        self.audio_queue = queue.Queue()
        self.is_playing = False
        self.play_thread = None
        self.stop_event = threading.Event()
        self.tasks_completed = threading.Event()
        self.tasks_completed.set()  # 初始状态为已完成
        self.current_process = None  # 当前播放进程

        self.interrupt_flag = False
        # 初始化pygame混音器
        self.pygame = pygame
        self.pygame.mixer.init()

        self.start_time = time.time()
        
    def start(self):
        """启动播放线程"""
        if not self.is_playing:
            self.is_playing = True
            self.stop_event.clear()
            self.tasks_completed.clear()  # 开始新任务时清除完成标志
            self.play_thread = threading.Thread(target=self._play_loop)
            self.play_thread.daemon = True
            self.play_thread.start()
            logger.info("音频播放线程已启动")
    
    def stop(self):
        """停止播放线程"""
        self.is_playing = False
        self.stop_event.set()
        self._interrupt_playback()  # 打断当前播放
        self._clear_queue()  # 清空队列
        if self.play_thread:
            self.play_thread.join()
        logger.info("音频播放线程已停止")
    
    def interrupt(self):
        """立即打断当前播放并清空队列"""
        #self._interrupt_playback()
        self.interrupt_flag = True
        if self.pygame.mixer.music.get_busy():
            self.pygame.mixer.music.stop()
        self._clear_queue()
        while not self.audio_queue.empty():
            self.audio_queue.get_nowait()
            time.sleep(0.1)
        self.interrupt_flag = False
        logger.info("音频播放已打断")
    
    def _interrupt_playback(self):
        """打断当前播放进程"""
        self.interrupt_flag = True
        # if self.current_process and self.current_process.poll() is None:
        #     try:
        #         self.current_process.kill()  # 直接杀死进程
        #     except Exception as e:
        #         logger.error(f"打断播放进程时出错: {e}")
        #     finally:
        #         self.current_process = None
    
    def _clear_queue(self):
        """清空播放队列"""
        with self.audio_queue.mutex:  # 获取队列的锁
            self.audio_queue.queue.clear()  # 清空队列
        self.tasks_completed.set()  # 设置完成标志
        logger.info("队列已清空")
    
    def add_to_queue(self, audio_data: bytes, duration: int = None):
        """添加音频到播放队列"""
        self.tasks_completed.clear()  # 添加新任务时清除完成标志
        self.audio_queue.put((audio_data, duration))
        #logger.info(f"音频已添加到队列，当前队列长度: {self.audio_queue.qsize()}")
    
    def wait_for_completion(self, timeout=None):
        """
        等待所有任务完成
        
        Args:
            timeout: 等待超时时间（秒），None表示无限等待
            
        Returns:
            bool: 如果所有任务完成返回True，否则返回False
        """
        return self.tasks_completed.wait(timeout=timeout)
    
    def _play_loop(self):
        """播放循环"""
        while self.is_playing and not self.stop_event.is_set():
            try:
                # 从队列获取音频数据，设置超时以便能够响应停止信号
                audio_data, duration = self.audio_queue.get(timeout=1.0)

                if audio_data:
                    logger.info("开始播放音频")
                    self._play_single_audio(audio_data, duration)
                    logger.info("音频播放完成")

                    # 标记当前任务完成
                    self.audio_queue.task_done()

                # 检查队列是否为空，如果为空则设置完成标志
                if self.audio_queue.empty():
                    self.tasks_completed.set()
                    logger.info("所有音频任务已完成")

            except queue.Empty:
                # 队列为空时设置完成标志
                self.tasks_completed.set()
                continue
            except Exception as e:
                logger.error(f"播放音频时出错: {e}")
                continue
    
    def _play_single_audio(self, audio_data: bytes, duration: int = None):
        """使用ffplay播放单个音频"""
        try:
            # # 直接使用管道播放

            """pydub --> sounddevice"""
            ## 转换为 pcm
            # print(f"pcm，阻塞采样点...{time.time() - self.start_time:.3f} 秒")
            # audio_segment = AudioSegment.from_file(BytesIO(audio_data),format="mp3")
            # audio_segment = audio_segment.set_frame_rate(16000).set_channels(1).set_sample_width(2)
            # pcm_data = audio_segment.raw_data
            # audio_array = np.frombuffer(pcm_data, dtype=np.int16)
            # print(f"播放音频，非阻塞采样点...{time.time() - self.start_time:.3f} 秒")
            # sd.play(audio_array, samplerate=16000, blocking=True)



            # audio = AudioSegment.from_file(
            #     BytesIO(mp3_data),
            #     format="mp3",
            #     parameters=["-threads", "2"]  # 使用多线程解码
            # )
            
            # # 3. 设置音频参数（如果需要）
            # audio = audio.set_frame_rate(16000).set_channels(1)
            # # 4. 播放
            # play(audio)

            """使用 audio_action_controller 播放音频并控制动作"""
            logger.info("准备播放大模型合成的语音")

            # 如果没有提供duration，尝试快速估算音频长度
            if duration is None or duration == 0:
                try:
                    # 快速估算：根据音频数据大小估算长度（避免完整解码）
                    # MP3平均比特率约128kbps，1秒约16KB
                    estimated_duration = len(audio_data) / (16 * 1024)

                    # 对于小片段（流式播放），使用更短的估算时间
                    if len(audio_data) < 8192:  # 小于8KB，可能是流式片段
                        duration = max(0.1, estimated_duration)  # 最少0.1秒
                        logger.debug(f"流式音频片段估算长度: {duration:.2f}秒")
                    else:
                        duration = max(2.0, min(10.0, estimated_duration))  # 限制在2-10秒范围
                        logger.info(f"完整音频估算长度: {duration:.2f}秒")
                except Exception as e:
                    duration = 0.5 if len(audio_data) < 8192 else 3.0  # 小片段用0.5秒，大文件用3秒

            logger.info(f"音频数据大小: {len(audio_data)} bytes, 持续时间: {duration}")

            try:
                logger.info("使用统一音频控制器播放大模型语音（包含嘴巴动作控制）")
                # 使用统一音频控制器播放，包含动作控制
                from utils.unified_audio_controller import get_audio_controller
                controller = get_audio_controller()
                controller.add_audio_data(audio_data, duration)

                # 等待播放完成
                controller.wait_for_completion()
                logger.info("大模型语音播放完成（包含嘴巴动作）")

            except Exception as e:
                logger.error(f"使用统一音频控制器播放失败，回退到 pygame（无嘴巴动作）: {e}")
                import traceback
                logger.error(f"详细错误信息: {traceback.format_exc()}")

                # 回退到原来的 pygame 方式（无嘴巴动作）
                audio_file = io.BytesIO(audio_data)
                self.pygame.mixer.music.load(audio_file)
                self.pygame.mixer.music.play()
                logger.warning("注意：使用pygame播放，嘴巴不会动作")

                # 等待播放完成
                while self.pygame.mixer.music.get_busy():
                    if self.interrupt_flag:
                        self.pygame.mixer.music.stop()
                        break
                    time.sleep(0.1)
                logger.info("pygame 播放大模型语音完成（无嘴巴动作）")

            """ffplay"""
            # 保存当前进程引用
            #print(f"开始播放音频时长: {time.time() - self.start_time}")
            # print("========= 写入音频数据 =========")
            # self.current_process = subprocess.Popen(
            #     ['ffplay', '-nodisp', '-autoexit', '-i', 'pipe:0'],
            #     stdin=subprocess.PIPE,
            #     stderr=subprocess.DEVNULL,
            #     stdout=subprocess.DEVNULL
            # )
            # self.current_process.stdin.write(audio_data)
            # self.current_process.stdin.close()
            # self.current_process.wait()
            
            # print(f"播放音频时长: {time.time() - self.start_time}")
            # 等待播放完成
            
        except Exception as e:
            logger.error(f"播放单个音频时出错: {e}")
        #     if self.current_process:
        #         self.current_process.kill()
        # finally:
        #     if self.current_process and self.current_process.poll() is None:
        #         self.current_process.kill()
        #     self.current_process = None



if __name__ == "__main__":
    audio_player = AudioPlayer()
    audio_player.start()
    audio_player.add_to_queue(b"1234567890")
    audio_player.stop()