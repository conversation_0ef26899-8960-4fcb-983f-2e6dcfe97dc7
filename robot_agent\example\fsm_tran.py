import py_magicdog_sdk
import numpy as np

if __name__ == '__main__':
  io_interface = py_magicdog_sdk.DataCmdExchange()
  data = py_magicdog_sdk.InputData()
  data.clear()
  cmd = py_magicdog_sdk.OutputData()
  cmd.clear()

  cnt = 0
  dt = 0.4
  first_enter = True
  first_step1 = True

  while True:
    t_start = io_interface.initDuration()

    # wait for connection
    if(not io_interface.isConnect()):
      print("Building connection ...\n")
      io_interface.waitDuration(t_start, dt)
      continue
    
    # receive data
    io_interface.receiveData(data)

    # ensure the initial state is Passive
    if (first_enter and data.robot_fsm_data!= 0):
      cmd.robot_fsm_cmd = 0
      io_interface.sendCmd(cmd)
      io_interface.waitDuration(t_start, 1.)
      
      # check
      io_interface.receiveData(data)
      first_enter = (data.robot_fsm_data!= 0)
      
      if (first_enter):
        continue

    cnt += 1
    if (cnt<20):
      if(first_step1):
        first_step1 = False
        print("Enter into RecoveryStand FSM!")

      cmd.robot_fsm_cmd = 2

    else:
      cmd.robot_fsm_cmd = 3 # jump high
      cmd.motion_cmd.body_height = 0.3


    io_interface.sendCmd(cmd)
    io_interface.waitDuration(t_start, dt)