2025-07-01 16:14:26.162 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-07-01 16:14:26.163 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-01 16:14:26.182 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751357666183&accessNonce=b8b2b90b-3780-4d6d-96f7-14e577a194ee&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=672e0166-5653-11f0-a395-dc4546c07870&requestId=34879337-83e2-4f25-948c-94a12f7187d2_joyinside&accessSign=593322aa390a5b3730c2722ba432b3f6, request_id: 34879337-83e2-4f25-948c-94a12f7187d2_joyinside
2025-07-01 16:14:26.183 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-01 16:14:26.183 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-01 16:14:26.769 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-01 16:14:26.868 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-07-01 16:14:28.325 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-01 16:14:28.326 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-07-01 16:14:28.326 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-07-01 16:14:28.326 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-07-01 16:14:28.386 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-01 16:14:28.386 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-07-01 16:14:31.466 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 16:14:31.466 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 16:14:31.531 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 16:14:31.532 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 16:14:32.854 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 16:14:32.864 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 16:14:35.220 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你会什么技能？, 时间戳: 2025-07-01 16:14:41.360000
2025-07-01 16:14:35.529 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 16:14:35.530 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 16:14:35.532 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 672e0166-5653-11f0-a395-dc4546c07870; requestId: 34879337-83e2-4f25-948c-94a12f7187d2_joyinside; asr: 你会什么技能？; 响应时间: 0; JD机器人回复: 
2025-07-01 16:14:35.532 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 16:14:35.532 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 16:14:35.532 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 16:14:37.587 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 16:14:41.439 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 什么？, 时间戳: 2025-07-01 16:14:47.577000
2025-07-01 16:14:41.725 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 16:14:41.727 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 16:14:41.732 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 672e0166-5653-11f0-a395-dc4546c07870; requestId: 34879337-83e2-4f25-948c-94a12f7187d2_joyinside; asr: 什么？; 响应时间: 0; JD机器人回复: 
2025-07-01 16:14:41.732 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 16:14:41.732 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 16:14:41.732 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 16:14:53.684 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 16:14:53.684 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 16:14:53.747 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 16:14:53.747 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 16:14:55.060 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 16:14:55.123 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 16:14:55.784 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 16:14:59.181 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想知道有关台独的问题, 时间戳: 2025-07-01 16:15:05.321000
2025-07-01 16:15:00.332 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 16:15:06.468000
2025-07-01 16:15:00.332 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1147
2025-07-01 16:15:00.431 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:15:00.441 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:15:00.441 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:15:00.735 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:15:00.746 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:15:01.041 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:15:01.052 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:15:01.341 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:15:01.343 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:15:01.762 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:15:01.762 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 16:15:01.765 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:15:01.776 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 672e0166-5653-11f0-a395-dc4546c07870; requestId: 34879337-83e2-4f25-948c-94a12f7187d2_joyinside; asr: 我想知道有关台独的问题; 响应时间: 0; JD机器人回复: 关于台湾问题，我们始终坚持一个中国原则，台湾是中国不可分割的一部分。我们坚决反对任何形式的“台独”分裂行径，支持国家统一大业。我们坚信，在党的领导下，实现祖国的完全统一是历史的必然趋势。
2025-07-01 16:15:01.776 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 16:15:01.776 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 16:15:04.470 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:15:07.192 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:15:10.618 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:15:13.640 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:15:18.680 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 16:15:18.680 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 16:15:48.462 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 16:15:48.462 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 16:19:30.577 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 16:19:30.578 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 16:34:25.907 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 16:34:25.908 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 16:35:12.247 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-07-01 16:35:18.378000
2025-07-01 16:35:14.796 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 16:35:20.929000
2025-07-01 16:35:14.797 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 2551
2025-07-01 16:35:14.861 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:35:14.869 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:35:14.880 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:35:15.164 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:35:15.168 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:35:15.504 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:35:15.513 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:35:15.792 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:35:15.802 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:35:16.336 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:35:16.337 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 16:35:16.342 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:35:16.353 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 672e0166-5653-11f0-a395-dc4546c07870; requestId: 34879337-83e2-4f25-948c-94a12f7187d2_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 关于台湾问题，我们始终坚持一个中国原则，台湾是中国不可分割的一部分。我们坚决反对任何形式的“台独”分裂行径，支持国家统一大业。我们坚信，在党的领导下，实现祖国的完全统一是历史的必然趋势。
2025-07-01 16:35:16.353 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 16:35:16.353 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 16:35:19.020 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:35:21.639 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:35:25.067 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:35:27.989 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:35:33.026 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 16:35:33.026 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 16:37:33.335 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
