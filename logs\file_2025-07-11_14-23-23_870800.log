[14:23:23.876] INFO     | audio_action_controller.py:136 - Modbus 功能已禁用（pymodbus 未安装）
[14:23:27.697] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[14:23:27.698] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[14:23:27.699] INFO     | audio_action_controller.py:292 - 正在加载音频: ./asserts/ding.wav
[14:23:27.699] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[14:23:27.700] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.9675963521003723秒
[14:23:27.700] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:23:27.701] INFO     | audio_action_controller.py:327 - 开始播放音频
[14:23:27.701] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[14:23:27.702] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:27.702] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[14:23:27.702] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:27.824] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:27.854] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:27.945] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:28.005] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:28.066] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:28.156] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:28.187] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:28.307] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:28.308] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:28.429] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:28.458] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:28.550] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:28.609] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:28.711] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:23:28.712] INFO     | audio_action_controller.py:348 - 音频播放完成
[14:23:28.760] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:28.874] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[14:23:28.875] INFO     | audio_action_controller.py:67 - 检测到MP3文件，已应用针对压缩音频的优化设置
[14:23:28.876] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/tts/dog_ok.mp3
[14:23:28.881] INFO     | audio_action_controller.py:42 - 音频音量设置为: 85.0% (针对WAV无损格式优化)
[14:23:28.881] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 4.439773082733154秒
[14:23:28.881] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:23:28.883] INFO     | audio_action_controller.py:327 - 开始播放音频
[14:23:28.883] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[14:23:28.883] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:28.884] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:28.884] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[14:23:29.006] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:29.035] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:29.127] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:29.186] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:29.249] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:29.337] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:29.370] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:29.488] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:29.491] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:29.612] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:29.639] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:29.733] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:29.791] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:29.853] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:29.942] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:29.975] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:30.093] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:30.096] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:30.216] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:30.245] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:30.337] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:30.396] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:30.458] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:30.547] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:30.579] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:30.699] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:30.700] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:30.821] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:30.851] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:30.942] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:31.002] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:31.064] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:31.153] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:31.184] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:31.304] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:31.305] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:31.426] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:31.455] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:31.547] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:31.606] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:31.669] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:31.757] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:31.790] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:31.909] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:31.911] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:32.031] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:32.059] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:32.152] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:32.210] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:32.273] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:32.361] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:32.394] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:32.512] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:32.515] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:32.638] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:32.663] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:32.759] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:32.815] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:32.880] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:32.966] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:33.001] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:33.117] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:33.122] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:33.268] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:33.310] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:23:33.310] INFO     | audio_action_controller.py:348 - 音频播放完成
[14:23:33.419] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:33.570] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:33.722] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:33.872] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:34.024] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:34.175] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:34.977] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[14:23:34.977] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[14:23:34.979] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/shenmeshi.wav
[14:23:35.131] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[14:23:35.131] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.7745804786682129秒
[14:23:35.132] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:23:35.132] INFO     | audio_action_controller.py:327 - 开始播放音频
[14:23:35.133] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[14:23:35.133] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:35.134] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[14:23:35.134] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:35.255] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:35.285] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:35.376] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:35.436] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:35.496] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:35.587] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:35.617] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:35.738] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:35.738] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:35.889] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:35.940] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:23:35.940] INFO     | audio_action_controller.py:348 - 音频播放完成
[14:23:40.617] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[14:23:40.619] INFO     | audio_action_controller.py:67 - 检测到MP3文件，已应用针对压缩音频的优化设置
[14:23:40.619] INFO     | audio_action_controller.py:292 - 正在加载音频: temp_tts_audio.mp3
[14:23:40.622] INFO     | audio_action_controller.py:42 - 音频音量设置为: 85.0% (针对WAV无损格式优化)
[14:23:40.622] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 2.523650884628296秒
[14:23:40.623] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:23:40.623] INFO     | audio_action_controller.py:327 - 开始播放音频
[14:23:40.623] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:40.623] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[14:23:40.625] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[14:23:40.625] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:40.745] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:40.776] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:40.866] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:40.927] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:40.987] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:41.079] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:41.108] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:41.228] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:41.230] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:41.349] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:41.381] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:41.470] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:41.532] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:41.591] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:41.683] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:41.713] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:41.834] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:41.834] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:41.955] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:41.985] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:42.075] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:42.135] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:42.199] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:42.286] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:42.320] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:42.437] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:42.441] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:42.562] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:42.588] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:42.683] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:42.739] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:42.803] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:42.890] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:42.924] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:43.041] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:43.046] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:43.192] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:43.241] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:23:43.241] INFO     | audio_action_controller.py:348 - 音频播放完成
[14:23:43.426] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:43.427] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[14:23:43.428] INFO     | audio_action_controller.py:67 - 检测到MP3文件，已应用针对压缩音频的优化设置
[14:23:43.428] INFO     | audio_action_controller.py:292 - 正在加载音频: temp_tts_audio.mp3
[14:23:43.431] INFO     | audio_action_controller.py:42 - 音频音量设置为: 85.0% (针对WAV无损格式优化)
[14:23:43.431] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 2.235691547393799秒
[14:23:43.431] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:23:43.431] INFO     | audio_action_controller.py:327 - 开始播放音频
[14:23:43.432] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[14:23:43.432] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:43.432] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:43.432] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[14:23:43.553] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:43.578] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:43.584] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:43.675] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:43.729] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:43.735] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:43.796] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:43.886] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:43.917] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:44.037] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:44.038] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:44.158] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:44.188] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:44.279] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:44.339] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:44.399] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:44.490] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:44.520] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:44.641] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:44.641] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:44.762] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:44.792] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:44.883] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:44.944] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:45.004] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:45.095] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:45.125] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:45.246] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:45.246] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:45.367] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:45.397] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:45.488] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:45.548] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:45.700] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:45.750] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:23:45.750] INFO     | audio_action_controller.py:348 - 音频播放完成
[14:23:45.906] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[14:23:45.906] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:45.907] INFO     | audio_action_controller.py:67 - 检测到MP3文件，已应用针对压缩音频的优化设置
[14:23:45.907] INFO     | audio_action_controller.py:292 - 正在加载音频: temp_tts_audio.mp3
[14:23:45.913] INFO     | audio_action_controller.py:42 - 音频音量设置为: 85.0% (针对WAV无损格式优化)
[14:23:45.913] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 4.3593878746032715秒
[14:23:45.913] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:23:45.913] INFO     | audio_action_controller.py:327 - 开始播放音频
[14:23:45.914] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[14:23:45.914] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:45.915] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[14:23:45.915] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:46.036] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:46.058] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:46.067] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:46.157] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:46.217] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:46.278] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:46.368] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:46.399] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:46.520] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:46.520] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:46.641] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:46.671] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:46.761] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:46.821] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:46.883] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:46.972] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:47.004] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:47.123] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:47.125] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:47.246] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:47.274] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:47.367] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:47.425] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:47.488] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:47.576] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:47.609] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:47.727] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:47.730] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:47.851] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:47.878] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:47.972] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:48.029] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:48.092] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:48.179] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:48.213] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:48.332] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:48.335] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:48.455] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:48.484] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:48.576] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:48.635] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:48.697] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:48.786] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:48.820] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:48.936] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:48.941] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:49.062] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:49.087] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:49.183] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:49.238] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:49.304] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:49.389] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:49.425] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:49.540] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:49.546] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:49.667] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:49.691] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:49.787] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:49.842] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:49.908] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:49.993] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:50.029] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:50.144] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:50.150] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:50.295] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:50.344] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:23:50.344] INFO     | audio_action_controller.py:348 - 音频播放完成
[14:23:50.529] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:50.529] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[14:23:50.530] INFO     | audio_action_controller.py:67 - 检测到MP3文件，已应用针对压缩音频的优化设置
[14:23:50.530] INFO     | audio_action_controller.py:292 - 正在加载音频: temp_tts_audio.mp3
[14:23:50.534] INFO     | audio_action_controller.py:42 - 音频音量设置为: 85.0% (针对WAV无损格式优化)
[14:23:50.534] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 2.091700792312622秒
[14:23:50.535] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:23:50.535] INFO     | audio_action_controller.py:327 - 开始播放音频
[14:23:50.536] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[14:23:50.536] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:50.536] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:50.536] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[14:23:50.657] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:50.681] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:50.687] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:50.778] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:50.832] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:50.838] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:50.899] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:50.984] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:50.989] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:51.020] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:51.134] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:51.140] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:51.141] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:51.262] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:51.285] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:51.291] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:51.384] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:51.441] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:51.506] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:51.592] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:51.627] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:51.744] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:51.748] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:51.869] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:51.895] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:51.990] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:52.046] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:52.111] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:52.197] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:52.232] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:52.348] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:52.353] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:52.474] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:52.500] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:52.650] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:23:52.651] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:23:52.651] INFO     | audio_action_controller.py:348 - 音频播放完成
