import time
SKILL_INTERVAL = 0.5
import sys
import os
import json  # 跟随
#from std_msgs.msg import String  # 跟随
import threading
import simpleaudio as sa

from .utils.robot_controller_utils import COMPATIBLE_STANDING_STATES, STATE_DOWN, STATE_STAND_LOW, STATE_STAND_MEDIUM, STATE_STAND_HIGH

from .robot_meticulous_control import RobotMeticulousController

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from util.logger import logger

class MagicdogController:
    def __init__(self, commander):
        self.skill_id_to_name = {
            1: "前进",
            2: "后退",
            3: "左平移",
            4: "右平移",
            5: "左转",  # 90度
            6: "右转",  # 90度
            7: "起立",
            8: "坐下",
            9: "趴下",
            10: "向前跳",
            11: "打招呼",
            12: "扭身体",
            13: "跳个舞",
            14: "走两步",
            15: "跑两步",
            16: "向左转圈",  # 360度
            17: "向右转圈",  # 360度
            18: "扭秧歌",
            19: "摇尾巴撒娇",
            20: "找尾巴",
            21: "扑爪要抱",
            22: "边走边观察",  # 探索环境
            23: "抬头",  # 特殊-唤醒动作
            24: "左转一点点",
            25: "右转一点点",
            26: "悠闲",  # 心情
            27: "兴奋",  # 心情
            28: "停止运动",
            29: "沮丧",  # 心情
            30: "倾听",
            31: "关闭遇到障碍停止模式",  # 模式
            32: "开启跟随模式",  # 模式
            33: "开启遇到障碍停止模式",  # 模式
            34: "测试",
            35: "低速模式",
            36: "中速模式",
            37: "高速模式",
            38: "关闭跟随模式", 
            39: "开启自主模式",
            40: "开启手动模式",
            41: "扭身跳",
            42: "舞蹈"
        }
        self.commander = commander
        # self.ros_commander = ros_commander
        self.stop_flag = False  # 添加停止标志
        # 跟随模式监控线程
        self.follow_monitor_thread = None
        self.follow_monitor_running = False
        self.current_direction = 0 

    @property
    def check_stop(self):
        """检查是否需要停止当前动作"""
        return self.stop_flag

    def stop(self):
        """停止当前动作"""
        self.stop_flag = True
        self.commander.run("停止移动")
        #time.sleep(0.025)
        # self.commander.run("停止平移")
        # time.sleep(0.025)
        # self.commander.run("停止转向")
        # time.sleep(0.025)
        # self.commander.run("停止编排动作")
        # time.sleep(0.025)
        # self.commander.run("关闭所有AI选项")
        time.sleep(0.025)
    
        # self.turn_off_follow()
        # self.execute_skill_turn_off_follow()
        # 改至 voice.voice.detect_callback 中

    
    def control_robot(self, skill_id):
        if skill_id in self.skill_id_to_name:
            skill_name = self.skill_id_to_name[skill_id]
            logger.info(f"机器狗执行动作：{skill_name}")
        else:
            logger.info("机器狗无法执行该动作")
            return
        
        # 重置停止标志
        self.stop_flag = False
        # 如果是停止命令，先设置停止标志
        if skill_id == 28:
            self.stop_flag = True

        if skill_id == 1:
            self.execute_skill_go_forward()
        elif skill_id == 2:
            self.execute_skill_go_backward()
        elif skill_id == 3:
            self.execute_skill_go_left()
        elif skill_id == 4:
            self.execute_skill_go_right()
        elif skill_id == 5:
            self.execute_skill_turn_left()
        elif skill_id == 6:
            self.execute_skill_turn_right()
        elif skill_id == 7:
            self.execute_skill_stand_up()
        elif skill_id == 8:
            self.execute_skill_sit_down()
        elif skill_id == 9:
            self.execute_skill_lie_down()
        elif skill_id == 10:
            self.execute_skill_jump_forward()
        elif skill_id == 11:
            self.execute_skill_say_hello()
        elif skill_id == 12:
            self.execute_skill_twist_body()
        elif skill_id == 13:
            self.execute_skill_dance()
        elif skill_id == 14:
            self.execute_skill_walk_two_steps()
        elif skill_id == 15:
            self.execute_skill_run_two_steps()
        elif skill_id == 16:
            self.execute_skill_turn_left_circle()
        elif skill_id == 17:
            self.execute_skill_turn_right_circle()
        elif skill_id == 18:
            self.execute_skill_twist_yangge()
        elif skill_id == 19:
            self.execute_skill_shake_tail()
        elif skill_id == 20:
            self.execute_skill_find_tail()
        elif skill_id == 21:
            self.execute_skill_paw_to_hug()
        elif skill_id == 22:
            self.execute_skill_explore_environment()
        elif skill_id == 23:
            self.execute_skill_raise_head()
        elif skill_id == 24:
            self.execute_skill_left_turn_a_bit()
        elif skill_id == 25:
            self.execute_skill_right_turn_a_bit()
        elif skill_id == 26:
            self.execute_skill_relax()
        elif skill_id == 27:
            self.execute_skill_excited()
        elif skill_id == 28:
            self.execute_skill_stop()
        elif skill_id == 29:
            self.execute_skill_depressed()
        elif skill_id == 30:
            self.execute_skill_listen()
        elif skill_id == 31:
            self.execute_skill_turn_off_obstacle_stop()
        elif skill_id == 32:
            self.execute_skill_follow()
        elif skill_id == 33:
            self.execute_skill_turn_on_obstacle_stop()
        elif skill_id == 34:
            self.execute_skill_test()
        elif skill_id == 35:
            self.execute_skill_low_speed()
        elif skill_id == 36:
            self.execute_skill_medium_speed()
        elif skill_id == 37:
            self.execute_skill_high_speed()
        elif skill_id == 38:
            self.execute_skill_turn_off_follow()
        elif skill_id == 39:
            self.execute_skill_turn_on_autonomous()
        elif skill_id == 40:
            self.execute_skill_turn_on_manual()
        elif skill_id == 41:
            self.execute_skill_turn_body_jump()
        elif skill_id == 42:
            self.execute_skill_dance_with_music()
    
    ### 状态等待 ###
    def wait_for_state(self, target_state_or_list, max_wait_time=1, log_interval=2):
        """
        等待机器狗状态变为指定状态或状态列表中的任何一个状态

        参数:
            target_state_or_list (str | list): 期望的机器人状态(字符串)或状态列表
            max_wait_time (int): 最大等待时间(秒)
            log_interval (int): 日志输出间隔(秒)

        返回:
            bool: 是否成功达到目标状态
        """
         
        return True

    def within_state_wait_for_state(self, target_state_or_list, max_wait_time=15, log_interval=2):
        """
        在状态中等待机器狗状态变为指定状态或状态列表中的任何一个状态
        (与 wait_for_state 类似，但不包含初始的状态转换逻辑，用于动作执行过程中的等待)

        参数:
            target_state_or_list (str | list): 期望的机器人状态(字符串)或状态列表
            max_wait_time (int): 最大等待时间(秒)
            log_interval (int): 日志输出间隔(秒)

        返回:
            bool: 是否成功达到目标状态
        """
        start_time = time.time()
        wait_count = 0

        # 确定目标状态是单个字符串还是列表
        target_states = []
        log_target_str = ""
        if isinstance(target_state_or_list, str):
            target_states = [target_state_or_list]
            log_target_str = target_state_or_list
        elif isinstance(target_state_or_list, list):
            target_states = target_state_or_list
            log_target_str = f"任何一个状态 {target_states}"
        else:
            logger.error(f"无效的目标状态类型: {type(target_state_or_list)}")
            return False

        while self.commander.robot_monitor.robot_state not in target_states:
            # 检查是否有停止命令
            if self.check_stop:
                logger.info("检测到停止命令，中断当前状态等待")
                return False

            time.sleep(0.01) # 保持较短的 sleep 时间
            wait_count += 1

            # 每隔log_interval秒输出一次日志
            if wait_count % (log_interval * 100) == 0: 
                logger.info(f"等待机器人达到 {log_target_str}，当前状态: {self.commander.robot_monitor.robot_state}")

            # 超时检查
            if time.time() - start_time > max_wait_time:
                logger.warning(f"在状态中等待机器人达到 {log_target_str} 状态超时")
                return False

        logger.info(f"机器人已在状态中达到目标状态: {self.commander.robot_monitor.robot_state} (期望: {log_target_str})")
        return True
        
    ### 技能执行 ###
    def execute_skill_go_forward(self):  # 1
        """
        skill 1. 前进
            1. 等待机器狗到可以前进的状态 (兼容低中高速站立)
            2. 执行"前进"
        """
        # 1. 等待机器狗到可以前进的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        # 如果 wait_for_state 返回 False，函数会使用 return 直接结束当前技能的执行

        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 执行"前进"
        self.commander.run("移动模式")
        if self.check_stop:
            return
        self.commander.run("前进(中速度)")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)
    
    def execute_skill_go_backward(self):  # 2
        """
        skill 2. 后退
            1. 等待机器狗到可以后退的状态
            2. 执行"后退"
        """
        # 1. 等待机器狗到可以后退的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 执行"后退"
        self.commander.run("移动模式")
        if self.check_stop:
            return
        self.commander.run("后退(中速度)")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)
    
    def execute_skill_go_left(self, interval=0.24):  # 3
        """
        skill 3. 左平移
            1. 等待机器狗到可以左平移的状态
            2. 执行"左平移"
        """
        # 1. 等待机器狗到可以左平移的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 执行"左平移"
        self.commander.run("移动模式")
        if self.check_stop:
            return
        self.commander.run("左平移(中速度)")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return
        self.commander.run("左平移(中速度)")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return
        self.commander.run("左平移(中速度)")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)
    
    def execute_skill_go_right(self, interval=0.24):  # 4
        """
        skill 4. 右平移
            1. 等待机器狗到可以右平移的状态
            2. 执行"右平移"
        """
        # 1. 等待机器狗到可以右平移的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 执行"右平移"
        self.commander.run("移动模式")
        if self.check_stop:
            return
        self.commander.run("右平移(中速度)")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return
        self.commander.run("右平移(中速度)")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return
        self.commander.run("右平移(中速度)")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)
    
    def execute_skill_turn_left(self, interval=0.24):  # 5
        """
        skill 5. 左转
            1. 等待机器狗到可以左转的状态
            2. 执行"左转"
        """
        # 1. 等待机器狗到可以左转的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 执行"左转"
        self.commander.run("移动模式")
        if self.check_stop:
            return
        self.commander.run("左转(中速度)")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return
        self.commander.run("左转(中速度)")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return
        self.commander.run("左转(中速度)")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return
        self.commander.run("左转(中速度)")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)
    
    def execute_skill_turn_right(self, interval=0.24):  # 6
        """
        skill 6. 右转
            1. 等待机器狗到可以右转的状态
            2. 执行"右转"
        """
        # 1. 等待机器狗到可以右转的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 执行"右转"
        self.commander.run("移动模式")
        if self.check_stop:
            return
        self.commander.run("右转(中速度)")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return
        self.commander.run("右转(中速度)")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return
        self.commander.run("右转(中速度)")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return
        self.commander.run("右转(中速度)")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)
    
    def execute_skill_stand_up(self):  # 7
        """
        skill 7. 起立
            1. 等待机器狗到可以起立的状态
            2. 执行"起立"
        """
        # 1. 等待机器狗到可以起立的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # # 2. 执行"起立"
        self.commander.run("起立")
        time.sleep(SKILL_INTERVAL)

    def execute_skill_sit_down(self):  # 8
        """
        skill 8. 坐下
            1. 等待机器狗到可以坐下的状态
            2. 执行"坐下"
        """
        # 1. 等待机器狗到可以坐下的状态
        if not self.wait_for_state(STATE_DOWN):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 执行"坐下"
        # self.commander.run("起立/趴下")
        time.sleep(SKILL_INTERVAL)
        
    def execute_skill_lie_down(self):  # 9
        """
        skill 9. 趴下
            1. 等待机器狗到可以趴下的状态
            2. 执行"趴下"
        """
        # 1. 等待机器狗到可以趴下的状态
        if not self.wait_for_state(STATE_DOWN):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # # 2. 执行"趴下"
        self.commander.run("起立/趴下")
        time.sleep(SKILL_INTERVAL)
    
    def execute_skill_jump_forward(self):  # 10
        """
        skill 10. 向前跳
            1. 等待机器狗到可以向前跳的状态
            2. 执行"向前跳"
        """
        # 1. 等待机器狗到可以向前跳的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 执行"向前跳"
        self.commander.run("向前跳")
        if self.check_stop:
            return
        time.sleep(2.5)

    def execute_skill_say_hello(self):  # 11
        """
        skill 11. 打招呼
            1. 等待机器狗到可以打招呼的状态
            2. 执行"打招呼"
        """
        # 1. 等待机器狗到可以打招呼的状态
        self.wait_for_state(COMPATIBLE_STANDING_STATES)

        # 2. 执行"打招呼"
        self.commander.run("打招呼")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)

    def execute_skill_twist_body(self):  # 12
        """
        skill 12. 扭身体
            1. 等待机器狗到可以扭身体的状态
            2. 执行"扭身体"
        """
        # 1. 等待机器狗到可以扭身体的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 执行"扭身体"
        self.commander.run("扭身体")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)

    def execute_skill_dance(self):  # 13
        """
        skill 13. 跳个舞
            1. 等待机器狗到可以跳舞的状态
            2. 执行"跳舞"
        """
        # 1. 等待机器狗到可以跳舞的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 执行"扭身体"
        self.commander.run("扭身体")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)
    
    def execute_skill_walk_two_steps(self, interval=0.24):  # 14
        """
        skill 14. 走两步
            1. 等待机器狗到可以走两步的状态
            2. 执行"走两步"
        """
        # 间隔时间短+多段==短时间走很远 0.049
        # 间隔时间长+少段==长时间/整体走很远 0.5
        # 两种避障都不太好，但后者比较自然
        # 经测试，0.24(不低于250ms)的间隔能使动作延续性更好(不中断) eg. 左转，如超过250ms，会变为两次左转，虽然踏步不中断，但动作不连贯

        # 1. 等待机器狗到可以走两步的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 执行"走两步"
        self.commander.run("移动模式")
        if self.check_stop:
            return
        self.commander.run("前进(中速度)")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return
        self.commander.run("前进(中速度)")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return
        self.commander.run("前进(中速度)")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)

    def execute_skill_run_two_steps(self, interval=0.24):  # 15
        """
        skill 15. 跑两步
            1. 等待机器狗到可以跑两步的状态
            2. 执行"跑两步"
        """
        # 1. 等待机器狗到可以跑两步的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 执行"跑两步"
        self.commander.run("移动模式")
        if self.check_stop:
            return
        self.commander.run("前进(高速度)")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return
        self.commander.run("前进(高速度)")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return
        self.commander.run("前进(高速度)")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)
    
    def execute_skill_turn_left_circle(self, interval=0.24):  # 16
        """
        skill 16. 向左转圈
            1. 等待机器狗到可以向左转圈的状态
            2. 执行"向左转圈"
        """
        # 1. 等待机器狗到可以向左转圈的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 执行"向左转圈"
        #    15次"左转(中速度)" == 90+90+45度
        self.commander.run("移动模式")
        if self.check_stop:
            return
        for i in range(24):
            self.commander.run("左转(中速度)")
            if self.check_stop:
                return
            time.sleep(interval)
            if self.check_stop:
                return
        self.commander.run("左转(中速度)")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)
    
    def execute_skill_turn_right_circle(self, interval=0.24):  # 17
        """
        skill 17. 向右转圈
            1. 等待机器狗到可以向右转圈的状态
            2. 执行"向右转圈"
        """
        # 1. 等待机器狗到可以向右转圈的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 执行"向右转圈"
        self.commander.run("移动模式")
        if self.check_stop:
            return
        for i in range(24):
            self.commander.run("右转(中速度)")
            if self.check_stop:
                return
            time.sleep(interval)
            if self.check_stop:
                return
        self.commander.run("右转(中速度)")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)
    
    def execute_skill_twist_yangge(self, interval=0.24):  # 18
        """
        skill 18. 扭秧歌
            1. 等待机器狗到可以扭秧歌的状态
            2. 左平移 → 右平移 → 扭身体/摇尾巴
        """
        # 1. 等待机器狗到可以扭秧歌的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 左平移 → 右平移 → 扭身体/摇尾巴
        self.commander.run("移动模式")
        if self.check_stop:
            return

        for i in range(6):
            self.commander.run("左平移(中速度)")
            if self.check_stop:
                return
            time.sleep(interval)
            if self.check_stop:
                return

        # if not self.within_state_wait_for_state("力控状态（静止站立）且步态为平地低速步态"):
        #     return

        # if self.check_stop:
        #     return
        
        for i in range(6):
            self.commander.run("右平移(中速度)")
            if self.check_stop:
                return
            time.sleep(interval)
            if self.check_stop:
                return

        # if not self.within_state_wait_for_state("力控状态（静止站立）且步态为平地低速步态"):
        #     return

        # if self.check_stop:
        #     return

        # self.commander.run("扭身体")
        # if self.check_stop:
        #     return
        # time.sleep(SKILL_INTERVAL)
        self.execute_skill_shake_tail()

    def execute_skill_shake_tail(self, interval=0.02):  # 19
        """
        skill 19. 摇尾巴撒娇 - 缩短 interval 使动作更丝滑
            1. 等待机器狗到可以摇尾巴的状态
            2. （扭身体 → 趴下 → 起立）执行横滚角摆动来模拟摇尾巴的动作
        """
        # 1. 等待机器狗到可以摇尾巴的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 执行横滚角摆动
        self.commander.run("原地模式")
        if self.check_stop:
            return

        # 向左摆动序列（从小到大）
        left_commands = [
            "调整偏航角(原地向左)-幅度1.0",   # -9553
            "调整偏航角(原地向左)-幅度2.0",   # -11870
            "调整偏航角(原地向左)-幅度3.0",   # -14191
            "调整偏航角(原地向左)-幅度4.0",   # -16510
            "调整偏航角(原地向左)-幅度5.0",   # -18830
            "调整偏航角(原地向左)-幅度6.0",   # -21150
            "调整偏航角(原地向左)-幅度7.0",   # -23470
            "调整偏航角(原地向左)-幅度8.0",   # -25790
            "调整偏航角(原地向左)-幅度9.0",   # -28110
            "调整偏航角(原地向左)-幅度10.0",  # -30430
            "调整偏航角(原地向左)-幅度11.0",  # -32767
        ]
        
        # 向右摆动序列（从小到大）
        right_commands = [
            "调整偏航角(原地向右)-幅度1.0",   # 9553
            "调整偏航角(原地向右)-幅度2.0",   # 11870
            "调整偏航角(原地向右)-幅度3.0",   # 14191
            "调整偏航角(原地向右)-幅度4.0",   # 16510
            "调整偏航角(原地向右)-幅度5.0",   # 18830
            "调整偏航角(原地向右)-幅度6.0",   # 21150
            "调整偏航角(原地向右)-幅度7.0",   # 23470
            "调整偏航角(原地向右)-幅度8.0",   # 25790
            "调整偏航角(原地向右)-幅度9.0",   # 28110
            "调整偏航角(原地向右)-幅度10.0",  # 30430
            "调整偏航角(原地向右)-幅度11.0",  # 32767
        ]
        
        # 执行3组完整的摆动（比relax多一组，显得更活泼）
        for _ in range(3):
            # 向左摆动（从小到大再到小）
            for cmd in left_commands:
                self.commander.run(cmd)
                if self.check_stop:
                    return
                time.sleep(interval)
                if self.check_stop:
                    return
            for cmd in reversed(left_commands[:-1]):
                self.commander.run(cmd)
                if self.check_stop:
                    return
                time.sleep(interval)
                if self.check_stop:
                    return
                
            # 回到中间
            self.commander.run("调整偏航角(原地)-幅度0.0")
            if self.check_stop:
                return
            time.sleep(0.025)
            if self.check_stop:
                return
            
            # 向右摆动（从小到大再到小）
            for cmd in right_commands:
                self.commander.run(cmd)
                if self.check_stop:
                    return
                time.sleep(interval)
                if self.check_stop:
                    return
            for cmd in reversed(right_commands[:-1]):
                self.commander.run(cmd)
                if self.check_stop:
                    return
                time.sleep(interval)
                if self.check_stop:
                    return
                
            # 回到中间
            self.commander.run("调整偏航角(原地)-幅度0.0")
            if self.check_stop:
                return
            time.sleep(0.025)
            if self.check_stop:
                return
        
        # 结束动作
        time.sleep(0.475)
    
    def execute_skill_find_tail(self, interval=0.24):  # 20
        """
        skill 20. 找尾巴
            1. 等待机器狗到可以找尾巴的状态
            2. 左转 → 右转 → #扭身体
        """
        # 1. 等待机器狗到可以找尾巴的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 左转 → 右转 → 扭身体
        # 2-1. 执行"向左转圈"
        #    15次"左转(中速度)" == 90+90+45度
        self.commander.run("移动模式")
        if self.check_stop:
            return
        for i in range(24):
            self.commander.run("左转(中速度)")
            if self.check_stop:
                return
            time.sleep(interval)
            if self.check_stop:
                return
        self.commander.run("左转(中速度)")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return

        # 2-2. 执行"向右转圈"
        self.commander.run("移动模式")
        if self.check_stop:
            return
        for i in range(24):
            self.commander.run("右转(中速度)")
            if self.check_stop:
                return
            time.sleep(interval)
            if self.check_stop:
                return
        self.commander.run("右转(中速度)")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return

        # 2-3. 执行"扭身体"
        # self.execute_skill_twist_body()
    
    def execute_skill_paw_to_hug(self):  # 21
        """
        skill 21. 扑爪要抱
            1. 等待机器狗到可以扑爪要抱的状态
            2. 向前跳
        """
        # 1. 等待机器狗到可以扑爪要抱的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return

        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 向前跳
        self.commander.run("向前跳")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)
    
    def execute_skill_explore_environment(self, interval=0.05):  # 22
        """
        skill 22. 边走边观察
            1. 等待机器狗到可以边走边观察的状态
            2. 低速前进 → 左转 → 右转
        """
        # 1. 等待机器狗到可以边走边观察的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return

        # 2. 低速前进 → 左转 → 右转
        self.commander.run("移动模式")

        for i in range(5):
            self.commander.run("前进(低速度)")
            if self.check_stop:
                return
            time.sleep(interval)
            if self.check_stop:
                return

        if not self.within_state_wait_for_state(COMPATIBLE_STANDING_STATES):
            return

        if self.check_stop:
            return

        self.commander.run("移动模式")

        for i in range(5):
            self.commander.run("左转(中速度)")
            if self.check_stop:
                return
            time.sleep(interval)
            if self.check_stop:
                return

        if not self.within_state_wait_for_state(COMPATIBLE_STANDING_STATES):
            return

        if self.check_stop:
            return

        self.commander.run("移动模式")

        for i in range(5):
            self.commander.run("右转(中速度)")
            if self.check_stop:
                return
            time.sleep(interval)
            if self.check_stop:
                return

        if not self.within_state_wait_for_state(COMPATIBLE_STANDING_STATES):
            return

        if self.check_stop:
            return

        self.commander.run("移动模式")

        for i in range(5):
            self.commander.run("右转(中速度)")
            if self.check_stop:
                return
            time.sleep(interval)
            if self.check_stop:
                return

        if not self.within_state_wait_for_state(COMPATIBLE_STANDING_STATES):
            return

        if self.check_stop:
            return

        self.commander.run("移动模式")

        for i in range(5):
            self.commander.run("左转(中速度)")
            if self.check_stop:
                return
            time.sleep(interval)
            if self.check_stop:
                return

        time.sleep(0.45)

    def execute_skill_raise_head(self, interval=0.5):  # 23
        """
        skill 23. 抬头
            1. 等待机器狗到可以抬头的状态
            2. 执行"抬头"
        """
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        if self.check_stop:
            return

        self.commander.run("原地模式")
        if self.check_stop:
            return
        self.commander.run("调整俯仰角(原地)-高幅度")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return
        self.commander.run("调整俯仰角(原地)-高幅度")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)
    
    def execute_skill_left_turn_a_bit(self):  # 24
        """
        skill 24. 左转一点
            1. 等待机器狗到可以左转一点的状态
            2. 左转
        """
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        if self.check_stop:
            return

        self.commander.run("移动模式")
        if self.check_stop:
            return
        self.commander.run("左转(中速度)")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)

    def execute_skill_right_turn_a_bit(self):  # 25
        """
        skill 25. 右转一点
            1. 等待机器狗到可以右转一点的状态
            2. 右转
        """
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        if self.check_stop:
            return

        self.commander.run("移动模式")
        if self.check_stop:
            return
        self.commander.run("右转(中速度)")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)

    def execute_skill_relax(self, interval=0.08):  # 26
        """
        skill 26. 悠闲 - 缩短 interval 使动作更丝滑
            1. 等待机器狗到可以悠闲的状态
            2. 执行"悠闲"动作 - 更加丝滑的横滚角摆动
        """
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        if self.check_stop:
            return

        self.commander.run("原地模式")

        if self.check_stop:
            return

        # 向左摆动序列（从小到大）
        left_commands = [
            "调整横滚角(原地向左)-幅度1.0",   # -12553
            "调整横滚角(原地向左)-幅度2.0",   # -14000
            "调整横滚角(原地向左)-幅度3.0",   # -16000
            "调整横滚角(原地向左)-幅度4.0",   # -18000
            "调整横滚角(原地向左)-幅度5.0",   # -20000
            "调整横滚角(原地向左)-幅度6.0",   # -22000
            "调整横滚角(原地向左)-幅度7.0",   # -24000
            "调整横滚角(原地向左)-幅度8.0",   # -26000
            "调整横滚角(原地向左)-幅度9.0",   # -28000
            "调整横滚角(原地向左)-幅度10.0",  # -30000
            "调整横滚角(原地向左)-幅度11.0",  # -32767
        ]
        
        # 向右摆动序列（从小到大）
        right_commands = [
            "调整横滚角(原地向右)-幅度1.0",   # 12553
            "调整横滚角(原地向右)-幅度2.0",   # 14000
            "调整横滚角(原地向右)-幅度3.0",   # 16000
            "调整横滚角(原地向右)-幅度4.0",   # 18000
            "调整横滚角(原地向右)-幅度5.0",   # 20000
            "调整横滚角(原地向右)-幅度6.0",   # 22000
            "调整横滚角(原地向右)-幅度7.0",   # 24000
            "调整横滚角(原地向右)-幅度8.0",   # 26000
            "调整横滚角(原地向右)-幅度9.0",   # 28000
            "调整横滚角(原地向右)-幅度10.0",  # 30000
            "调整横滚角(原地向右)-幅度11.0",  # 32767
        ]
        
        # 执行n组完整的摆动
        for _ in range(2):
            # 向左摆动（从小到大再到小）
            for cmd in left_commands:
                self.commander.run(cmd)
                if self.check_stop:
                    return
                time.sleep(interval)
                if self.check_stop:
                    return
            for cmd in reversed(left_commands[:-1]):
                self.commander.run(cmd)
                if self.check_stop:
                    return
                time.sleep(interval)
                if self.check_stop:
                    return
                
            # 回到中间
            self.commander.run("调整横滚角(原地)-幅度0.0")
            if self.check_stop:
                return
            time.sleep(0.025)
            if self.check_stop:
                return
            
            # 向右摆动（从小到大再到小）
            for cmd in right_commands:
                self.commander.run(cmd)
                if self.check_stop:
                    return
                time.sleep(interval)
                if self.check_stop:
                    return
            for cmd in reversed(right_commands[:-1]):
                self.commander.run(cmd)
                if self.check_stop:
                    return
                time.sleep(interval)
                if self.check_stop:
                    return
                
            # 回到中间
            self.commander.run("调整横滚角(原地)-幅度0.0")
            if self.check_stop:
                return
            time.sleep(0.025)
            if self.check_stop:
                return
        
        # 结束动作
        time.sleep(0.475)

    def execute_skill_excited(self, interval=0.05):  # 27
        """
        skill 27. 兴奋 - 缩短 interval 使动作更丝滑
            1. 等待机器狗到可以兴奋的状态
            2. 执行"兴奋"动作 - 大幅度快速丝滑摆动
        """
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        if self.check_stop:
            return

        self.commander.run("原地模式")
        if self.check_stop:
            return
        
        # 大幅度摆动序列（从小到大）
        swing_commands = [
            "调整横滚角(原地向左)-幅度7.0",   # -24000
            "调整横滚角(原地向左)-幅度8.0",   # -26000
            "调整横滚角(原地向左)-幅度9.0",   # -28000
            "调整横滚角(原地向左)-幅度10.0",  # -30000
            "调整横滚角(原地向左)-幅度9.0",   # -28000
            "调整横滚角(原地向左)-幅度8.0",   # -26000
            "调整横滚角(原地向左)-幅度7.0",   # -24000
            "调整横滚角(原地)-幅度0.0",       # 0
            "调整横滚角(原地向右)-幅度7.0",   # 24000
            "调整横滚角(原地向右)-幅度8.0",   # 26000
            "调整横滚角(原地向右)-幅度9.0",   # 28000
            "调整横滚角(原地向右)-幅度10.0",  # 30000
            "调整横滚角(原地向右)-幅度9.0",   # 28000
            "调整横滚角(原地向右)-幅度8.0",   # 26000
            "调整横滚角(原地向右)-幅度7.0",   # 24000
            "调整横滚角(原地)-幅度0.0"        # 0
        ]
        
        # 执行n组完整的摆动
        for _ in range(2):
            for cmd in swing_commands:
                self.commander.run(cmd)
                if self.check_stop:
                    return
                time.sleep(interval)
                if self.check_stop:
                    return
        
        # 结束动作
        self.commander.run("调整横滚角(原地)-幅度0.0")
        if self.check_stop:
            return
        time.sleep(0.45)

    def execute_skill_stop(self, interval=0.025):  # 28
        """
        skill 28. 停止运动
            1. 执行"停止运动"
        """
        self.commander.run("停止移动")
        time.sleep(interval)
        self.commander.run("停止平移")
        time.sleep(interval)
        self.commander.run("停止转向")
        time.sleep(interval)
        self.commander.run("停止编排动作")
        # time.sleep(interval)
        # self.commander.run("关闭所有AI选项")
        time.sleep(interval)
        self.turn_off_follow()
        time.sleep(SKILL_INTERVAL)

    def execute_skill_depressed(self, interval=0.08):  # 29
        """
        skill 29. 沮丧
            1. 等待机器狗到可以沮丧的状态
            2. 执行"沮丧"动作
        """
        # 1. 等待机器狗到可以沮丧的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        if self.check_stop:
            return

        # 2. 执行"沮丧"动作
        self.commander.run("原地模式")
        if self.check_stop:
            return
        
        # 动作序列
        bow_head_commands = [
            "调整俯仰角(原地低头)-幅度1.0",
            "调整俯仰角(原地低头)-幅度2.0",
            "调整俯仰角(原地低头)-幅度3.0",
            "调整俯仰角(原地低头)-幅度4.0",
            "调整俯仰角(原地低头)-幅度5.0",
            "调整俯仰角(原地低头)-幅度6.0",
            "调整俯仰角(原地低头)-幅度7.0",
            "调整俯仰角(原地低头)-幅度8.0",
            "调整俯仰角(原地低头)-幅度9.0",
            "调整俯仰角(原地低头)-幅度10.0",
            "调整俯仰角(原地低头)-幅度11.0",
            "调整俯仰角(原地低头)-幅度11.0",
            "调整俯仰角(原地低头)-幅度11.0",
            "调整俯仰角(原地低头)-幅度11.0",
            "调整俯仰角(原地低头)-幅度11.0",
        ]
        
        # 向左摆动序列（从小到大）
        left_commands = [
            "调整偏航角(原地向左)-幅度1.0",   # -9553
            "调整偏航角(原地向左)-幅度2.0",   # -11870
            "调整偏航角(原地向左)-幅度3.0",   # -14191
            "调整偏航角(原地向左)-幅度4.0",   # -16510
            "调整偏航角(原地向左)-幅度5.0",   # -18830
            "调整偏航角(原地向左)-幅度6.0",   # -21150
            "调整偏航角(原地向左)-幅度7.0",   # -23470
            "调整偏航角(原地向左)-幅度8.0",   # -25790
            "调整偏航角(原地向左)-幅度9.0",   # -28110
            "调整偏航角(原地向左)-幅度10.0",  # -30430
            "调整偏航角(原地向左)-幅度11.0",  # -32767
        ]
        
        # 向右摆动序列（从小到大）
        right_commands = [
            "调整偏航角(原地向右)-幅度1.0",   # 9553
            "调整偏航角(原地向右)-幅度2.0",   # 11870
            "调整偏航角(原地向右)-幅度3.0",   # 14191
            "调整偏航角(原地向右)-幅度4.0",   # 16510
            "调整偏航角(原地向右)-幅度5.0",   # 18830
            "调整偏航角(原地向右)-幅度6.0",   # 21150
            "调整偏航角(原地向右)-幅度7.0",   # 23470
            "调整偏航角(原地向右)-幅度8.0",   # 25790
            "调整偏航角(原地向右)-幅度9.0",   # 28110
            "调整偏航角(原地向右)-幅度10.0",  # 30430
            "调整偏航角(原地向右)-幅度11.0",  # 32767
        ]

        for cmd in bow_head_commands:
            self.commander.run(cmd)
            if self.check_stop:
                return
            time.sleep(interval)
            if self.check_stop:
                return
        
        # 执行3组
        for _ in range(2):
            # 向左摆动（从小到大再到小）
            for cmd in left_commands:
                # 同时执行偏航角和俯仰角命令
                self.commander.run(cmd)
                self.commander.run("调整俯仰角(原地低头)-幅度11.0")
                if self.check_stop:
                    return
                time.sleep(interval)
                if self.check_stop:
                    return
            for cmd in reversed(left_commands[:-1]):
                # 同时执行偏航角和俯仰角命令
                self.commander.run(cmd)
                self.commander.run("调整俯仰角(原地低头)-幅度11.0")
                if self.check_stop:
                    return
                time.sleep(interval)
                if self.check_stop:
                    return
                
            # 回到中间
            self.commander.run("调整偏航角(原地)-幅度0.0")
            if self.check_stop:
                return
            time.sleep(0.025)
            if self.check_stop:
                return
            
            # 向右摆动（从小到大再到小）
            for cmd in right_commands:
                # 同时执行偏航角和俯仰角命令
                self.commander.run(cmd)
                self.commander.run("调整俯仰角(原地低头)-幅度11.0")
                if self.check_stop:
                    return
                time.sleep(interval)
                if self.check_stop:
                    return
            for cmd in reversed(right_commands[:-1]):
                # 同时执行偏航角和俯仰角命令
                self.commander.run(cmd)
                self.commander.run("调整俯仰角(原地低头)-幅度11.0")
                if self.check_stop:
                    return
                time.sleep(interval)
                if self.check_stop:
                    return
                
            # 回到中间
            self.commander.run("调整偏航角(原地)-幅度0.0")
            if self.check_stop:
                return
            time.sleep(0.025)
            if self.check_stop:
                return
        
        # 缓慢抬头
        head_up_commands = [
            "调整俯仰角(原地低头)-幅度9.0",
            "调整俯仰角(原地低头)-幅度7.0",
            "调整俯仰角(原地低头)-幅度5.0",
            "调整俯仰角(原地低头)-幅度3.0",
            "调整俯仰角(原地低头)-幅度1.0",
            ]
        for i in head_up_commands:
            self.commander.run(i)
            if self.check_stop:
                return
            time.sleep(interval)
            if self.check_stop:
                return
        # 结束动作
        time.sleep(0.42)

    def execute_skill_listen(self, interval=0.02, max_listen_time=3):  # 30
        """
        skill 30. 倾听
            1. 等待机器狗到可以倾听的状态
            2. 执行"坐下"
        """
        # 1. 等待机器狗到可以坐下的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        # 检查是否有停止命令
        if self.check_stop:
            return
        
        self.commander.run("原地模式")
        if self.check_stop:
            return

        # 2. 执行"坐下"
        head_up_commands = [
            "调整俯仰角(原地抬头)-幅度1.0",
            "调整俯仰角(原地抬头)-幅度2.0",
            "调整俯仰角(原地抬头)-幅度3.0",
            "调整俯仰角(原地抬头)-幅度4.0",
            "调整俯仰角(原地抬头)-幅度5.0",
            "调整俯仰角(原地抬头)-幅度6.0",
            "调整俯仰角(原地抬头)-幅度7.0",
            "调整俯仰角(原地抬头)-幅度8.0",
            "调整俯仰角(原地抬头)-幅度9.0",
            "调整俯仰角(原地抬头)-幅度10.0",
            "调整俯仰角(原地抬头)-幅度11.0",
        ]

        button_down_commands = [
            "调整身体高度(原地降低)-幅度1.0",
            "调整身体高度(原地降低)-幅度2.0",
            "调整身体高度(原地降低)-幅度3.0",
            "调整身体高度(原地降低)-幅度4.0",
            "调整身体高度(原地降低)-幅度5.0",
            "调整身体高度(原地降低)-幅度6.0",
            "调整身体高度(原地降低)-幅度7.0",
            "调整身体高度(原地降低)-幅度8.0",
            "调整身体高度(原地降低)-幅度9.0",
            "调整身体高度(原地降低)-幅度10.0",
            "调整身体高度(原地降低)-幅度11.0",
        ]

        # 同步执行两个动作序列
        for head_cmd, body_cmd in zip(head_up_commands, button_down_commands):
            # 同时执行抬头和降低身体高度命令
            self.commander.run(head_cmd)
            self.commander.run(body_cmd)
            if self.check_stop:
                return
            time.sleep(interval)
            if self.check_stop:
                return
            
        # 保持坐下n秒
        for _ in range(max_listen_time):
            for i in range(4):
                self.commander.run("调整俯仰角(原地抬头)-幅度11.0")
                self.commander.run("调整身体高度(原地降低)-幅度11.0")
                time.sleep(0.24)
                if self.check_stop:
                    return
        time.sleep(0.26)

    def execute_skill_turn_off_obstacle_stop(self, interval=0.025):  # 31
        """
        skill 31. 关闭遇到障碍停止模式
            1. 执行"关闭遇到障碍停止"
        """
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        if self.check_stop:
            return

        self.commander.run("原地模式")
        if self.check_stop:
            return
        self.commander.run("调整俯仰角(原地低头)-幅度11.0")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)
        if self.check_stop:
            return
        self.commander.run("调整俯仰角(原地低头)-幅度11.0")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return

        self.commander.run("关闭所有AI选项")
        time.sleep(SKILL_INTERVAL)

    def execute_skill_follow(self, interval=0.025):  # 32
        """
        skill 32. 开启跟随模式
            1. 等待机器狗到可以跟随的状态
            2. 开启跟随模式
            3. 启动监控线程，如果机器人在站立状态下超过5秒，则自动退出跟随模式
        """
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        if self.check_stop:
            return

        # 确保之前的监控线程已终止
        self._stop_follow_monitor_thread()

        self.commander.run("原地模式")
        if self.check_stop:
            return
        self.commander.run("调整俯仰角(原地抬头)-幅度11.0")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)
        if self.check_stop:
            return
        self.commander.run("调整俯仰角(原地低头)-幅度11.0")
        if self.check_stop:
            return
        time.sleep(0.5)
        if self.check_stop:
            return

        # 开启
        data_dict = {
            "cmd": "start",
            "direction": None
        }
        json_str = json.dumps(data_dict)
        msg = String()
        msg.data = json_str
        # self.ros_commander.publish(msg)

        time.sleep(interval)
        if self.check_stop:
            return
        self.execute_skill_turn_on_autonomous()
        self.commander.run("移动模式")

        # 启动监控线程，如果机器人在站立状态下超过5秒，则自动退出跟随模式
        self._start_follow_monitor_thread()
        
        time.sleep(SKILL_INTERVAL)

    def _start_follow_monitor_thread(self):  # 32.5
        """启动跟随模式监控线程"""
        # 确保之前的线程已终止
        self._stop_follow_monitor_thread()
        
        # 设置监控运行标志
        self.follow_monitor_running = True
        
        def monitor_standing_state():
            standing_start_time = None
            while self.follow_monitor_running and not self.check_stop:
                current_state = self.commander.robot_monitor.robot_state
                # 检查当前状态是否为站立状态
                if current_state in COMPATIBLE_STANDING_STATES:
                    if standing_start_time is None:
                        # 第一次检测到站立状态，记录时间
                        standing_start_time = time.time()
                        logger.info(f"检测到机器人进入站立状态，开始计时：{current_state}")
                    elif time.time() - standing_start_time > 2:
                        # 站立状态超过2秒，执行退出跟随模式
                        logger.info(f"检测到机器人在站立状态下超过2秒，自动退出跟随模式：{current_state}")
                        self.execute_skill_turn_off_follow()
                        break
                else:
                    # 检测到机器人不在站立状态，重置计时
                    if standing_start_time is not None:
                        logger.info(f"机器人离开站立状态，重置计时：{current_state}")
                        standing_start_time = None
                
                # 每0.1秒检查一次
                time.sleep(0.1)
        
            logger.info("跟随模式监控线程已终止")
        
        # 创建并启动监控线程
        self.follow_monitor_thread = threading.Thread(target=monitor_standing_state, daemon=True)
        self.follow_monitor_thread.start()
        logger.info("已启动跟随模式站立状态监控线程")

    def _stop_follow_monitor_thread(self):  # 32.75
        """安全终止监控线程"""
        if self.follow_monitor_thread and self.follow_monitor_thread.is_alive():
            self.follow_monitor_running = False
            # 给线程一些时间来终止
            for _ in range(5):  # 最多等待0.5秒
                if not self.follow_monitor_thread.is_alive():
                    break
                time.sleep(0.1)
            logger.info("已终止跟随模式监控线程")
        
        # 重置线程变量
        self.follow_monitor_thread = None
        self.follow_monitor_running = False

    def execute_skill_turn_on_obstacle_stop(self, interval=0.025):  # 33
        """
        skill 33. 开启遇到障碍停止模式
            1. 执行"遇到障碍停止"
        """
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        self.commander.run("原地模式")
        if self.check_stop:
            return
        self.commander.run("调整俯仰角(原地低头)-幅度11.0")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)
        if self.check_stop:
            return
        self.commander.run("调整俯仰角(原地低头)-幅度11.0")
        if self.check_stop:
            return
        time.sleep(interval)
        if self.check_stop:
            return

        self.commander.run("开启停障")
        time.sleep(SKILL_INTERVAL)

    def execute_skill_test(self, interval=0.5):  # 34
        """
        skill 34. 测试
        """
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        if self.check_stop:
            return
        
        self.commander.run("停止编排动作")
        time.sleep(interval)

    def execute_skill_low_speed(self):  # 35
        """
        skill 35. 低速模式
        """
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        if self.check_stop:
            return
        
        self.commander.run("移动模式")
        if self.check_stop:
            return
        self.commander.run("低速")
        time.sleep(0.1)
    
    def execute_skill_medium_speed(self):  # 36
        """
        skill 36. 中速模式
        """
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        if self.check_stop:
            return
        
        self.commander.run("移动模式")
        if self.check_stop:
            return
        self.commander.run("中速")
        time.sleep(0.1)
    
    def execute_skill_high_speed(self):  # 37
        """
        skill 37. 高速模式
        """
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        if self.check_stop:
            return
        
        self.commander.run("移动模式")
        if self.check_stop:
            return
        self.commander.run("高速")
        time.sleep(0.1)
    
    def execute_skill_turn_off_follow(self, interval=0.025):  # 38
        """
        skill 38. 关闭跟随模式
            1. 执行"关闭跟随模式"
        """
        # 首先终止监控线程
        self._stop_follow_monitor_thread()
        
        # 关闭
        data_dict = {
            "cmd": "stop",
            "direction": None
        }
        json_str = json.dumps(data_dict)
        msg = String()
        msg.data = json_str
        # self.ros_commander.publish(msg)

        time.sleep(0.1)

        self.execute_skill_turn_on_manual()
        time.sleep(interval)
        if self.check_stop:
            return

        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        if self.check_stop:
            return
        
        time.sleep(interval)
        if self.check_stop:
            return
    
        self.commander.run("原地模式")
        if self.check_stop:
            return
        self.commander.run("调整俯仰角(原地抬头)-幅度11.0")
        if self.check_stop:
            return
        time.sleep(SKILL_INTERVAL)
        if self.check_stop:
            return
        self.commander.run("调整俯仰角(原地低头)-幅度11.0")
        if self.check_stop:
            return
        time.sleep(interval)
    
    def turn_off_follow(self, interval=0.025):  # 38.5
        """
        skill 38. 关闭跟随模式
            1. 执行"关闭跟随模式"
        """
        # 首先终止监控线程
        self._stop_follow_monitor_thread()
        
        # 关闭
        data_dict = {
            "cmd": "stop",
            "direction": None
        }
        json_str = json.dumps(data_dict)
        msg = String()
        msg.data = json_str
        # self.ros_commander.publish(msg)

        time.sleep(0.1)

        self.execute_skill_turn_on_manual()
        time.sleep(interval)

    def execute_skill_turn_on_autonomous(self):  # 39
        """
        skill 39. 开启自主模式
            1. 执行"开启自主模式"
        """
        self.commander.run("导航模式")

    def execute_skill_turn_on_manual(self):  # 40
        """
        skill 40. 开启手动模式
            1. 执行"开启手动模式"
        """
        self.commander.run("手动模式")
    
    def execute_skill_turn_body_jump(self, interval=1):  # 41
        """
        skill 41. 扭身跳
            1. 等待机器狗到可以扭身跳的状态
            2. 执行"扭身跳"
        """
        # 1. 等待机器狗到可以扭身跳的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        if self.check_stop:
            return

        # 2. 执行"扭身跳"
        self.commander.run("扭身跳")
        if self.check_stop:
            return
        time.sleep(interval)
    
    def execute_skill_dance_with_music(self, dance_style=["default", "spin", "wave", "combined", "with_music"]):  # 42
        """
        skill 42. 随音乐跳舞
            1. 等待机器狗到可以随音乐跳舞的状态
            2. 执行"跳舞"
        """
        # 1. 等待机器狗到可以随音乐跳舞的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            return
        
        if self.check_stop:
            return

        meticulous_controller = RobotMeticulousController(self)

        # 2. 执行"随音乐跳舞"
        play_obj = self.start_audio("/home/<USER>/Possessed_AI/asserts/music/friendships.wav")
        meticulous_controller.execute_dance(dance_style[4])
        # 确保音乐播放完成
        if play_obj and play_obj.is_playing():
            if self.check_stop:
                play_obj.stop()
            play_obj.wait_done()

    def start_audio(self, tempfile):  # 42.5
        """开始播放音频但不等待结束"""
        wave_obj = sa.WaveObject.from_wave_file(tempfile)
        play_obj = wave_obj.play()
        return play_obj  # 不要用wait_done()
        
