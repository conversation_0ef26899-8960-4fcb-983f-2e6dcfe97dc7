2025-07-10 12:54:51.351 - chat_with_robot - chat_with_robot.py - <module> - line 619 - INFO - use_action: dont
2025-07-10 12:54:51.351 - chat_with_robot - chat_with_robot.py - <module> - line 620 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-10 12:54:51.370 - chat_with_robot - chat_with_robot.py - init_websocket - line 311 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1752123291369&accessNonce=36db1435-13f8-4336-b3e9-e3519b271eb2&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=035a32d3-5d4a-11f0-bf36-dc4546c07870&requestId=82848d40-36cc-4593-bb6b-4354c9bbb4c8_joyinside&accessSign=48b3d5bda9ba639863ab73dde60ce0b2, request_id: 82848d40-36cc-4593-bb6b-4354c9bbb4c8_joyinside
2025-07-10 12:54:51.370 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-10 12:54:51.370 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-10 12:54:51.853 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-10 12:54:51.972 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-07-10 12:54:53.604 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-10 12:54:53.606 - chat_with_robot - voice.py - _setup_audio_stream - line 311 - INFO - 使用音频设备: 1
2025-07-10 12:54:53.606 - chat_with_robot - voice.py - _setup_audio_stream - line 312 - INFO - channels: 4 <class 'int'>
2025-07-10 12:54:53.606 - chat_with_robot - voice.py - _setup_audio_stream - line 313 - INFO - rate: 44100.0 <class 'float'>
2025-07-10 12:54:53.690 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-10 12:54:53.690 - chat_with_robot - voice.py - init_wakeup - line 298 - INFO - 本地流式KWS检测器启动成功
2025-07-10 12:57:55.331 - chat_with_robot - voice.py - detect_callback - line 413 - INFO - [wakeup] 检测到唤醒词
2025-07-10 12:57:55.331 - chat_with_robot - voice.py - end_streaming - line 212 - INFO - [end recording]...
2025-07-10 12:57:55.399 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-10 12:57:55.399 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-10 12:57:56.499 - chat_with_robot - voice.py - start_streaming - line 208 - INFO - [start recording]...
2025-07-10 12:57:56.501 - chat_with_robot - voice.py - run - line 469 - INFO - [run] 持续监听状态...
2025-07-10 12:57:58.831 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-10 12:57:58.831 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-10 12:58:00.273 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-10 12:58:00.273 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-10 12:58:01.353 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-10 12:58:01.354 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-10 12:58:14.074 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-10 12:58:14.074 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-10 12:59:40.190 - chat_with_robot - voice.py - detect_callback - line 413 - INFO - [wakeup] 检测到唤醒词
2025-07-10 12:59:40.190 - chat_with_robot - voice.py - end_streaming - line 212 - INFO - [end recording]...
2025-07-10 12:59:40.252 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-10 12:59:40.252 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-10 12:59:41.667 - chat_with_robot - voice.py - start_streaming - line 208 - INFO - [start recording]...
2025-07-10 12:59:41.730 - chat_with_robot - voice.py - run - line 469 - INFO - [run] 持续监听状态...
2025-07-10 12:59:44.399 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想知道台湾问题, 时间戳: 2025-07-10 12:59:44.135000
2025-07-10 12:59:45.743 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-10 12:59:45.478000
2025-07-10 12:59:45.743 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1343
2025-07-10 12:59:45.814 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-10 12:59:45.824 - chat_with_robot - chat_with_robot.py - _task_worker - line 421 - INFO - 存入音频
2025-07-10 12:59:45.825 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-10 12:59:46.236 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-10 12:59:46.236 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-10 12:59:46.247 - chat_with_robot - chat_with_robot.py - _task_worker - line 421 - INFO - 存入音频
2025-07-10 12:59:46.257 - chat_with_robot - chat_with_robot.py - _task_worker - line 375 - INFO - session_id: 035a32d3-5d4a-11f0-bf36-dc4546c07870; requestId: 82848d40-36cc-4593-bb6b-4354c9bbb4c8_joyinside; asr: 我想知道台湾问题; 响应时间: 0; JD机器人回复: 台湾是中国不可分割的一部分，两岸同胞血脉相连，共同推动实现祖国完全统一是中华民族的共同愿望。
2025-07-10 12:59:46.257 - chat_with_robot - chat_with_robot.py - _task_worker - line 377 - INFO - 等待控制完成
2025-07-10 12:59:46.257 - chat_with_robot - chat_with_robot.py - _task_worker - line 382 - INFO - 等待音频播放完成
2025-07-10 12:59:48.747 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-10 12:59:55.602 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-10 12:59:55.603 - chat_with_robot - chat_with_robot.py - _task_worker - line 392 - INFO - 任务完成，继续
2025-07-10 13:01:55.436 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-07-10 13:01:55.436 - chat_with_robot - voice.py - stop - line 408 - INFO - 已停止local_streaming检测器
