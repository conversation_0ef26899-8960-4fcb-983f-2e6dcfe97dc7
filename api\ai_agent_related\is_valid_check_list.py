STATE_STAND_LOW = "力控状态（静止站立）且步态为平地低速步态"
STATE_STAND_MEDIUM = "力控状态（静止站立）且步态为平地中速步态"
STATE_STAND_HIGH = "力控状态（静止站立）且步态为平地高速步态"
STATE_DOWN = "趴下状态"
STATE_DOING_STANDING = ["准备起立状态", "正在起立状态"]

COMPATIBLE_STANDING_STATES = [STATE_STAND_LOW, STATE_STAND_MEDIUM, STATE_STAND_HIGH]
COMPATIBLE_STEPING_STATES = ["正在以平地低速步态踏步或正在根据轴指令扭动身体", "正在以平地中速步态踏步", "正在以平地高速步态踏步"]

ALL_STATES = [
    "趴下状态",
    "正在执行向前跳",
    "准备起立状态",
    "正在起立状态",
    "力控状态（静止站立）且步态为平地低速步态",
    "正在以平地低速步态踏步或正在根据轴指令扭动身体",
    "正在执行扭身体",
    "正在执行扭身跳",
    "力控状态（静止站立）且步态为通用越障步态",
    "正在以通用越障步态踏步",
    "力控状态（静止站立）且步态为平地中速步态",
    "正在以平地中速步态踏步",
    "力控状态（静止站立）且步态为平地高速步态",
    "正在以平地高速步态踏步",
    "力控状态（静止站立）且步态为抓地越障步态",
    "正在以抓地越障步态踏步",
    "正在执行太空步",
    "力控状态（静止站立）且步态为高踏步越障步态",
    "正在以高踏步越障步态踏步",
    "正在趴下状态",
    "失控保护状态",
    "姿态调整状态",
    "正在执行翻身",
    "回零状态",
    "正在执行后空翻",
    "正在执行打招呼"
]

META_ACTIONS = {
    "前进": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
        "end_states": COMPATIBLE_STANDING_STATES, 
        "description": "耗时: 1秒, 移动距离: 0.3米"
    },
    "后退": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 1秒, 移动距离: 0.3米"
    },
    "左平移": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 1秒, 移动距离: 0.15米"
    },
    "右平移": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 1秒, 移动距离: 0.15米"
    },
    "左转": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 1秒, 移动距离: 0米, 移动角度: 45度"
    },
    "右转": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 1秒, 移动距离: 0米, 移动角度: 45度"
    },
    "起立": {
        "start_states": [STATE_DOWN],  # wait_for_state 和 check_action_state 逻辑矛盾，去除 COMPATIBLE_STANDING_STATES
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 1.5秒, 移动距离: 0米"
    },
    "趴下": {
        "start_states": COMPATIBLE_STANDING_STATES,  # wait_for_state 和 check_action_state 逻辑矛盾，去除 + [STATE_DOWN]
        "end_states": STATE_DOWN,
        "description": "耗时: 1.5秒, 移动距离: 0米"
    },
    "打招呼": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 7秒, 移动距离: 0米"
    },
    "向前跳": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 2.5秒, 移动距离: 0.84米"
    },
    "摇尾巴撒娇": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 2.75秒, 移动距离: 0米"
    },
    "抬头": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 0.95秒, 移动距离: 0米"
    },
    "悠闲": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 6.75秒, 移动距离: 0米"
    },
    "兴奋": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 1.33秒, 移动距离: 0米"
    },
    "停止运动": {
        "start_states": ALL_STATES + ["未知状态"],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 0.75秒, 移动距离: 0米"
    },
    "沮丧": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 8.2秒, 移动距离: 0米"
    },
    "倾听": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 3.2秒, 移动距离: 0米"
    },
    "关闭遇到障碍停止模式": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 0.95秒, 移动距离: 0米"
    },
    "开启跟随模式": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN] + STATE_DOING_STANDING,   # 加入 STATE_DOING_STANDING 兜底起立状态信息泄露
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 0.95秒, 移动距离: 0米"
    },
    "开启遇到障碍停止模式":{
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 0.95秒, 移动距离: 0米"
    },
    "低速": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN] + COMPATIBLE_STEPING_STATES,
        "end_states": [STATE_STAND_LOW],
        "description": "耗时: 0.1秒, 移动距离: 0米"
    },
    "中速": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN] + COMPATIBLE_STEPING_STATES,
        "end_states": [STATE_STAND_MEDIUM],
        "description": "耗时: 0.1秒, 移动距离: 0米"
    },
    "高速": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN] + COMPATIBLE_STEPING_STATES,
        "end_states": [STATE_STAND_HIGH],
        "description": "耗时: 0.1秒, 移动距离: 0米"
    },
    "扭身跳":{
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 1秒, 移动距离: 0米"
    },
    "关闭跟随模式": {
        "start_states": ALL_STATES + ["未知状态"],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 0.95秒, 移动距离: 0米"
    },
        "舞蹈": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
        "end_states": COMPATIBLE_STANDING_STATES,
        "description": "耗时: 71秒, 移动距离: 0米"
    },
}