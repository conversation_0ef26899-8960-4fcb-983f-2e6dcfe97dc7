[14:18:25.850] INFO     | audio_action_controller.py:136 - Modbus 功能已禁用（pymodbus 未安装）
[14:18:28.443] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[14:18:28.443] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[14:18:28.444] INFO     | audio_action_controller.py:292 - 正在加载音频: ./asserts/ding.wav
[14:18:28.444] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[14:18:28.445] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.9675963521003723秒
[14:18:28.445] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:18:28.445] INFO     | audio_action_controller.py:327 - 开始播放音频
[14:18:28.445] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[14:18:28.445] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:28.447] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[14:18:28.447] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:28.567] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:28.598] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:28.688] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:28.749] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:28.809] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:28.900] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:28.930] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:29.050] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:29.050] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:29.172] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:29.202] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:29.292] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:29.352] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:29.453] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:18:29.453] INFO     | audio_action_controller.py:348 - 音频播放完成
[14:18:29.504] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:29.621] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[14:18:29.621] INFO     | audio_action_controller.py:67 - 检测到MP3文件，已应用针对压缩音频的优化设置
[14:18:29.622] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/tts/dog_ok.mp3
[14:18:29.628] INFO     | audio_action_controller.py:42 - 音频音量设置为: 85.0% (针对WAV无损格式优化)
[14:18:29.628] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 4.439773082733154秒
[14:18:29.629] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:18:29.629] INFO     | audio_action_controller.py:327 - 开始播放音频
[14:18:29.630] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[14:18:29.630] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:29.630] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[14:18:29.630] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:29.751] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:29.781] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:29.872] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:29.932] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:29.992] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:30.083] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:30.113] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:30.234] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:30.234] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:30.355] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:30.385] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:30.476] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:30.535] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:30.597] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:30.686] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:30.718] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:30.837] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:30.838] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:30.959] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:30.988] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:31.079] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:31.139] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:31.200] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:31.290] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:31.321] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:31.441] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:31.442] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:31.563] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:31.591] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:31.683] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:31.742] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:31.804] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:31.893] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:31.925] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:32.044] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:32.046] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:32.166] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:32.195] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:32.287] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:32.346] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:32.408] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:32.497] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:32.528] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:32.648] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:32.650] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:32.770] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:32.799] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:32.891] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:32.949] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:33.012] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:33.100] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:33.133] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:33.251] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:33.253] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:33.374] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:33.402] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:33.495] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:33.553] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:33.615] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:33.704] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:33.736] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:33.855] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:33.857] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:34.005] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:34.154] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:18:34.154] INFO     | audio_action_controller.py:348 - 音频播放完成
[14:18:34.156] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:34.307] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:34.458] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:34.609] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:34.759] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:18:34.910] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
