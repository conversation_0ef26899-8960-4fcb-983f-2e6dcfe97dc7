2025-07-11 14:23:24.426 - chat_with_robot - chat_with_robot.py - <module> - line 640 - INFO - use_action: dont
2025-07-11 14:23:24.426 - chat_with_robot - chat_with_robot.py - <module> - line 641 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-11 14:23:24.427 - chat_with_robot - chat_with_robot.py - init_websocket - line 319 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1752215004427&accessNonce=9037e92f-8bc9-4299-8a41-1e4207c2b923&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=8c9833cf-5e1f-11f0-9692-dc4546c07870&requestId=a96d1b32-6d92-4eec-9462-43342d2e3849_joyinside&accessSign=2a51feda52ddd9581c4936138bee0ec7, request_id: a96d1b32-6d92-4eec-9462-43342d2e3849_joyinside
2025-07-11 14:23:24.428 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-11 14:23:24.429 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-11 14:23:24.880 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-11 14:23:25.148 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-07-11 14:23:26.482 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-11 14:23:26.484 - chat_with_robot - voice.py - _setup_audio_stream - line 324 - INFO - 使用音频设备: 1
2025-07-11 14:23:26.484 - chat_with_robot - voice.py - _setup_audio_stream - line 325 - INFO - channels: 4 <class 'int'>
2025-07-11 14:23:26.484 - chat_with_robot - voice.py - _setup_audio_stream - line 326 - INFO - rate: 44100.0 <class 'float'>
2025-07-11 14:23:26.553 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-11 14:23:26.553 - chat_with_robot - voice.py - init_wakeup - line 311 - INFO - 本地流式KWS检测器启动成功
2025-07-11 14:23:27.554 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-11 14:23:27.555 - chat_with_robot - chat_with_robot.py - play_audio - line 525 - INFO - 使用 audio_action_controller 播放: ./asserts/ding.wav
2025-07-11 14:23:28.712 - chat_with_robot - chat_with_robot.py - play_audio - line 527 - INFO - audio_action_controller 播放完成: ./asserts/ding.wav
2025-07-11 14:23:28.712 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 84 - INFO - Windows 系统，使用 audio_action_controller 播放: asserts/tts/dog_ok.mp3
2025-07-11 14:23:34.734 - chat_with_robot - voice.py - detect_callback - line 426 - INFO - [wakeup] 检测到唤醒词
2025-07-11 14:23:34.734 - chat_with_robot - voice.py - end_streaming - line 225 - INFO - [end recording]...
2025-07-11 14:23:34.797 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-11 14:23:34.797 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-11 14:23:34.797 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-07-11 14:23:34.797 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用 audio_action_controller 播放唤醒音频: asserts/shenmeshi.wav
2025-07-11 14:23:35.940 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-07-11 14:23:35.940 - chat_with_robot - voice.py - start_streaming - line 221 - INFO - [start recording]...
2025-07-11 14:23:35.944 - chat_with_robot - voice.py - run - line 482 - INFO - [run] 持续监听状态...
2025-07-11 14:23:38.934 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想知道回锅肉怎么做, 时间戳: 2025-07-11 14:23:39.017000
2025-07-11 14:23:40.440 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-11 14:23:40.506000
2025-07-11 14:23:40.440 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1489
2025-07-11 14:23:40.458 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 14:23:40.463 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 14:23:40.473 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 14:23:40.473 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 14:23:40.474 - chat_with_robot - audio_player.py - _play_single_audio - line 178 - INFO - 使用 audio_action_controller 播放大模型语音: temp_tts_audio.mp3
2025-07-11 14:23:40.795 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 14:23:40.797 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 14:23:41.256 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 14:23:41.256 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 14:23:41.574 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 14:23:41.579 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 14:23:41.980 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 14:23:41.988 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 14:23:42.304 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 14:23:42.307 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 14:23:42.307 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-11 14:23:42.317 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: 8c9833cf-5e1f-11f0-9692-dc4546c07870; requestId: a96d1b32-6d92-4eec-9462-43342d2e3849_joyinside; asr: 我想知道回锅肉怎么做; 响应时间: 0; JD机器人回复: 哇！东东也喜欢吃回锅肉呀！不过这道菜要用到刀和火🔥，我们小朋友还不能自己操作哦～要不要让爸爸妈妈来做？我们可以帮他们准备材料！比如：五花肉、蒜苗、豆瓣酱...你最喜欢吃里面的哪样配菜呀？
2025-07-11 14:23:42.317 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-11 14:23:42.317 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-11 14:23:43.242 - chat_with_robot - audio_player.py - _play_single_audio - line 182 - INFO - 大模型语音播放完成
2025-07-11 14:23:43.243 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 14:23:43.243 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 14:23:43.244 - chat_with_robot - audio_player.py - _play_single_audio - line 178 - INFO - 使用 audio_action_controller 播放大模型语音: temp_tts_audio.mp3
2025-07-11 14:23:45.751 - chat_with_robot - audio_player.py - _play_single_audio - line 182 - INFO - 大模型语音播放完成
2025-07-11 14:23:45.752 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 14:23:45.752 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 14:23:45.752 - chat_with_robot - audio_player.py - _play_single_audio - line 178 - INFO - 使用 audio_action_controller 播放大模型语音: temp_tts_audio.mp3
2025-07-11 14:23:50.344 - chat_with_robot - audio_player.py - _play_single_audio - line 182 - INFO - 大模型语音播放完成
2025-07-11 14:23:50.346 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 14:23:50.346 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 14:23:50.346 - chat_with_robot - audio_player.py - _play_single_audio - line 178 - INFO - 使用 audio_action_controller 播放大模型语音: temp_tts_audio.mp3
2025-07-11 14:23:52.651 - chat_with_robot - audio_player.py - _play_single_audio - line 182 - INFO - 大模型语音播放完成
2025-07-11 14:23:52.652 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 14:23:52.652 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 14:23:52.652 - chat_with_robot - audio_player.py - _play_single_audio - line 178 - INFO - 使用 audio_action_controller 播放大模型语音: temp_tts_audio.mp3
