2025-06-30 16:41:52.527 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-06-30 16:41:52.528 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-06-30 16:41:52.543 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751272912542&accessNonce=bcdecd34-de5a-42e5-83ac-6fca3f21c014&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=1212cabe-558e-11f0-957e-dc4546c07870&requestId=7e4869c1-26a2-4c0a-8f1e-f9c5cab72021_joyinside&accessSign=378e9f7065c6c73eea84b21dbbed8b63, request_id: 7e4869c1-26a2-4c0a-8f1e-f9c5cab72021_joyinside
2025-06-30 16:41:52.545 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-06-30 16:41:52.545 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-06-30 16:41:52.857 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-06-30 16:41:53.073 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-06-30 16:41:54.491 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-06-30 16:41:54.491 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-06-30 16:41:54.491 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-06-30 16:41:54.492 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-06-30 16:41:54.519 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-06-30 16:41:54.519 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-06-30 16:41:57.604 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:41:57.604 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:41:57.670 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:41:57.670 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:41:59.056 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:41:59.059 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:42:05.255 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天北京天气怎么样？, 时间戳: 2025-06-30 16:42:11.009000
2025-06-30 16:42:06.421 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:42:12.174000
2025-06-30 16:42:06.421 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1165
2025-06-30 16:42:06.526 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:42:06.537 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:42:06.548 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:42:06.794 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:42:06.794 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:42:07.170 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:42:07.180 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:42:07.491 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:42:07.502 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:42:07.804 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:42:07.814 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:42:08.107 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:42:08.108 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:42:08.113 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:42:08.125 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1212cabe-558e-11f0-957e-dc4546c07870; requestId: 7e4869c1-26a2-4c0a-8f1e-f9c5cab72021_joyinside; asr: 今天北京天气怎么样？; 响应时间: 0; JD机器人回复: 今天北京有雷阵雨，温度和昨天差不多，现在31度，有风，空气一般。体感温度为32度，相对湿度59%，南风3级，能见度9公里，紫外线强度中等。出门记得带伞，小心路滑，注意安全！
2025-06-30 16:42:08.125 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:42:08.125 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:42:10.571 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:42:13.430 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天北京有雷阵雨，温度和昨天差不多，现在31度，有风, 时间戳: 2025-06-30 16:42:19.182000
2025-06-30 16:42:14.093 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:42:14.589 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:42:20.341000
2025-06-30 16:42:14.589 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1159
2025-06-30 16:42:14.597 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:42:14.597 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:42:14.598 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:42:14.605 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:42:14.612 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:42:14.618 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1212cabe-558e-11f0-957e-dc4546c07870; requestId: 7e4869c1-26a2-4c0a-8f1e-f9c5cab72021_joyinside; asr: 今天北京有雷阵雨，温度和昨天差不多，现在31度，有风; 响应时间: 0; JD机器人回复: 
2025-06-30 16:42:14.618 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:42:14.618 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:42:14.618 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:42:14.696 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:42:14.698 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:42:14.708 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:42:14.708 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:42:14.809 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:42:16.652 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:42:16.652 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:42:16.717 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:42:16.717 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:42:17.754 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:42:17.822 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:42:19.166 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:42:24.918000
2025-06-30 16:42:19.166 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 5736
2025-06-30 16:42:19.236 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:42:19.244 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:42:19.254 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:42:19.442 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:42:19.447 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:42:19.448 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:42:19.448 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:42:19.450 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1212cabe-558e-11f0-957e-dc4546c07870; requestId: 7e4869c1-26a2-4c0a-8f1e-f9c5cab72021_joyinside; asr: 今天北京有雷阵雨，温度和昨天差不多，现在31度，有风; 响应时间: 0; JD机器人回复: 今天北京有雷阵雨，温度和昨天差不多，
2025-06-30 16:42:19.450 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:42:19.450 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:42:19.450 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:42:19.456 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:42:19.549 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:42:22.750 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你会做什么呢？, 时间戳: 2025-06-30 16:42:28.503000
2025-06-30 16:42:23.041 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:42:23.042 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:42:23.047 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1212cabe-558e-11f0-957e-dc4546c07870; requestId: 7e4869c1-26a2-4c0a-8f1e-f9c5cab72021_joyinside; asr: ，你会做什么呢？; 响应时间: 0; JD机器人回复: 
2025-06-30 16:42:23.047 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:42:23.047 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:42:23.047 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:42:24.457 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:42:32.735 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:42:32.736 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:42:35.051 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:42:35.051 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:42:37.804 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:42:37.804 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:42:39.290 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:42:39.290 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:42:44.103 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:42:44.104 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:42:50.951 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 小日本的游戏永远, 时间戳: 2025-06-30 16:42:56.706000
2025-06-30 16:42:51.258 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:42:51.259 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:42:51.267 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1212cabe-558e-11f0-957e-dc4546c07870; requestId: 7e4869c1-26a2-4c0a-8f1e-f9c5cab72021_joyinside; asr: 小日本的游戏永远; 响应时间: 0; JD机器人回复: 
2025-06-30 16:42:51.267 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:42:51.267 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:42:51.268 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:42:53.569 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:42:53.571 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:42:55.683 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:42:55.685 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:42:57.239 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:42:57.239 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:43:00.958 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:43:00.958 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:43:05.784 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:43:05.785 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:43:09.345 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:43:09.346 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:43:12.545 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:43:12.545 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:43:15.164 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，他们以为是你们这边也不知道, 时间戳: 2025-06-30 16:43:20.918000
2025-06-30 16:43:15.479 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:43:15.479 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:43:15.480 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1212cabe-558e-11f0-957e-dc4546c07870; requestId: 7e4869c1-26a2-4c0a-8f1e-f9c5cab72021_joyinside; asr: ，他们以为是你们这边也不知道; 响应时间: 0; JD机器人回复: 
2025-06-30 16:43:15.480 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:43:15.480 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:43:15.480 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:43:18.679 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:43:20.782 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:43:23.282 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:43:23.282 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:43:32.658 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，但是没有拿这种教程我就是看到这个完了, 时间戳: 2025-06-30 16:43:38.410000
2025-06-30 16:43:32.968 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:43:32.969 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:43:32.972 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1212cabe-558e-11f0-957e-dc4546c07870; requestId: 7e4869c1-26a2-4c0a-8f1e-f9c5cab72021_joyinside; asr: ，但是没有拿这种教程我就是看到这个完了; 响应时间: 0; JD机器人回复: 
2025-06-30 16:43:32.972 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:43:32.972 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:43:32.972 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:43:37.049 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，今天北京，你这个这个要去现场的话，得先, 时间戳: 2025-06-30 16:43:42.803000
2025-06-30 16:43:38.246 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:43:43.999000
2025-06-30 16:43:38.246 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1196
2025-06-30 16:43:38.340 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:43:38.351 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:43:38.362 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:43:38.697 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:43:38.705 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:43:38.875 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:43:38.878 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:43:38.878 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:43:38.884 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:43:38.890 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1212cabe-558e-11f0-957e-dc4546c07870; requestId: 7e4869c1-26a2-4c0a-8f1e-f9c5cab72021_joyinside; asr: ，今天北京，你这个这个要去现场的话，得先; 响应时间: 0; JD机器人回复: 今天北京有雷阵雨，温度在23°到31°之间，体感温度32°，相对湿度59%，
2025-06-30 16:43:38.891 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:43:38.891 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:43:38.892 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:43:38.967 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:43:38.978 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:43:41.462 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你来吃饭你吃下来你就能, 时间戳: 2025-06-30 16:43:47.215000
2025-06-30 16:43:41.750 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:43:41.751 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:43:41.754 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1212cabe-558e-11f0-957e-dc4546c07870; requestId: 7e4869c1-26a2-4c0a-8f1e-f9c5cab72021_joyinside; asr: 你来吃饭你吃下来你就能; 响应时间: 0; JD机器人回复: 
2025-06-30 16:43:41.754 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:43:41.754 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:43:41.754 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:43:41.901 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:43:43.084 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:43:47.498 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 他，他回答天气很快，但是他回答其他问题, 时间戳: 2025-06-30 16:43:53.251000
2025-06-30 16:43:47.797 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:43:47.798 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:43:47.805 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1212cabe-558e-11f0-957e-dc4546c07870; requestId: 7e4869c1-26a2-4c0a-8f1e-f9c5cab72021_joyinside; asr: 他，他回答天气很快，但是他回答其他问题; 响应时间: 0; JD机器人回复: 
2025-06-30 16:43:47.805 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:43:47.805 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:43:47.805 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:43:54.857 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，要么就是多测一下，问一下敏感问题，看一下有没有这种已回忘回的情况。嗯，现在问他问题基本上不回, 时间戳: 2025-06-30 16:44:00.610000
2025-06-30 16:43:55.164 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:43:55.165 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:43:55.167 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1212cabe-558e-11f0-957e-dc4546c07870; requestId: 7e4869c1-26a2-4c0a-8f1e-f9c5cab72021_joyinside; asr: ，要么就是多测一下，问一下敏感问题，看一下有没有这种已回忘回的情况。嗯，现在问他问题基本上不回; 响应时间: 0; JD机器人回复: 
2025-06-30 16:43:55.167 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:43:55.167 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:43:55.167 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:44:00.668 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，就是时间很长，它会冒几个字出来，我我还找没找到原因, 时间戳: 2025-06-30 16:44:06.402000
2025-06-30 16:44:00.985 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:44:00.986 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:44:00.992 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1212cabe-558e-11f0-957e-dc4546c07870; requestId: 7e4869c1-26a2-4c0a-8f1e-f9c5cab72021_joyinside; asr: ，就是时间很长，它会冒几个字出来，我我还找没找到原因; 响应时间: 0; JD机器人回复: 
2025-06-30 16:44:00.992 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:44:00.992 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:44:00.992 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:44:02.685 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，唉, 时间戳: 2025-06-30 16:44:08.439000
2025-06-30 16:44:02.954 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:44:02.956 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:44:02.960 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1212cabe-558e-11f0-957e-dc4546c07870; requestId: 7e4869c1-26a2-4c0a-8f1e-f9c5cab72021_joyinside; asr: ，唉; 响应时间: 0; JD机器人回复: 
2025-06-30 16:44:02.960 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:44:02.960 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:44:02.960 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:44:05.100 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:44:07.296 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:44:07.296 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:44:15.528 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你是说天气晚点重来呗，他那个工具, 时间戳: 2025-06-30 16:44:21.280000
2025-06-30 16:44:15.685 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-06-30 16:44:15.685 - chat_with_robot - voice.py - stop - line 401 - INFO - 已停止local_streaming检测器
2025-06-30 16:44:15.828 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:44:15.829 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:44:15.835 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1212cabe-558e-11f0-957e-dc4546c07870; requestId: 7e4869c1-26a2-4c0a-8f1e-f9c5cab72021_joyinside; asr: ，你是说天气晚点重来呗，他那个工具; 响应时间: 0; JD机器人回复: 
2025-06-30 16:44:15.835 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:44:15.835 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:44:15.835 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
