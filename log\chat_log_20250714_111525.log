2025-07-14 11:15:27.822 - chat_with_robot - chat_with_robot.py - <module> - line 640 - INFO - use_action: dont
2025-07-14 11:15:27.822 - chat_with_robot - chat_with_robot.py - <module> - line 641 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-14 11:15:27.823 - chat_with_robot - chat_with_robot.py - init_websocket - line 319 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1752462927823&accessNonce=9df1e903-0815-4741-92b4-d191834f094b&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=ca7443bd-6060-11f0-ace8-dc4546c07870&requestId=fa5c6901-0c0f-40f3-b0f9-74a55ba37dc5_joyinside&accessSign=f6caa8cde390ece060fc2c3ee2ca1140, request_id: fa5c6901-0c0f-40f3-b0f9-74a55ba37dc5_joyinside
2025-07-14 11:15:27.823 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-14 11:15:27.823 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-14 11:15:28.376 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-14 11:15:28.632 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-07-14 11:15:30.034 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-14 11:15:30.035 - chat_with_robot - voice.py - _setup_audio_stream - line 324 - INFO - 使用音频设备: 1
2025-07-14 11:15:30.035 - chat_with_robot - voice.py - _setup_audio_stream - line 325 - INFO - channels: 4 <class 'int'>
2025-07-14 11:15:30.035 - chat_with_robot - voice.py - _setup_audio_stream - line 326 - INFO - rate: 44100.0 <class 'float'>
2025-07-14 11:15:30.104 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-14 11:15:30.104 - chat_with_robot - voice.py - init_wakeup - line 311 - INFO - 本地流式KWS检测器启动成功
2025-07-14 11:15:31.105 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-14 11:15:31.105 - chat_with_robot - chat_with_robot.py - play_audio - line 525 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-07-14 11:15:31.107 - chat_with_robot - chat_with_robot.py - play_audio - line 527 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-07-14 11:15:31.107 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 84 - INFO - Windows 系统，使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-07-14 11:15:38.643 - chat_with_robot - voice.py - detect_callback - line 426 - INFO - [wakeup] 检测到唤醒词
2025-07-14 11:15:38.643 - chat_with_robot - voice.py - end_streaming - line 225 - INFO - [end recording]...
2025-07-14 11:15:38.709 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-14 11:15:38.709 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-14 11:15:38.711 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-07-14 11:15:38.711 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-07-14 11:15:38.713 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-07-14 11:15:38.713 - chat_with_robot - voice.py - start_streaming - line 221 - INFO - [start recording]...
2025-07-14 11:15:38.714 - chat_with_robot - voice.py - run - line 484 - INFO - [run] 持续监听状态...
2025-07-14 11:15:43.388 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想知道成都今天天气, 时间戳: 2025-07-14 11:15:43.308000
2025-07-14 11:15:45.548 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-14 11:15:45.421000
2025-07-14 11:15:45.548 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 2113
2025-07-14 11:15:45.548 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-14 11:15:45.559 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-14 11:15:45.562 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-14 11:15:45.562 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-14 11:15:45.562 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-14 11:15:45.562 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-14 11:15:45.562 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-14 11:15:45.799 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-14 11:15:45.800 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-14 11:15:45.807 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-14 11:15:45.807 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-14 11:15:45.807 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-14 11:15:45.808 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-14 11:15:45.808 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-14 11:15:46.106 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-14 11:15:46.116 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-14 11:15:46.127 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-14 11:15:46.127 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-14 11:15:46.127 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-14 11:15:46.127 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-14 11:15:46.127 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-14 11:15:46.406 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-14 11:15:46.410 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-14 11:15:46.421 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-14 11:15:46.421 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-14 11:15:46.421 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-14 11:15:46.422 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-14 11:15:46.422 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-14 11:15:46.709 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-14 11:15:46.713 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-14 11:15:46.713 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-14 11:15:46.713 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-14 11:15:46.713 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-14 11:15:46.713 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-14 11:15:46.713 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-14 11:15:46.955 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-14 11:15:46.956 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-14 11:15:46.963 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-14 11:15:46.964 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-14 11:15:46.964 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-14 11:15:46.964 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-14 11:15:46.964 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-14 11:15:46.964 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-14 11:15:46.974 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: ca7443bd-6060-11f0-ace8-dc4546c07870; requestId: fa5c6901-0c0f-40f3-b0f9-74a55ba37dc5_joyinside; asr: 我想知道成都今天天气; 响应时间: 0; JD机器人回复: 今天成都晴，最高气温35度，最低气温26度，体感温度36度，西南风3级，相对湿度55%，紫外线很强，能见度10公里，空气质量不错。适合外出活动，但要注意防晒哦！
2025-07-14 11:15:46.974 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-14 11:15:46.974 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-14 11:15:46.974 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-14 11:15:53.714 - chat_with_robot - voice.py - detect_callback - line 426 - INFO - [wakeup] 检测到唤醒词
2025-07-14 11:15:53.714 - chat_with_robot - voice.py - end_streaming - line 225 - INFO - [end recording]...
2025-07-14 11:15:53.781 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-14 11:15:53.781 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-14 11:15:53.781 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-07-14 11:15:53.781 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-07-14 11:15:53.782 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-07-14 11:15:53.782 - chat_with_robot - voice.py - start_streaming - line 221 - INFO - [start recording]...
2025-07-14 11:15:53.838 - chat_with_robot - voice.py - run - line 484 - INFO - [run] 持续监听状态...
2025-07-14 11:15:54.237 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-14 11:15:54.157000
2025-07-14 11:15:55.521 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-14 11:15:55.423000
2025-07-14 11:15:55.521 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1266
2025-07-14 11:15:55.546 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-14 11:15:55.553 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-14 11:15:55.557 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-14 11:15:55.557 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-14 11:15:55.557 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-14 11:15:55.557 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-14 11:15:55.558 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-14 11:15:55.631 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-14 11:15:55.632 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-14 11:15:55.635 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: ca7443bd-6060-11f0-ace8-dc4546c07870; requestId: fa5c6901-0c0f-40f3-b0f9-74a55ba37dc5_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 你好呀！我是东东，超级全能王！
2025-07-14 11:15:55.636 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-14 11:15:55.636 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-14 11:15:55.636 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-14 11:15:55.637 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-14 11:15:55.637 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-14 11:15:55.738 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 354 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-14 11:15:56.251 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退下, 时间戳: 2025-07-14 11:15:56.170000
2025-07-14 11:15:56.260 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-14 11:15:56.260 - chat_with_robot - chat_with_robot.py - play_audio - line 525 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-07-14 11:15:56.261 - chat_with_robot - chat_with_robot.py - play_audio - line 527 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-07-14 11:15:57.706 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-14 11:15:57.605000
2025-07-14 11:15:57.706 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1435
2025-07-14 11:15:57.726 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-14 11:15:57.992 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-14 11:15:57.994 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-14 11:15:58.284 - chat_with_robot - voice.py - detect_callback - line 426 - INFO - [wakeup] 检测到唤醒词
2025-07-14 11:15:58.284 - chat_with_robot - voice.py - end_streaming - line 225 - INFO - [end recording]...
2025-07-14 11:15:58.350 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-14 11:15:58.350 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-14 11:15:58.351 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-07-14 11:15:58.352 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-07-14 11:15:58.352 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-07-14 11:15:58.352 - chat_with_robot - voice.py - start_streaming - line 221 - INFO - [start recording]...
2025-07-14 11:15:58.360 - chat_with_robot - voice.py - run - line 484 - INFO - [run] 持续监听状态...
2025-07-14 11:16:01.062 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退出, 时间戳: 2025-07-14 11:16:00.981000
2025-07-14 11:16:01.064 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-14 11:16:01.064 - chat_with_robot - chat_with_robot.py - play_audio - line 525 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-07-14 11:16:01.064 - chat_with_robot - chat_with_robot.py - play_audio - line 527 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-07-14 11:16:02.747 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-14 11:16:02.641000
2025-07-14 11:16:02.747 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1660
2025-07-14 11:16:02.754 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-14 11:16:03.009 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-14 11:16:03.009 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
