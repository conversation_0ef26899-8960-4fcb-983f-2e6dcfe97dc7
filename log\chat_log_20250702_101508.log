2025-07-02 10:15:09.239 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-07-02 10:15:09.240 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-02 10:15:09.256 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751422509255&accessNonce=aef4add1-9a40-4f14-9e53-b11a3d695383&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=60a9a4d8-56ea-11f0-b9d2-dc4546c07870&requestId=320d8080-97b2-43be-b1cd-b805351485a1_joyinside&accessSign=e74e7e647ad06e65b3665707a67be807, request_id: 320d8080-97b2-43be-b1cd-b805351485a1_joyinside
2025-07-02 10:15:09.256 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-02 10:15:09.257 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-02 10:15:09.758 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-02 10:15:10.051 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-07-02 10:15:11.627 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-02 10:15:11.628 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-07-02 10:15:11.628 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-07-02 10:15:11.628 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-07-02 10:15:11.696 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-02 10:15:11.696 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-07-02 10:15:14.475 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-02 10:15:14.475 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 10:15:14.541 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 10:15:14.542 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 10:15:15.950 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 10:15:15.960 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-02 10:15:18.676 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你会什么技能？, 时间戳: 2025-07-02 10:15:25.069000
2025-07-02 10:15:20.887 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 10:15:27.226000
2025-07-02 10:15:20.887 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 2157
2025-07-02 10:15:23.647 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:15:23.649 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:15:23.649 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:15:23.659 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:15:23.661 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 60a9a4d8-56ea-11f0-b9d2-dc4546c07870; requestId: 320d8080-97b2-43be-b1cd-b805351485a1_joyinside; asr: 你会什么技能？; 响应时间: 0; JD机器人回复: 我能陪你聊天解闷，解答各种问题，比如科技、动漫、教育这些领域都很熟。有啥想聊的？
2025-07-02 10:15:23.661 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:15:23.662 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:15:23.662 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:15:25.254 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-02 10:15:31.647000
2025-07-02 10:15:26.958 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 10:15:33.326000
2025-07-02 10:15:26.958 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1679
2025-07-02 10:15:27.098 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:15:27.100 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:15:27.103 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:15:27.115 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 60a9a4d8-56ea-11f0-b9d2-dc4546c07870; requestId: 320d8080-97b2-43be-b1cd-b805351485a1_joyinside; asr: 嗯; 响应时间: 0; JD机器人回复: 有什么我可以帮忙的吗？
2025-07-02 10:15:27.115 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:15:27.115 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:15:39.270 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:15:52.437 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-02 10:15:52.437 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 10:15:52.502 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 10:15:52.503 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 10:15:52.502 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:15:52.562 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 10:15:53.521 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 10:15:53.585 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-02 10:15:54.107 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-02 10:16:00.501000
2025-07-02 10:15:54.657 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 10:15:54.661 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:15:54.666 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 10:15:54.666 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 10:15:54.669 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 60a9a4d8-56ea-11f0-b9d2-dc4546c07870; requestId: 320d8080-97b2-43be-b1cd-b805351485a1_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-02 10:15:54.669 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:15:54.669 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:15:54.669 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:15:54.768 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 10:15:55.888 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天天气怎么样？, 时间戳: 2025-07-02 10:16:02.281000
2025-07-02 10:15:57.439 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 10:16:03.812000
2025-07-02 10:15:57.439 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1531
2025-07-02 10:15:57.900 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:15:57.900 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:15:57.907 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:15:57.918 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:15:57.918 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 60a9a4d8-56ea-11f0-b9d2-dc4546c07870; requestId: 320d8080-97b2-43be-b1cd-b805351485a1_joyinside; asr: 今天天气怎么样？; 响应时间: 0; JD机器人回复: 抱歉，未能获取到东东的天气信息，请稍后再试。
2025-07-02 10:15:57.918 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:15:57.918 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:15:57.918 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:16:03.731 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-02 10:16:03.731 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 10:16:03.795 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 10:16:03.797 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 10:16:03.861 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 10:16:04.815 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 10:16:04.876 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-02 10:16:05.619 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-02 10:16:12.010000
2025-07-02 10:16:06.180 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 10:16:06.185 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 10:16:06.185 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 10:16:06.187 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:16:06.193 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 60a9a4d8-56ea-11f0-b9d2-dc4546c07870; requestId: 320d8080-97b2-43be-b1cd-b805351485a1_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-02 10:16:06.193 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:16:06.193 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:16:06.193 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:16:06.286 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 10:16:07.301 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天天气怎么样？, 时间戳: 2025-07-02 10:16:13.694000
2025-07-02 10:16:08.739 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 10:16:15.112000
2025-07-02 10:16:08.739 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1418
2025-07-02 10:16:09.206 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:16:09.207 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:16:09.209 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:16:09.215 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:16:09.219 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 60a9a4d8-56ea-11f0-b9d2-dc4546c07870; requestId: 320d8080-97b2-43be-b1cd-b805351485a1_joyinside; asr: 今天天气怎么样？; 响应时间: 0; JD机器人回复: 抱歉，未能获取到东东的天气信息，请稍后再试。
2025-07-02 10:16:09.219 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:16:09.219 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:16:09.219 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
