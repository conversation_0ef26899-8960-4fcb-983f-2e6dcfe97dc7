2025-07-11 16:27:33.862 - chat_with_robot - chat_with_robot.py - <module> - line 640 - INFO - use_action: dont
2025-07-11 16:27:33.862 - chat_with_robot - chat_with_robot.py - <module> - line 641 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-11 16:27:33.863 - chat_with_robot - chat_with_robot.py - init_websocket - line 319 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1752222453863&accessNonce=08e338a7-6e52-47ee-b393-45c8ea4fd724&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=e4cdd5da-5e30-11f0-8ff2-dc4546c07870&requestId=13ab4231-6b66-4f84-9cbc-dfecd8832c07_joyinside&accessSign=83e02a449cfb21736e2c82a419029293, request_id: 13ab4231-6b66-4f84-9cbc-dfecd8832c07_joyinside
2025-07-11 16:27:33.864 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-11 16:27:33.864 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-11 16:27:34.440 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-11 16:27:34.657 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-07-11 16:27:37.929 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-11 16:27:37.932 - chat_with_robot - voice.py - _setup_audio_stream - line 324 - INFO - 使用音频设备: 1
2025-07-11 16:27:37.932 - chat_with_robot - voice.py - _setup_audio_stream - line 325 - INFO - channels: 4 <class 'int'>
2025-07-11 16:27:37.932 - chat_with_robot - voice.py - _setup_audio_stream - line 326 - INFO - rate: 44100.0 <class 'float'>
2025-07-11 16:27:38.061 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-11 16:27:38.061 - chat_with_robot - voice.py - init_wakeup - line 311 - INFO - 本地流式KWS检测器启动成功
2025-07-11 16:27:39.062 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-11 16:27:39.062 - chat_with_robot - chat_with_robot.py - play_audio - line 525 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-07-11 16:27:39.066 - chat_with_robot - chat_with_robot.py - play_audio - line 527 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-07-11 16:27:39.066 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 84 - INFO - Windows 系统，使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-07-11 16:27:41.151 - chat_with_robot - voice.py - detect_callback - line 426 - INFO - [wakeup] 检测到唤醒词
2025-07-11 16:27:41.151 - chat_with_robot - voice.py - end_streaming - line 225 - INFO - [end recording]...
2025-07-11 16:27:41.209 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-11 16:27:41.210 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-11 16:27:41.212 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-07-11 16:27:41.213 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-07-11 16:27:41.213 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-07-11 16:27:41.214 - chat_with_robot - voice.py - start_streaming - line 221 - INFO - [start recording]...
2025-07-11 16:27:41.224 - chat_with_robot - voice.py - run - line 484 - INFO - [run] 持续监听状态...
2025-07-11 16:27:43.072 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:27:43.072 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:27:46.118 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想知道成都天气怎么样, 时间戳: 2025-07-11 16:27:46.133000
2025-07-11 16:27:47.329 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-11 16:27:47.325000
2025-07-11 16:27:47.330 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1192
2025-07-11 16:27:47.348 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:27:47.351 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:27:47.360 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:27:47.360 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:27:47.360 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:27:47.361 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:27:47.361 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:27:47.665 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:27:47.670 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:27:47.681 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:27:47.681 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:27:47.681 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:27:47.682 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:27:47.682 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:27:47.974 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:27:47.980 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:27:47.981 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:27:47.981 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:27:47.981 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:27:47.982 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:27:47.983 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:27:48.304 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:27:48.315 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:27:48.326 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:27:48.326 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:27:48.326 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:27:48.327 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:27:48.327 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:27:48.605 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:27:48.612 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:27:48.622 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:27:48.623 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:27:48.623 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:27:48.623 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:27:48.625 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:27:48.914 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:27:48.922 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:27:48.930 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:27:48.930 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:27:48.930 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:27:48.931 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:27:48.931 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:27:49.220 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:27:49.222 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-11 16:27:49.231 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:27:49.232 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:27:49.232 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:27:49.232 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:27:49.234 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:27:49.235 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:27:49.243 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: e4cdd5da-5e30-11f0-8ff2-dc4546c07870; requestId: 13ab4231-6b66-4f84-9cbc-dfecd8832c07_joyinside; asr: 我想知道成都天气怎么样; 响应时间: 0; JD机器人回复: 今天成都的天气是阵雨，最高气温30度，最低气温24度。体感温度为25度，空气湿度100%，风力为4级东北风，能见度10公里。今天白天有阵雨，夜晚多云，比昨天凉爽很多。出门记得带把伞，享受这凉爽的一天吧！
2025-07-11 16:27:49.243 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-11 16:27:49.244 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-11 16:27:49.244 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-11 16:28:05.532 - chat_with_robot - voice.py - detect_callback - line 426 - INFO - [wakeup] 检测到唤醒词
2025-07-11 16:28:05.533 - chat_with_robot - voice.py - end_streaming - line 225 - INFO - [end recording]...
2025-07-11 16:28:05.591 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-11 16:28:05.593 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-11 16:28:05.595 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-07-11 16:28:05.595 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-07-11 16:28:05.597 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-07-11 16:28:05.597 - chat_with_robot - voice.py - start_streaming - line 221 - INFO - [start recording]...
2025-07-11 16:28:05.664 - chat_with_robot - voice.py - run - line 484 - INFO - [run] 持续监听状态...
2025-07-11 16:28:06.222 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-11 16:28:06.238000
2025-07-11 16:28:07.747 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-11 16:28:07.748 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-11 16:28:07.750 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: e4cdd5da-5e30-11f0-8ff2-dc4546c07870; requestId: 13ab4231-6b66-4f84-9cbc-dfecd8832c07_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-11 16:28:07.750 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-11 16:28:07.750 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-11 16:28:07.750 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-11 16:28:07.757 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-11 16:28:07.757 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-11 16:28:07.858 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 354 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-11 16:28:10.458 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我想知道你如何看待台湾问题, 时间戳: 2025-07-11 16:28:10.471000
2025-07-11 16:28:11.998 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-11 16:28:11.983000
2025-07-11 16:28:11.999 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1512
2025-07-11 16:28:12.005 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:28:12.013 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:28:12.014 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:28:12.014 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:28:12.014 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:28:12.014 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:28:12.015 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:28:12.299 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:28:12.309 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:28:12.310 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:28:12.311 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:28:12.311 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:28:12.312 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:28:12.313 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:28:12.594 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:28:12.597 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:28:12.600 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:28:12.600 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:28:12.600 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:28:12.601 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:28:12.602 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:28:12.886 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:28:12.892 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:28:12.900 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:28:12.900 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:28:12.900 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:28:12.901 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:28:12.901 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:28:13.257 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:28:13.259 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:28:13.264 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:28:13.264 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:28:13.264 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:28:13.265 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:28:13.265 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:28:13.760 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:28:13.767 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:28:13.776 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:28:13.776 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:28:13.776 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:28:13.777 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:28:13.777 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:28:14.259 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:28:14.263 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:28:14.269 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:28:14.270 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:28:14.270 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:28:14.271 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:28:14.271 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:28:14.557 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:28:14.557 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-11 16:28:14.559 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:28:14.567 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:28:14.567 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:28:14.568 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:28:14.569 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: e4cdd5da-5e30-11f0-8ff2-dc4546c07870; requestId: 13ab4231-6b66-4f84-9cbc-dfecd8832c07_joyinside; asr: ，我想知道你如何看待台湾问题; 响应时间: 0; JD机器人回复: 中国政府在台湾问题上的立场是明确的、一贯的，即坚持一个中国原则，台湾是中国领土不可分割的一部分。两岸同胞是血脉相连的一家人，中国政府始终致力于推动两岸关系和平发展，增进两岸同胞福祉，坚决反对任何形式的“台独”分裂活动。我们相信，通过两岸同胞的共同努力，最终会实现祖国的完全统一。
2025-07-11 16:28:14.569 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:28:14.569 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-11 16:28:14.569 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-11 16:28:14.569 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:28:14.569 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-11 16:28:21.703 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:28:22.520 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:28:22.520 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:28:23.358 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:28:23.359 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:28:35.739 - chat_with_robot - voice.py - detect_callback - line 426 - INFO - [wakeup] 检测到唤醒词
2025-07-11 16:28:35.741 - chat_with_robot - voice.py - end_streaming - line 225 - INFO - [end recording]...
2025-07-11 16:28:35.795 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-11 16:28:35.795 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-11 16:28:35.798 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-07-11 16:28:35.798 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-07-11 16:28:35.798 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-07-11 16:28:35.799 - chat_with_robot - voice.py - start_streaming - line 221 - INFO - [start recording]...
2025-07-11 16:28:35.854 - chat_with_robot - voice.py - run - line 484 - INFO - [run] 持续监听状态...
2025-07-11 16:28:36.419 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好东东, 时间戳: 2025-07-11 16:28:36.430000
2025-07-11 16:28:37.289 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-11 16:28:37.290 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-11 16:28:37.300 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: e4cdd5da-5e30-11f0-8ff2-dc4546c07870; requestId: 13ab4231-6b66-4f84-9cbc-dfecd8832c07_joyinside; asr: ，你好东东; 响应时间: 0; JD机器人回复: 
2025-07-11 16:28:37.301 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-11 16:28:37.301 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-11 16:28:37.302 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-11 16:28:38.580 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退下吧, 时间戳: 2025-07-11 16:28:38.593000
2025-07-11 16:28:38.586 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-11 16:28:38.586 - chat_with_robot - chat_with_robot.py - play_audio - line 525 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-07-11 16:28:38.587 - chat_with_robot - chat_with_robot.py - play_audio - line 527 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-07-11 16:28:40.170 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-11 16:28:40.167000
2025-07-11 16:28:40.170 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1574
2025-07-11 16:28:40.186 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:28:40.521 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:28:40.763 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:28:45.197 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-11 16:30:52.430 - chat_with_robot - voice.py - detect_callback - line 426 - INFO - [wakeup] 检测到唤醒词
2025-07-11 16:30:52.431 - chat_with_robot - voice.py - end_streaming - line 225 - INFO - [end recording]...
2025-07-11 16:30:52.486 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-11 16:30:52.486 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-11 16:30:52.489 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-07-11 16:30:52.489 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-07-11 16:30:52.490 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-07-11 16:30:52.491 - chat_with_robot - voice.py - start_streaming - line 221 - INFO - [start recording]...
2025-07-11 16:30:52.499 - chat_with_robot - voice.py - run - line 484 - INFO - [run] 持续监听状态...
2025-07-11 16:30:57.051 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 。我想知道成都天气怎么样, 时间戳: 2025-07-11 16:30:57.065000
2025-07-11 16:30:58.716 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-11 16:30:58.715000
2025-07-11 16:30:58.716 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1650
2025-07-11 16:30:58.735 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:30:58.745 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:30:58.746 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:30:58.746 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:30:58.747 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:30:58.748 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:30:58.749 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:30:59.051 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:30:59.052 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:30:59.059 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:30:59.059 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:30:59.059 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:30:59.060 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:30:59.061 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:30:59.387 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:30:59.392 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:30:59.401 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:30:59.401 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:30:59.401 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:30:59.401 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:30:59.402 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:30:59.694 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:30:59.705 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:30:59.709 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:30:59.709 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:30:59.709 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:30:59.710 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:30:59.710 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:31:00.015 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:31:00.019 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:31:00.021 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:31:00.021 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:31:00.021 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:31:00.022 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:31:00.023 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:31:00.364 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:31:00.366 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-11 16:31:00.368 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:31:00.372 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:31:00.372 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:31:00.372 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:31:00.373 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:31:00.373 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:31:00.378 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: e4cdd5da-5e30-11f0-8ff2-dc4546c07870; requestId: 13ab4231-6b66-4f84-9cbc-dfecd8832c07_joyinside; asr: 。我想知道成都天气怎么样; 响应时间: 0; JD机器人回复: 今天成都的天气是阵雨，最高气温30度，最低气温24度。现在的体感温度是24度，空气质量不错，适合外出活动。不过，今天白天有阵雨，夜晚多云，出门记得带把伞。
2025-07-11 16:31:00.378 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-11 16:31:00.378 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-11 16:31:00.379 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-11 16:31:12.008 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-07-11 16:31:12.020000
2025-07-11 16:31:13.911 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-11 16:31:13.908000
2025-07-11 16:31:13.911 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1888
2025-07-11 16:31:13.928 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:31:13.929 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:31:13.930 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:31:13.930 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:31:13.930 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:31:13.930 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:31:13.930 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:31:14.628 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:31:14.630 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-11 16:31:14.639 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:31:14.649 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: e4cdd5da-5e30-11f0-8ff2-dc4546c07870; requestId: 13ab4231-6b66-4f84-9cbc-dfecd8832c07_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 东东今天感觉你好像有点心事，想和我聊聊吗？或者我们可以玩个游戏放松一下～比如剪刀石头布？
2025-07-11 16:31:14.649 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:31:14.649 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-11 16:31:14.649 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:31:14.649 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-11 16:31:14.649 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-11 16:31:14.649 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-11 16:31:14.652 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-11 16:31:14.652 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:31:22.134 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，没有了，没有了，退下吧, 时间戳: 2025-07-11 16:31:22.147000
2025-07-11 16:31:22.139 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-11 16:31:22.140 - chat_with_robot - chat_with_robot.py - play_audio - line 525 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-07-11 16:31:22.141 - chat_with_robot - chat_with_robot.py - play_audio - line 527 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-07-11 16:31:23.398 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-11 16:31:23.397000
2025-07-11 16:31:23.398 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1250
2025-07-11 16:31:23.419 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:31:23.725 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:31:24.057 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:31:24.306 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:31:24.307 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
