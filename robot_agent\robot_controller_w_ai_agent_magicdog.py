import time
import sys
import os
from typing import List

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from util.logger import logger

# 导入实际执行动作的 RobotController
from .robot_controller_magicdog import MagicdogController, SKILL_INTERVAL

from robot_agent.utils.robot_controller_utils import valid_start_state
from .utils.robot_controller_utils import COMPATIBLE_STANDING_STATES, STATE_DOWN

class MagicDogControllerWithAiAgent(MagicdogController):
    def __init__(self, commander):
        super().__init__(commander)
        _skill_id_to_name = {
            1: "前进", 2: "后退", 3: "左平移", 4: "右平移", 5: "左转", 6: "右转",
            7: "起立", 8: "坐下", 9: "趴下", 10: "向前跳", 11: "打招呼",
            19: "摇尾巴撒娇", 23: "抬头", 26: "悠闲", 27: "兴奋", 28: "停止运动", 
            29: "沮丧", 30: "倾听", 31: "关闭遇到障碍停止模式",
            32: "开启跟随模式", 33: "开启遇到障碍停止模式", 
            35: "低速", 36: "中速", 37: "高速", 
            38: "关闭跟随模式", 41: "扭身跳", 42: "舞蹈",
            0: "无动作",
        }
        self.name_to_skill_id = {v: k for k, v in _skill_id_to_name.items()}

        if "无动作" not in self.name_to_skill_id:
            self.name_to_skill_id["无动作"] = 0

        logger.info("MagicDogControllerWithAiAgent 启动完成")
    
     

    def process_action_list(self, action_names: List[str]):
        """
        处理来自AI Agent或其他来源的动作名称列表。

        Args:
            action_names (list[str]): 一个包含要执行的动作名称的列表,
                                       例如 ["前进", "前进", "左转"].
        """
        logger.info(f"接收到动作序列: {action_names}")
        if not action_names:
            logger.warning("接收到的动作列表为空，不执行任何操作。")
            return
        
        self.stop_flag = False
        first_action_checked = False
        
        for action_name in action_names:
            if self.check_stop:
                logger.info("检测到停止命令，中断当前动作序列。")
                return
            
            if action_name in self.name_to_skill_id:
                skill_id = self.name_to_skill_id[action_name]
                if skill_id == 0: # 特殊处理 "无动作"
                    logger.info("识别到 '无动作'，跳过。")
                    continue
                
         

                logger.info(f"准备执行动作: {action_name} (ID: {skill_id})")
                try:
                    self.control_robot(skill_id)
                    # 动作之间添加延迟
                    # time.sleep(0.1)
                except Exception as e:
                    logger.error(f"执行动作 '{action_name}' (ID: {skill_id}) 时发生错误: {e}")
                    # 根据需要决定是否中断序列
                    # self.stop()
                    # break
            elif action_name == "请求超时":
                 logger.warning(f"识别到动作 '{action_name}'，跳过。")
                 continue
            else:
                logger.warning(f"未知的动作名称: '{action_name}'，无法执行。")
                # 根据需要决定是否中断序列
                # break

        logger.info(f"动作序列 {action_names} 处理完毕。")

    # 速度
    def execute_skill_low_speed(self):
        self.commander.run("移动模式")
        if self.check_stop:
            return
        self.commander.run("低速")
        time.sleep(0.1)

    def execute_skill_medium_speed(self):
        self.commander.run("移动模式")
        if self.check_stop:
            return
        self.commander.run("中速")
        time.sleep(0.1)

    def execute_skill_high_speed(self):
        self.commander.run("移动模式")
        if self.check_stop:
            return
        self.commander.run("高速")
        time.sleep(0.1)

    # 轴指令
    motion_interval = 0.19

    def execute_skill_go_forward(self, speed=motion_interval):
        self.commander.run("移动模式")
        if self.check_stop:
            return
        for i in range(3):
            self.commander.run("前进(中速度)")
            if self.check_stop:
                return
            time.sleep(speed)

    def execute_skill_go_backward(self, speed=motion_interval):
        self.commander.run("移动模式")
        if self.check_stop:
            return
        for i in range(3):
            self.commander.run("后退(中速度)")
            if self.check_stop:
                return
            time.sleep(speed)

    def execute_skill_left_move(self, speed=motion_interval):
        self.commander.run("移动模式")
        if self.check_stop:
            return
        for i in range(3):
            self.commander.run("左平移(中速度)")
            if self.check_stop:
                return
            time.sleep(speed)

    def execute_skill_right_move(self, speed=motion_interval):
        self.commander.run("移动模式")
        if self.check_stop:
            return
        for i in range(3):
            self.commander.run("右平移(中速度)")
            if self.check_stop:
                return
            time.sleep(speed)
    
    def execute_skill_turn_left(self, speed=motion_interval):
        self.commander.run("移动模式")
        if self.check_stop:
            return
        for i in range(3):
            self.commander.run("左转(中速度)")
            if self.check_stop:
                return
            time.sleep(speed)
    
    def execute_skill_turn_right(self, speed=motion_interval):
        self.commander.run("移动模式")
        if self.check_stop:
            return
        for i in range(3):
            self.commander.run("右转(中速度)")
            if self.check_stop:
                return
            time.sleep(speed)

    # 定位
    def execute_skill_locator(self, angle = 0):
        """
        skill. 定位
        """
        # 1. 等待机器狗到可以随音乐跳舞的状态
        if not self.wait_for_state(COMPATIBLE_STANDING_STATES):
            print("机器人未处于站立状态，跳过定位")
            return
        
        # if self.check_stop:
        #     return
        
        self.turn_robot(angle)

    def calculate_turns(self, angle):
        """
        根据角度计算需要转向的次数
        
        Args:
            angle: 需要转动的角度 (0-180度)
            
        Returns:
            int: 需要转向的次数
        """
        # 示例映射:
        # 30度 = 1次
        # 90度 = 3次
        # 180度 = 8次
        if angle == 0:
            return 0
        elif angle <= 30:
            return 1
        elif angle <= 90:
            return int(round(angle / 30))
        elif angle <= 180:
            return int(round(angle / 25.7))  # 180/8 = 22.5
        else:
            return int(round(angle / 25.7))

    def turn_robot(self, direction):
        """根据输入的方向控制机器人转向"""
        # 规范化方向到0-359范围
        #direction = 0
        #direction = direction % 360
        
        # 确定转向方向和次数
        if direction <= 180:
            # 左转(逆时针)
            turn_direction = "左转"
            turns_needed = self.calculate_turns(direction)
        else:
            # 右转(顺时针)
            turn_direction = "右转"
            turns_needed = self.calculate_turns(360 - direction)
        
        logger.info(f"转向{direction}度: {turn_direction}转{turns_needed}次")
        
        # 执行转向动作
        if turn_direction == "左转":
            for i in range(turns_needed):
                # if self.check_stop:
                #     break
                self.execute_skill_turn_left()
                time.sleep(0.1)
        else:
            for i in range(turns_needed):
                # if self.check_stop:
                #     break
                self.execute_skill_turn_right()
                time.sleep(0.1)
        
        logger.info(f"转向{direction}度完成")






        