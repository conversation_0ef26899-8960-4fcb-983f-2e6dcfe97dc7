2025-07-29 16:12:29.720 - chat_with_robot - chat_with_robot.py - <module> - line 658 - INFO - use_action: dont
2025-07-29 16:12:29.721 - chat_with_robot - chat_with_robot.py - <module> - line 659 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-29 16:12:29.727 - chat_with_robot - chat_with_robot.py - init_websocket - line 326 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1753776749727&accessNonce=433da67d-e0d5-4ed2-90bc-9eeaedffbbf6&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=c5555336-6c53-11f0-8330-dc4546c07870&requestId=05ca337b-1b80-4f46-8e2c-3e9ebddfb25f_joyinside&accessSign=c84e4af25d7d8ea77a94d27c0f81c2fc, request_id: 05ca337b-1b80-4f46-8e2c-3e9ebddfb25f_joyinside
2025-07-29 16:12:29.728 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-29 16:12:29.728 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-29 16:12:30.231 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-29 16:12:30.484 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-07-29 16:12:31.985 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-29 16:12:31.986 - chat_with_robot - voice.py - _setup_audio_stream - line 336 - INFO - 使用音频设备: 1
2025-07-29 16:12:31.986 - chat_with_robot - voice.py - _setup_audio_stream - line 337 - INFO - channels: 4 <class 'int'>
2025-07-29 16:12:31.986 - chat_with_robot - voice.py - _setup_audio_stream - line 338 - INFO - rate: 44100.0 <class 'float'>
2025-07-29 16:12:32.074 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-29 16:12:32.074 - chat_with_robot - voice.py - init_wakeup - line 323 - INFO - 本地流式KWS检测器启动成功
2025-07-29 16:12:33.074 - chat_with_robot - chat_with_robot.py - play_audio - line 535 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-29 16:12:33.074 - chat_with_robot - chat_with_robot.py - play_audio - line 543 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-07-29 16:12:36.077 - chat_with_robot - chat_with_robot.py - play_audio - line 545 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-07-29 16:12:36.078 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 82 - INFO - 使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-07-29 16:12:36.079 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 85 - INFO - 统一音频控制器播放完成: asserts/tts/dog_ok.mp3
2025-07-29 16:12:47.332 - chat_with_robot - voice.py - detect_callback - line 438 - INFO - [wakeup] 检测到唤醒词
2025-07-29 16:12:47.332 - chat_with_robot - voice.py - end_streaming - line 237 - INFO - [end recording]...
2025-07-29 16:12:47.397 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-29 16:12:47.397 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-29 16:12:47.398 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-07-29 16:12:47.398 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-07-29 16:12:47.398 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-07-29 16:12:47.399 - chat_with_robot - voice.py - start_streaming - line 233 - INFO - [start recording]...
2025-07-29 16:12:47.408 - chat_with_robot - voice.py - run - line 496 - INFO - [run] 持续监听状态...
2025-07-29 16:12:48.633 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-29 16:12:48.633 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-29 16:12:51.679 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想知道成都今天天气, 时间戳: 2025-07-29 16:12:52.039000
2025-07-29 16:12:53.495 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-29 16:12:53.836000
2025-07-29 16:12:53.496 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1797
2025-07-29 16:12:53.506 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:12:53.517 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:12:53.517 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-29 16:12:53.517 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-07-29 16:12:53.517 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-07-29 16:12:53.517 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15120 bytes, 持续时间: 2.0
2025-07-29 16:12:53.517 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-07-29 16:12:53.776 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:12:53.781 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:12:54.091 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:12:54.094 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:12:54.395 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:12:54.397 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:12:54.637 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:12:54.649 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:12:54.900 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:12:54.902 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-29 16:12:54.909 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:12:54.921 - chat_with_robot - chat_with_robot.py - _task_worker - line 401 - INFO - session_id: c5555336-6c53-11f0-8330-dc4546c07870; requestId: 05ca337b-1b80-4f46-8e2c-3e9ebddfb25f_joyinside; asr: 我想知道成都今天天气; 响应时间: 0; JD机器人回复: 今天成都多云，最高气温38度，最低气温25度，体感温度37度，东南风2级，相对湿度50%，紫外线很强，能见度10公里，空气一般。适合外出，但要注意防晒。
2025-07-29 16:12:54.921 - chat_with_robot - chat_with_robot.py - _task_worker - line 403 - INFO - 等待控制完成
2025-07-29 16:12:54.921 - chat_with_robot - chat_with_robot.py - _task_worker - line 408 - INFO - 等待音频播放完成
2025-07-29 16:12:56.143 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-07-29 16:12:56.144 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-07-29 16:12:56.144 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-29 16:12:56.144 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-07-29 16:12:56.144 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-07-29 16:12:56.144 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 17064 bytes, 持续时间: 2.0
2025-07-29 16:12:56.144 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-07-29 16:12:59.073 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-07-29 16:12:59.074 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-07-29 16:12:59.074 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-29 16:12:59.074 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-07-29 16:12:59.074 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-07-29 16:12:59.074 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 15768 bytes, 持续时间: 2.0
2025-07-29 16:12:59.074 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-07-29 16:13:01.801 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-07-29 16:13:01.801 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-07-29 16:13:01.801 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-29 16:13:01.801 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-07-29 16:13:01.801 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-07-29 16:13:01.801 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 13608 bytes, 持续时间: 2.0
2025-07-29 16:13:01.801 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-07-29 16:13:04.219 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-07-29 16:13:04.219 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-07-29 16:13:04.219 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-29 16:13:04.221 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-07-29 16:13:04.221 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-07-29 16:13:04.221 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 10800 bytes, 持续时间: 2.0
2025-07-29 16:13:04.221 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-07-29 16:13:05.460 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 现在是不是快了？, 时间戳: 2025-07-29 16:13:05.825000
2025-07-29 16:13:06.393 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-29 16:13:06.745000
2025-07-29 16:13:06.393 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 920
2025-07-29 16:13:06.404 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-29 16:13:06.404 - chat_with_robot - chat_with_robot.py - _task_worker - line 418 - INFO - 任务完成，继续
2025-07-29 16:13:06.404 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-29 16:13:06.412 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:06.416 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:13:06.505 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 353 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-07-29 16:13:06.638 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-07-29 16:13:06.638 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-07-29 16:13:06.638 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-29 16:13:06.639 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-07-29 16:13:06.639 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-07-29 16:13:06.639 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 11448 bytes, 持续时间: 2.0
2025-07-29 16:13:06.639 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-07-29 16:13:06.670 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:06.676 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:13:06.942 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:06.946 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:13:07.223 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:07.233 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:13:07.494 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:07.496 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:13:07.496 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-29 16:13:07.507 - chat_with_robot - chat_with_robot.py - _task_worker - line 401 - INFO - session_id: c5555336-6c53-11f0-8330-dc4546c07870; requestId: 05ca337b-1b80-4f46-8e2c-3e9ebddfb25f_joyinside; asr: 现在是不是快了？; 响应时间: 0; JD机器人回复: 嚯！你说的是快到什么呀？快到吃饭时间？还是快下雨啦？不过现在确实挺热的，要不要听听我刚发现的一个天气小知识？比如为什么云有时候看起来像棉花糖？
2025-07-29 16:13:07.507 - chat_with_robot - chat_with_robot.py - _task_worker - line 403 - INFO - 等待控制完成
2025-07-29 16:13:07.507 - chat_with_robot - chat_with_robot.py - _task_worker - line 408 - INFO - 等待音频播放完成
2025-07-29 16:13:08.075 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-29 16:13:09.059 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-07-29 16:13:09.059 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-07-29 16:13:09.059 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-29 16:13:09.059 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-07-29 16:13:09.060 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-07-29 16:13:09.060 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 14040 bytes, 持续时间: 2.0
2025-07-29 16:13:09.060 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-07-29 16:13:09.783 - chat_with_robot - voice.py - detect_callback - line 438 - INFO - [wakeup] 检测到唤醒词
2025-07-29 16:13:09.783 - chat_with_robot - voice.py - end_streaming - line 237 - INFO - [end recording]...
2025-07-29 16:13:09.848 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-29 16:13:09.848 - chat_with_robot - chat_with_robot.py - _task_worker - line 418 - INFO - 任务完成，继续
2025-07-29 16:13:09.848 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-29 16:13:09.848 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-07-29 16:13:09.848 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-07-29 16:13:09.849 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-07-29 16:13:09.849 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-07-29 16:13:09.849 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-07-29 16:13:09.849 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-07-29 16:13:09.849 - chat_with_robot - voice.py - start_streaming - line 233 - INFO - [start recording]...
2025-07-29 16:13:09.907 - chat_with_robot - voice.py - run - line 496 - INFO - [run] 持续监听状态...
2025-07-29 16:13:10.330 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-29 16:13:10.695000
2025-07-29 16:13:10.602 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-29 16:13:10.603 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-29 16:13:10.614 - chat_with_robot - chat_with_robot.py - _task_worker - line 401 - INFO - session_id: c5555336-6c53-11f0-8330-dc4546c07870; requestId: 05ca337b-1b80-4f46-8e2c-3e9ebddfb25f_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-29 16:13:10.614 - chat_with_robot - chat_with_robot.py - _task_worker - line 403 - INFO - 等待控制完成
2025-07-29 16:13:10.614 - chat_with_robot - chat_with_robot.py - _task_worker - line 408 - INFO - 等待音频播放完成
2025-07-29 16:13:10.614 - chat_with_robot - chat_with_robot.py - _task_worker - line 418 - INFO - 任务完成，继续
2025-07-29 16:13:12.350 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退出, 时间戳: 2025-07-29 16:13:12.715000
2025-07-29 16:13:12.350 - chat_with_robot - chat_with_robot.py - play_audio - line 535 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-29 16:13:12.351 - chat_with_robot - chat_with_robot.py - play_audio - line 543 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-07-29 16:13:12.352 - chat_with_robot - chat_with_robot.py - play_audio - line 545 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-07-29 16:13:13.415 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-29 16:13:13.755000
2025-07-29 16:13:13.415 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1040
2025-07-29 16:13:13.427 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:13.755 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:14.070 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:14.320 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:14.321 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-29 16:13:15.613 - chat_with_robot - voice.py - detect_callback - line 438 - INFO - [wakeup] 检测到唤醒词
2025-07-29 16:13:15.613 - chat_with_robot - voice.py - end_streaming - line 237 - INFO - [end recording]...
2025-07-29 16:13:15.678 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-29 16:13:15.678 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-29 16:13:15.678 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-07-29 16:13:15.678 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-07-29 16:13:15.678 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-07-29 16:13:15.678 - chat_with_robot - voice.py - start_streaming - line 233 - INFO - [start recording]...
2025-07-29 16:13:15.687 - chat_with_robot - voice.py - run - line 496 - INFO - [run] 持续监听状态...
2025-07-29 16:13:20.398 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我想知道回锅肉怎么做, 时间戳: 2025-07-29 16:13:20.764000
2025-07-29 16:13:21.637 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-29 16:13:21.982000
2025-07-29 16:13:21.637 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1218
2025-07-29 16:13:21.652 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:21.659 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:13:21.660 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-29 16:13:21.660 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-07-29 16:13:21.660 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-07-29 16:13:21.660 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 21384 bytes, 持续时间: 2.0
2025-07-29 16:13:21.661 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-07-29 16:13:21.937 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:21.943 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:13:22.245 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:22.246 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:13:22.487 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:22.488 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:13:22.725 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:22.735 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:13:22.998 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:23.009 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:13:23.402 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:23.403 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-29 16:13:23.409 - chat_with_robot - chat_with_robot.py - _task_worker - line 447 - INFO - 存入音频
2025-07-29 16:13:23.420 - chat_with_robot - chat_with_robot.py - _task_worker - line 401 - INFO - session_id: c5555336-6c53-11f0-8330-dc4546c07870; requestId: 05ca337b-1b80-4f46-8e2c-3e9ebddfb25f_joyinside; asr: ，我想知道回锅肉怎么做; 响应时间: 0; JD机器人回复: 哇！回锅肉呀～我记着你上次说过妈妈教的步骤呢！先把五花肉煮熟，切成薄片，然后在锅里爆香蒜和豆瓣酱，再把肉片炒到卷起来，最后加上青蒜和调料！滋啦啦的声音听起来就超级香！你也试着做过吗？要不要和我分享下你的成果呀？
2025-07-29 16:13:23.420 - chat_with_robot - chat_with_robot.py - _task_worker - line 403 - INFO - 等待控制完成
2025-07-29 16:13:23.420 - chat_with_robot - chat_with_robot.py - _task_worker - line 408 - INFO - 等待音频播放完成
2025-07-29 16:13:25.308 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-07-29 16:13:25.308 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-07-29 16:13:25.308 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-29 16:13:25.308 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-07-29 16:13:25.309 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-07-29 16:13:25.309 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 12960 bytes, 持续时间: 2.0
2025-07-29 16:13:25.309 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-07-29 16:13:27.725 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-07-29 16:13:27.727 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-07-29 16:13:27.727 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-29 16:13:27.727 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-07-29 16:13:27.727 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-07-29 16:13:27.727 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 12744 bytes, 持续时间: 2.0
2025-07-29 16:13:27.727 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-07-29 16:13:30.147 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-07-29 16:13:30.147 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-07-29 16:13:30.147 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-29 16:13:30.147 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-07-29 16:13:30.147 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-07-29 16:13:30.147 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 10152 bytes, 持续时间: 2.0
2025-07-29 16:13:30.147 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-07-29 16:13:32.568 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-07-29 16:13:32.568 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-07-29 16:13:32.568 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-29 16:13:32.568 - chat_with_robot - audio_player.py - _play_single_audio - line 171 - INFO - 准备播放大模型合成的语音
2025-07-29 16:13:32.568 - chat_with_robot - audio_player.py - _play_single_audio - line 186 - INFO - 完整音频估算长度: 2.00秒
2025-07-29 16:13:32.568 - chat_with_robot - audio_player.py - _play_single_audio - line 190 - INFO - 音频数据大小: 10152 bytes, 持续时间: 2.0
2025-07-29 16:13:32.568 - chat_with_robot - audio_player.py - _play_single_audio - line 193 - INFO - 使用统一音频控制器播放大模型语音（包含嘴巴动作控制）
2025-07-29 16:13:33.624 - chat_with_robot - voice.py - detect_callback - line 438 - INFO - [wakeup] 检测到唤醒词
2025-07-29 16:13:33.624 - chat_with_robot - voice.py - end_streaming - line 237 - INFO - [end recording]...
2025-07-29 16:13:33.688 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-29 16:13:33.688 - chat_with_robot - chat_with_robot.py - _task_worker - line 418 - INFO - 任务完成，继续
2025-07-29 16:13:33.688 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-29 16:13:33.688 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-07-29 16:13:33.688 - chat_with_robot - audio_player.py - _play_single_audio - line 201 - INFO - 大模型语音播放完成（包含嘴巴动作）
2025-07-29 16:13:33.688 - chat_with_robot - audio_player.py - _play_loop - line 124 - INFO - 音频播放完成
2025-07-29 16:13:33.688 - chat_with_robot - audio_player.py - _play_loop - line 132 - INFO - 所有音频任务已完成
2025-07-29 16:13:33.688 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-07-29 16:13:33.689 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-07-29 16:13:33.689 - chat_with_robot - voice.py - start_streaming - line 233 - INFO - [start recording]...
2025-07-29 16:13:33.748 - chat_with_robot - voice.py - run - line 496 - INFO - [run] 持续监听状态...
2025-07-29 16:13:34.237 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好东东, 时间戳: 2025-07-29 16:13:34.603000
2025-07-29 16:13:34.708 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-29 16:13:34.709 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-29 16:13:34.715 - chat_with_robot - chat_with_robot.py - _task_worker - line 401 - INFO - session_id: c5555336-6c53-11f0-8330-dc4546c07870; requestId: 05ca337b-1b80-4f46-8e2c-3e9ebddfb25f_joyinside; asr: ，你好东东; 响应时间: 0; JD机器人回复: 
2025-07-29 16:13:34.715 - chat_with_robot - chat_with_robot.py - _task_worker - line 403 - INFO - 等待控制完成
2025-07-29 16:13:34.715 - chat_with_robot - chat_with_robot.py - _task_worker - line 408 - INFO - 等待音频播放完成
2025-07-29 16:13:34.715 - chat_with_robot - chat_with_robot.py - _task_worker - line 418 - INFO - 任务完成，继续
2025-07-29 16:13:36.135 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退出, 时间戳: 2025-07-29 16:13:36.500000
2025-07-29 16:13:36.137 - chat_with_robot - chat_with_robot.py - play_audio - line 535 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-29 16:13:36.137 - chat_with_robot - chat_with_robot.py - play_audio - line 543 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-07-29 16:13:36.137 - chat_with_robot - chat_with_robot.py - play_audio - line 545 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-07-29 16:13:37.185 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-29 16:13:37.529000
2025-07-29 16:13:37.185 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1029
2025-07-29 16:13:37.196 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:37.505 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:37.753 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:38.099 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:38.357 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:38.618 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-29 16:13:38.618 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-29 16:13:39.173 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-07-29 16:13:39.173 - chat_with_robot - voice.py - stop - line 433 - INFO - 已停止local_streaming检测器
