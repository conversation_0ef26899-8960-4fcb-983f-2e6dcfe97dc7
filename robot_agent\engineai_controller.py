"""
@Author: lidong<PERSON><EMAIL>
@Create Date: 2025.04.09
@Description: EngineAI机器人控制器

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
"""
from robot_agent.engineai_config import ENGINEAI_CONFIG
from util.logger import logger
import time

class RobotControllerEngineAI:
    """EngineAI机器人控制器"""
    
    def __init__(self, commander):
        """初始化EngineAI机器人控制器
        
        Args:
            commander: EngineAI机器人通信管理器
        """
        self.commander = commander
        self.command_map = ENGINEAI_CONFIG.get("command_map", {})
        if not self.command_map:
            logger.warning("未找到EngineAI机器人命令映射配置")
        self.can_walk = False
    
    def control_robot(self, command_id):
        """控制EngineAI机器人执行命令
        
        Args:
            command_id: 命令ID或命令名称
        """
        try:
            # 处理无动作
            if command_id == -1:
                logger.info("无需执行动作")
                return True

            # 设置行走权限
            if command_id == 4:
                self.can_walk = True
                logger.info("获得行走权限，可以执行行走动作")
            if command_id == 5:
                self.can_walk = False
                logger.info("关闭行走权限，不可执行行走动作")

            # 检查行走权限
            if command_id == 3 and not self.can_walk:
                logger.warning("未获得行走权限，拒绝执行行走动作")
                return False
            
            # 获取映射命令
            if command_id in self.command_map:
                commands = self.command_map[command_id]
            else:
                logger.error(f"未知的命令ID: {command_id}")
                return False
            
            # 启动加载
            if command_id == 0:
                success = True
                for cmd in commands:
                    time.sleep(1.5)
                    if not self.commander.run(cmd):
                        success = False
                        logger.error(f"执行命令 {cmd} 失败")
                return success

            # 顺序执行命令序列
            success = True
            for cmd in commands:
                time.sleep(0.05)
                if cmd == "delay-3000":
                    time.sleep(3)
                else:
                    # 正常执行其他命令
                    if not self.commander.run(cmd):
                        success = False
                        logger.error(f"执行命令 {cmd} 失败")
            
            return success
        except Exception as e:
            logger.error(f"控制机器人时出错: {str(e)}")
            return False
    
    def stop(self):
        """停止机器人当前动作"""
        logger.info("尝试停止机器人动作，但目前不支持中断")
        # return self.commander.run("stop")
