2025-07-02 11:03:11.150 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-07-02 11:03:11.151 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-02 11:03:11.166 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751425391166&accessNonce=a2341e14-ed9f-4c0c-843b-7ea4f3a8c6c4&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=166a5d85-56f1-11f0-b5b6-dc4546c07870&requestId=857d37d5-fa3f-40f6-aef8-ef7f79bf6372_joyinside&accessSign=9015fb20f044a12e9cd296180f4ee047, request_id: 857d37d5-fa3f-40f6-aef8-ef7f79bf6372_joyinside
2025-07-02 11:03:11.168 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-02 11:03:11.169 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-02 11:03:11.638 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-02 11:03:11.839 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-07-02 11:03:13.304 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-02 11:03:13.306 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-07-02 11:03:13.306 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-07-02 11:03:13.306 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-07-02 11:03:13.367 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-02 11:03:13.367 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-07-02 11:03:16.749 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-02 11:03:16.749 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 11:03:16.816 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 11:03:16.816 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 11:03:18.130 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 11:03:18.134 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-02 11:03:20.977 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想克隆我的声音, 时间戳: 2025-07-02 11:03:27.347000
2025-07-02 11:03:21.946 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:03:28.311000
2025-07-02 11:03:21.946 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 964
2025-07-02 11:03:22.024 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:03:22.033 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:03:22.039 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:03:22.367 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:03:22.378 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:03:22.715 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:03:22.724 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:03:23.082 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:03:23.093 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:03:23.421 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:03:23.421 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:03:23.421 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:03:23.432 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 166a5d85-56f1-11f0-b5b6-dc4546c07870; requestId: 857d37d5-fa3f-40f6-aef8-ef7f79bf6372_joyinside; asr: 我想克隆我的声音; 响应时间: 0; JD机器人回复: 太好了！那我们先来确认一下，你是想让我克隆你自己的声音，对吧？确认好了就可以开始啦！我们先来读一段话，好不好？请跟我读：“今天天气真好我们一起去郊游吧”
2025-07-02 11:03:23.432 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:03:23.432 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:03:23.592 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 11:03:25.261 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:03:28.186 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:03:30.583 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 。是的，我想克隆, 时间戳: 2025-07-02 11:03:36.953000
2025-07-02 11:03:31.182 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:03:37.550000
2025-07-02 11:03:31.183 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 597
2025-07-02 11:03:31.188 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 11:03:31.188 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:03:31.188 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 11:03:31.209 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:03:31.250 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:03:31.253 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:03:31.263 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:03:31.289 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-07-02 11:03:31.531 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:03:31.532 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:03:32.010 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:03:32.010 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:03:32.013 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:03:32.024 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 166a5d85-56f1-11f0-b5b6-dc4546c07870; requestId: 857d37d5-fa3f-40f6-aef8-ef7f79bf6372_joyinside; asr: 。是的，我想克隆; 响应时间: 0; JD机器人回复: 太棒了！那我们现在就开始吧。请你跟着我读这句话：“今天天气真好我们一起去郊游吧” 记得要一字不漏哦！
2025-07-02 11:03:32.024 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:03:32.024 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:03:34.283 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:03:36.398 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:03:41.433 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:03:41.433 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:03:47.197 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天天气真好我们一起去郊游吧, 时间戳: 2025-07-02 11:03:53.565000
2025-07-02 11:03:48.003 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:03:54.371000
2025-07-02 11:03:48.003 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 806
2025-07-02 11:03:48.328 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:03:48.329 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:03:48.338 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:03:48.591 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:03:48.591 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:03:48.596 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:03:48.607 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 166a5d85-56f1-11f0-b5b6-dc4546c07870; requestId: 857d37d5-fa3f-40f6-aef8-ef7f79bf6372_joyinside; asr: 今天天气真好我们一起去郊游吧; 响应时间: 0; JD机器人回复: 哎呀，录音少于3秒，失败啦！你要继续克隆试试看，还是退出换个玩法？稍等一下哦！
2025-07-02 11:03:48.607 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:03:48.607 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:03:55.689 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:03:57.103 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:03:57.103 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:03:59.850 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，继续克隆, 时间戳: 2025-07-02 11:04:06.220000
2025-07-02 11:04:00.488 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:04:06.856000
2025-07-02 11:04:00.488 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 636
2025-07-02 11:04:00.616 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:04:00.616 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:04:00.627 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:04:00.637 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:04:00.638 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 166a5d85-56f1-11f0-b5b6-dc4546c07870; requestId: 857d37d5-fa3f-40f6-aef8-ef7f79bf6372_joyinside; asr: ，继续克隆; 响应时间: 0; JD机器人回复: 这次我没学会你的声音，要不要再给我一次机会？不想继续也可以退出哦。
2025-07-02 11:04:00.638 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:04:00.638 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:04:00.638 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:04:07.583 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:04:09.311 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 继续, 时间戳: 2025-07-02 11:04:15.681000
2025-07-02 11:04:09.873 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:04:16.242000
2025-07-02 11:04:09.874 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 561
2025-07-02 11:04:09.989 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:04:09.990 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:04:09.990 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:04:10.001 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:04:10.001 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 166a5d85-56f1-11f0-b5b6-dc4546c07870; requestId: 857d37d5-fa3f-40f6-aef8-ef7f79bf6372_joyinside; asr: 继续; 响应时间: 0; JD机器人回复: 这次我没学会你的声音，要不要再给我一次机会？不想继续也可以退出哦。
2025-07-02 11:04:10.001 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:04:10.001 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:04:10.001 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:04:16.848 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:04:18.731 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 再给你一次机会，重重复一遍, 时间戳: 2025-07-02 11:04:25.101000
2025-07-02 11:04:19.854 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:04:26.223000
2025-07-02 11:04:19.854 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1122
2025-07-02 11:04:19.923 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:04:19.934 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:04:19.945 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:04:20.279 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:04:20.290 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:04:20.622 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:04:20.633 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:04:20.649 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:04:20.649 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:04:20.654 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:04:20.664 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 166a5d85-56f1-11f0-b5b6-dc4546c07870; requestId: 857d37d5-fa3f-40f6-aef8-ef7f79bf6372_joyinside; asr: 再给你一次机会，重重复一遍; 响应时间: 0; JD机器人回复: 好的，以下是之前回答的重复：

“好的，我明白了。请问有什么我可以帮助您的吗？”
2025-07-02 11:04:20.664 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:04:20.664 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:04:23.069 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:04:25.082 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:04:28.002 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:04:28.002 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:04:30.587 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，哎，太蠢了吧, 时间戳: 2025-07-02 11:04:36.956000
2025-07-02 11:04:31.786 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:04:38.153000
2025-07-02 11:04:31.786 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1197
2025-07-02 11:04:31.869 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:04:31.874 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:04:31.881 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:04:32.208 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:04:32.208 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:04:32.216 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:04:32.227 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 166a5d85-56f1-11f0-b5b6-dc4546c07870; requestId: 857d37d5-fa3f-40f6-aef8-ef7f79bf6372_joyinside; asr: ，哎，太蠢了吧; 响应时间: 0; JD机器人回复: 如果您有任何问题或需要帮助的地方，请随时告诉我，我会尽力提供帮助。
2025-07-02 11:04:32.227 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:04:32.227 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:04:35.304 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
