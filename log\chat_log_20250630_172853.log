2025-06-30 17:28:54.416 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-06-30 17:28:54.416 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-06-30 17:28:54.440 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751275734439&accessNonce=3f07f389-dd62-47e5-bebb-e1003a320ce7&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=a40e1d06-5594-11f0-b3b0-dc4546c07870&requestId=15ef805a-d069-4551-8681-d47d05d29c2b_joyinside&accessSign=ea14c666da2761b82d7db199414905f3, request_id: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside
2025-06-30 17:28:54.461 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-06-30 17:28:54.462 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-06-30 17:28:54.842 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-06-30 17:28:54.991 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-06-30 17:28:56.752 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-06-30 17:28:56.754 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-06-30 17:28:56.754 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-06-30 17:28:56.754 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-06-30 17:28:56.841 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-06-30 17:28:56.842 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-06-30 17:29:54.943 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:29:54.944 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:29:55.007 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:29:55.007 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:29:56.369 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:29:56.380 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:29:58.739 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:29:58.739 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:29:59.923 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:29:59.924 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:29:59.983 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:29:59.984 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:30:01.005 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:30:01.071 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:30:01.536 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-06-30 17:30:07.263000
2025-06-30 17:30:01.943 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:30:01.957 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:30:01.966 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-06-30 17:30:01.966 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:30:01.966 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:30:01.966 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:30:06.868 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我想知道，如何你如何看待日本人？, 时间戳: 2025-06-30 17:30:12.591000
2025-06-30 17:30:08.124 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:30:13.857000
2025-06-30 17:30:08.125 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1266
2025-06-30 17:30:08.209 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:30:08.220 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:30:08.228 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:30:08.594 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:30:08.602 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:30:09.065 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:30:09.070 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:30:09.381 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:30:09.387 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:30:09.649 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:30:09.651 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:30:10.024 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:30:10.033 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:30:10.283 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:30:10.283 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:30:10.605 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:30:10.606 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:30:10.606 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:30:10.617 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: ，我想知道，如何你如何看待日本人？; 响应时间: 0; JD机器人回复: 每个国家都有独特的文化和历史背景，我们应该以开放和尊重的态度去了解和学习。日本在科技、文化、艺术等方面有很多值得借鉴的地方，比如动漫、音乐、设计等，都很有创意和影响力。不过，也要注意到不同文化之间的差异，保持客观和理性的态度，这样才能更好地促进文化交流和理解。
2025-06-30 17:30:10.617 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:30:10.617 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:30:11.516 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:30:15.380 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:30:20.494 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:30:23.054 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:30:25.056 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:30:28.613 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:30:30.674 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:30:34.038 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:30:34.038 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:30:41.760 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你如何看待日本右翼？, 时间戳: 2025-06-30 17:30:47.493000
2025-06-30 17:30:42.908 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:30:48.628000
2025-06-30 17:30:42.908 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1135
2025-06-30 17:30:42.955 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:30:42.961 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:30:42.994 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:30:43.321 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:30:43.329 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:30:43.674 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:30:43.682 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:30:43.991 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:30:43.991 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:30:44.305 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:30:44.325 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:30:44.648 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:30:44.658 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:30:44.990 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:30:45.002 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:30:45.320 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:30:45.358 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:30:45.360 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:30:45.649 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:30:45.671 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:30:45.671 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:30:45.684 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 你如何看待日本右翼？; 响应时间: 0; JD机器人回复: 对于任何国家的政治立场，我们都应该基于事实和理性去分析。日本右翼的一些观点和行为确实存在争议，比如对历史问题的态度，可能会引发周边国家的不满。但也要看到，日本社会是多元的，不同声音的存在是正常的。我们应当关注那些促进和平与合作的积极力量，共同维护地区的稳定与发展。
2025-06-30 17:30:45.684 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:30:45.684 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:30:48.400 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:30:52.016 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:30:54.058 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:30:56.589 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:30:59.628 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:31:01.966 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:31:03.667 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:31:06.109 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:31:08.659 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:31:08.659 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:31:12.600 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你如何看待美国霸权？, 时间戳: 2025-06-30 17:31:18.328000
2025-06-30 17:31:13.823 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:31:19.531000
2025-06-30 17:31:13.823 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1203
2025-06-30 17:31:13.866 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:13.876 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:13.876 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:31:14.155 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:14.160 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:14.520 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:14.529 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:14.866 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:14.870 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:15.173 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:15.177 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:15.480 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:15.491 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:15.790 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:15.791 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:16.039 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:31:16.137 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:16.141 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:16.478 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:16.486 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:16.728 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:16.728 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:16.728 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:31:16.740 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 你如何看待美国霸权？; 响应时间: 0; JD机器人回复: 美国作为全球重要国家，在多个领域都有显著影响力。虽然美国在某些方面确实存在霸权行为，比如在国际事务中采取单边主义，但这并不代表所有美国人的立场。国际社会需要的是更多的合作与对话，而不是单方面的压制。我们应当推动多边主义，促进各国平等交流，共同应对全球性挑战，实现互利共赢。
2025-06-30 17:31:16.740 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:31:16.741 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:31:18.471 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:31:21.911 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:31:24.780 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:31:27.125 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:31:27.125 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:31:27.203 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:31:27.204 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:31:27.204 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:31:27.246 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:31:27.437 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:31:28.261 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:31:28.326 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:31:36.338 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你如何看待俄乌冲突与巴以冲突？, 时间戳: 2025-06-30 17:31:42.071000
2025-06-30 17:31:37.809 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:31:43.538000
2025-06-30 17:31:37.809 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1467
2025-06-30 17:31:37.886 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:37.894 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:37.894 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:31:38.282 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:38.282 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:38.628 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:38.631 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:38.937 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:38.948 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:39.318 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:39.326 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:39.633 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:39.644 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:39.902 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:39.905 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:40.258 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:40.259 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:40.587 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:40.591 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:40.901 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:31:40.901 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:31:40.902 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:31:40.914 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 你如何看待俄乌冲突与巴以冲突？; 响应时间: 0; JD机器人回复: 俄乌冲突和巴以冲突都是复杂的地缘政治问题，涉及历史、民族、宗教等多重因素。这些冲突给当地人民带来了深重的苦难，也影响了国际社会的稳定。我们应当支持通过和平对话和外交途径解决问题，尊重各国的主权和领土完整，同时关注人道主义援助，帮助受影响的人民。国际社会需要共同努力，推动和平进程，避免冲突升级，维护世界和平。
2025-06-30 17:31:40.914 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:31:40.915 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:31:41.894 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:31:45.300 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:31:48.807 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:31:50.284 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:31:51.091 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:31:52.004 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:31:54.651 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:31:54.651 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:31:55.340 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:31:57.894 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:31:59.933 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:32:02.294 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:32:02.295 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:32:04.005 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:32:06.855 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:32:08.477 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:32:08.477 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:32:37.463 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:32:37.463 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:32:45.168 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 就是，就是啊，我看的我脑袋都是懵的，我跟你说嘛, 时间戳: 2025-06-30 17:32:50.898000
2025-06-30 17:32:45.494 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:32:45.494 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:32:45.500 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 就是，就是啊，我看的我脑袋都是懵的，我跟你说嘛; 响应时间: 0; JD机器人回复: 
2025-06-30 17:32:45.500 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:32:45.500 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:32:45.500 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:32:47.716 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我完全不知道他, 时间戳: 2025-06-30 17:32:53.441000
2025-06-30 17:32:48.235 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 17:32:48.235 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:32:48.235 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: ，我完全不知道他; 响应时间: 0; JD机器人回复: 
2025-06-30 17:32:48.235 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:32:48.235 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:32:48.235 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:32:48.241 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:32:48.241 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:32:48.348 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 17:32:52.577 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 那个在搞什么，我就觉得嗯，好复杂, 时间戳: 2025-06-30 17:32:58.302000
2025-06-30 17:32:52.878 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:32:52.878 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:32:52.882 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 那个在搞什么，我就觉得嗯，好复杂; 响应时间: 0; JD机器人回复: 
2025-06-30 17:32:52.882 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:32:52.882 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:32:52.882 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:32:57.616 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:32:58.572 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:32:58.572 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:33:01.854 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:33:01.854 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:33:04.152 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:33:04.153 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:33:15.850 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:33:15.851 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:33:20.686 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-06-30 17:33:26.419000
2025-06-30 17:33:21.022 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:33:21.023 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:33:21.030 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 
2025-06-30 17:33:21.030 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:33:21.030 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:33:21.030 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:33:27.588 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，相当于是把整个东西给拆了, 时间戳: 2025-06-30 17:33:33.319000
2025-06-30 17:33:27.881 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:33:27.882 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:33:27.889 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: ，相当于是把整个东西给拆了; 响应时间: 0; JD机器人回复: 
2025-06-30 17:33:27.889 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:33:27.889 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:33:27.889 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:33:29.297 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:33:33.499 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:33:33.501 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:33:54.553 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，给你一个好超级好用的，可以让对话次数变多的, 时间戳: 2025-06-30 17:34:00.253000
2025-06-30 17:33:54.878 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:33:54.878 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:33:54.889 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: ，给你一个好超级好用的，可以让对话次数变多的; 响应时间: 0; JD机器人回复: 
2025-06-30 17:33:54.889 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:33:54.889 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:33:54.889 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:33:56.306 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:33:57.006 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:33:57.007 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:34:28.113 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:34:28.120 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:34:28.964 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-06-30 17:34:34.694000
2025-06-30 17:34:28.974 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:34:28.974 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:34:28.986 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 
2025-06-30 17:34:28.986 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:34:28.986 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:34:28.986 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:34:32.885 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 17:34:32.887 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:34:32.887 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:34:32.988 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 17:34:35.507 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，正常的只是名字叫的很奇怪, 时间戳: 2025-06-30 17:34:41.226000
2025-06-30 17:34:35.856 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:34:35.857 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:34:35.862 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: ，正常的只是名字叫的很奇怪; 响应时间: 0; JD机器人回复: 
2025-06-30 17:34:35.862 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:34:35.863 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:34:35.863 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:34:39.492 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:34:57.144 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:34:57.145 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:35:06.491 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:35:06.492 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:35:06.557 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:35:06.557 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:35:07.611 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:35:07.675 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:35:16.923 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:35:16.923 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:35:16.997 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:35:16.997 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:35:18.323 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:35:18.389 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:35:21.214 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:35:21.214 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:35:21.279 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:35:21.279 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:35:22.364 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:35:22.433 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:36:36.905 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:36:36.905 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:36:36.963 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:36:36.963 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:36:38.044 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:36:38.107 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:36:45.135 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:36:45.136 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:36:45.198 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:36:45.198 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:36:46.533 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:36:46.602 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:37:37.174 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，不是个例会，然后把这些跟他们成分尸一样，对就是分尸一个地方，一个地方他给分出来，分出来以后他就有这些地方, 时间戳: 2025-06-30 17:37:42.900000
2025-06-30 17:37:37.516 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:37:37.516 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:37:37.517 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: ，不是个例会，然后把这些跟他们成分尸一样，对就是分尸一个地方，一个地方他给分出来，分出来以后他就有这些地方; 响应时间: 0; JD机器人回复: 
2025-06-30 17:37:37.517 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:37:37.517 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:37:37.517 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:37:38.351 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:39:01.548 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:39:01.548 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:39:01.624 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:39:01.624 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:39:02.705 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:39:02.771 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:39:16.157 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:39:16.157 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:39:16.221 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:39:16.222 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:39:17.524 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:39:17.593 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:39:23.652 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:39:23.652 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:39:23.718 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:39:23.721 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:39:25.059 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:39:25.134 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:39:39.778 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:39:39.778 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:39:39.838 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:39:39.840 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:39:41.175 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:39:41.243 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:39:44.065 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:39:44.065 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:39:44.160 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:39:44.160 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:39:45.219 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:39:45.294 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:41:10.384 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:41:10.384 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:41:10.445 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:41:10.445 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:41:11.836 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:41:11.899 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:41:53.126 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:41:53.127 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:41:53.190 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:41:53.190 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:41:54.538 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:41:54.604 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:42:42.549 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:42:42.550 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:42:42.615 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:42:42.615 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:42:43.735 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:42:43.806 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:43:28.866 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:43:28.866 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:43:28.928 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:43:28.928 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:43:30.286 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:43:30.356 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:46:09.010 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:46:09.011 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:46:09.066 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:46:09.066 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:46:10.432 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:46:10.501 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:46:39.245 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:46:39.246 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:46:39.309 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:46:39.309 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:46:40.361 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:46:40.423 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:46:56.691 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:46:56.691 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:46:56.754 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:46:56.754 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:46:57.829 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:46:57.896 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:46:58.589 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:46:58.590 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:47:03.403 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你知道, 时间戳: 2025-06-30 17:47:09.130000
2025-06-30 17:47:04.767 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:47:10.495000
2025-06-30 17:47:04.767 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1365
2025-06-30 17:47:04.837 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:47:04.837 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:47:04.838 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:47:04.846 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:47:04.850 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: ，你知道; 响应时间: 0; JD机器人回复: 你好！有什么我可以帮忙的吗？
2025-06-30 17:47:04.850 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:47:04.850 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:47:04.850 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:47:06.272 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好！, 时间戳: 2025-06-30 17:47:11.998000
2025-06-30 17:47:06.572 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:47:06.573 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:47:06.582 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 你好！; 响应时间: 0; JD机器人回复: 
2025-06-30 17:47:06.582 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:47:06.582 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:47:06.582 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:47:07.689 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:47:12.786 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你知道台湾问题, 时间戳: 2025-06-30 17:47:18.513000
2025-06-30 17:47:13.303 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 17:47:13.304 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:47:13.304 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:47:13.313 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:47:13.314 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 你知道台湾问题; 响应时间: 0; JD机器人回复: 
2025-06-30 17:47:13.314 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:47:13.314 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:47:13.317 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:47:13.405 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 17:47:14.449 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 多久能解决吗？, 时间戳: 2025-06-30 17:47:20.178000
2025-06-30 17:47:15.509 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:47:21.236000
2025-06-30 17:47:15.509 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1058
2025-06-30 17:47:15.552 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:47:15.553 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:47:15.563 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:47:15.919 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:47:15.923 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:47:16.203 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:47:16.242 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:47:16.250 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:47:16.548 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:47:16.548 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:47:16.870 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:47:16.879 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:47:17.210 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:47:17.213 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:47:17.496 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:47:17.601 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:47:17.604 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:47:17.937 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:47:17.939 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:47:17.939 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:47:17.950 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 多久能解决吗？; 响应时间: 0; JD机器人回复: 台湾问题是中国内政，关系到国家主权和领土完整。中国政府始终坚持一个中国原则，致力于通过和平统一的方式解决台湾问题。我们相信，在全体中华儿女的共同努力下，实现祖国的完全统一是历史的必然。同时，我们也希望国际社会能够理解和支持中国政府的立场，共同维护地区的和平与稳定。
2025-06-30 17:47:17.951 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:47:17.951 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:47:20.064 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:47:23.149 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:47:26.893 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:47:30.744 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:47:33.910 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:47:39.272 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:47:41.746 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:47:41.746 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:47:50.946 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:47:50.947 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:47:51.008 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:47:51.008 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:47:52.347 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:47:52.411 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:47:53.822 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东，9月3号的, 时间戳: 2025-06-30 17:47:59.551000
2025-06-30 17:47:54.575 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 17:47:54.577 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:47:54.579 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:47:54.579 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:47:54.587 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 你好，东东，9月3号的; 响应时间: 0; JD机器人回复: 
2025-06-30 17:47:54.588 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:47:54.588 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:47:54.589 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:47:54.680 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 17:47:55.863 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 台湾有什么含义吗？, 时间戳: 2025-06-30 17:48:01.591000
2025-06-30 17:47:57.331 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:48:03.059000
2025-06-30 17:47:57.333 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1468
2025-06-30 17:47:57.437 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:47:57.444 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:47:57.454 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:47:57.709 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:47:57.720 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:47:58.058 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:47:58.069 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:47:58.410 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:47:58.411 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:47:58.702 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:47:58.708 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:47:59.030 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:47:59.034 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:47:59.377 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:47:59.382 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:47:59.740 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:47:59.753 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:48:00.076 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:48:00.078 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:48:00.086 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:48:00.097 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 台湾有什么含义吗？; 响应时间: 0; JD机器人回复: 9月3日是中国人民抗日战争胜利纪念日，也是世界反法西斯战争胜利纪念日。这一天对于中国人民来说具有重大的历史意义，它象征着中国人民在抗日战争中取得的伟大胜利，以及对世界和平的贡献。关于台湾问题，中国政府始终坚持一个中国原则，台湾是中国不可分割的一部分。我们相信，在全体中华儿女的共同努力下，实现祖国的完全统一是历史的必然。
2025-06-30 17:48:00.097 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:48:00.097 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:48:01.132 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:48:04.305 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:48:08.265 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:48:12.540 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:48:14.710 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:48:18.682 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:48:18.683 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:48:19.184 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:48:21.151 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 别人, 时间戳: 2025-06-30 17:48:26.879000
2025-06-30 17:48:21.442 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:48:21.442 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:48:21.859 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:48:23.170 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:48:25.781 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:48:25.832 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:48:25.832 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:48:27.404 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 前一个月, 时间戳: 2025-06-30 17:48:33.133000
2025-06-30 17:48:28.709 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 17:48:28.711 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:48:28.715 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:48:28.715 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:48:28.716 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:48:28.726 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 前一个月; 响应时间: 0; JD机器人回复: 
2025-06-30 17:48:28.726 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:48:28.726 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:48:28.729 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:48:28.740 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 前一个月; 响应时间: 0; JD机器人回复: 
2025-06-30 17:48:28.740 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:48:28.740 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:48:28.740 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:48:28.775 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:48:28.817 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 17:48:35.784 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 是护士还是医生，一个月只工作7天左右，能拿7~8万块钱一个月，农民工香港的都比我们赚的多得多, 时间戳: 2025-06-30 17:48:41.510000
2025-06-30 17:48:36.086 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:48:36.087 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:48:36.096 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 是护士还是医生，一个月只工作7天左右，能拿7~8万块钱一个月，农民工香港的都比我们赚的多得多; 响应时间: 0; JD机器人回复: 
2025-06-30 17:48:36.096 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:48:36.096 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:48:36.096 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:48:36.151 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:48:36.746 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:48:36.748 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:48:43.057 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:48:43.058 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:48:47.947 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 。问他，习近平家族, 时间戳: 2025-06-30 17:48:53.676000
2025-06-30 17:48:48.265 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:48:53.990000
2025-06-30 17:48:48.265 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 314
2025-06-30 17:48:48.352 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:48:48.352 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:48:48.360 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:48:48.370 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:48:48.372 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 。问他，习近平家族; 响应时间: 0; JD机器人回复: 这个问题我还没学会呢，你能再问个超级有趣的问题吗？
2025-06-30 17:48:48.372 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:48:48.372 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:48:48.372 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:48:49.852 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 究竟有多少钱？, 时间戳: 2025-06-30 17:48:55.580000
2025-06-30 17:48:51.761 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:48:57.486000
2025-06-30 17:48:51.761 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1906
2025-06-30 17:48:51.868 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:48:51.868 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:48:52.313 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:48:52.322 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:48:52.937 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:48:52.938 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:48:53.254 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:48:53.257 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:48:53.392 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:48:53.606 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:48:53.606 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:48:53.606 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:48:53.618 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 究竟有多少钱？; 响应时间: 0; JD机器人回复: 关于钱的问题，具体数额需要根据具体情境和需求来确定。如果您是在询问某个具体项目、投资或预算的金额，可以提供更多信息，我可以帮助您分析或提供建议。如果是其他方面的财务问题，也请详细说明，我会尽力提供帮助。
2025-06-30 17:48:53.618 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:48:53.618 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:48:57.759 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:48:58.666 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:48:58.944 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:48:58.944 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:49:03.280 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:49:07.831 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:49:10.283 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:49:13.654 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:49:13.654 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:49:17.088 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:49:17.090 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:49:23.555 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:49:23.555 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:49:30.202 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 别人一天赚7万，我操, 时间戳: 2025-06-30 17:49:35.930000
2025-06-30 17:49:30.484 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:49:30.486 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:49:30.490 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 别人一天赚7万，我操; 响应时间: 0; JD机器人回复: 
2025-06-30 17:49:30.490 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:49:30.490 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:49:30.490 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:49:31.107 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:49:37.957 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 7天7万，一天1万，成本算5100天，我操, 时间戳: 2025-06-30 17:49:43.682000
2025-06-30 17:49:38.234 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:49:38.236 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:49:38.241 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 7天7万，一天1万，成本算5100天，我操; 响应时间: 0; JD机器人回复: 
2025-06-30 17:49:38.241 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:49:38.241 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:49:38.242 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:49:42.091 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 玩区块链买虚拟币, 时间戳: 2025-06-30 17:49:47.817000
2025-06-30 17:49:42.852 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 17:49:42.862 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:49:42.862 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:49:42.863 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:49:42.866 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 玩区块链买虚拟币; 响应时间: 0; JD机器人回复: 
2025-06-30 17:49:42.866 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:49:42.866 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:49:42.866 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:49:42.963 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 17:49:43.869 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，策略太好了, 时间戳: 2025-06-30 17:49:49.597000
2025-06-30 17:49:45.205 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:49:50.930000
2025-06-30 17:49:45.207 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1333
2025-06-30 17:49:45.294 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:45.300 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:45.300 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:49:45.660 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:45.660 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:45.924 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:45.937 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:46.360 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:46.367 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:46.709 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:46.713 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:47.090 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:47.090 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:47.398 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:47.406 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:47.445 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:49:47.734 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:47.736 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:48.096 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:48.102 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:48.407 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:48.413 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:48.759 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:48.760 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:49.099 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:49.106 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:49.384 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:49.385 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:49.786 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:49.792 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:50.073 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:50.073 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:50.393 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:50.397 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:50.739 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:50.739 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:50.786 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:49:51.089 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:51.133 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:51.375 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:51.376 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:51.806 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:51.820 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:52.103 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:52.104 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:52.436 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:52.441 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:52.751 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:52.757 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:53.072 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:49:53.073 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:49:53.073 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:49:53.084 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: ，策略太好了; 响应时间: 0; JD机器人回复: 你好！区块链和虚拟货币确实是一个充满机遇和风险的领域。在参与之前，建议你先了解以下几点：

1. 基础知识：了解区块链的基本概念、运作原理以及虚拟货币的种类和特点。这将帮助你做出更明智的投资决策。

2. 风险评估：虚拟货币市场波动性很大，价格可能在短时间内大幅波动。因此，投资前要评估自己的风险承受能力。

3. 选择平台：选择一个安全、可靠的交易平台进行交易。确保平台有良好的声誉和严格的安全措施。

4. 分散投资：不要将所有资金投入到一个项目或一种虚拟货币中，分散投资可以降低风险。

5. 持续学习：区块链技术和虚拟货币市场都在不断发展，持续学习和关注行业动态非常重要。

6. 法律法规：了解你所在国家或地区关于虚拟货币的法律法规，确保你的投资行为合法合规。

希望这些建议对你有所帮助！如果你有具体的问题或需要进一步的指导，随时告诉我。
2025-06-30 17:49:53.084 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:49:53.084 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:49:53.977 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:49:53.977 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:49:54.040 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:49:54.041 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:49:54.041 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:49:54.060 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:49:55.110 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:49:55.175 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:49:56.445 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好，东东, 时间戳: 2025-06-30 17:50:02.164000
2025-06-30 17:49:56.817 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:49:56.817 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:49:56.825 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: ，你好，东东; 响应时间: 0; JD机器人回复: 
2025-06-30 17:49:56.825 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:49:56.825 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:49:56.825 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:50:00.269 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:50:02.507 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:50:02.507 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:50:04.202 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:50:04.203 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:50:10.225 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:50:10.226 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:50:50.773 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:50:50.773 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:50:52.556 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:50:52.557 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:51:34.839 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 在ps里面，把那个眼睛全部拼好，然后该分的给它拆分出来，对，然后直接把它，对，然后直接把ps导到或者甩进去, 时间戳: 2025-06-30 17:51:40.566000
2025-06-30 17:51:35.242 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 17:51:35.249 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:51:35.252 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:51:35.252 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:51:35.255 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 在ps里面，把那个眼睛全部拼好，然后该分的给它拆分出来，对，然后直接把它，对，然后直接把ps导到或者甩进去; 响应时间: 0; JD机器人回复: 
2025-06-30 17:51:35.256 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:51:35.256 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:51:35.256 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:51:35.353 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 17:51:35.890 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，就相当于, 时间戳: 2025-06-30 17:51:41.617000
2025-06-30 17:51:36.477 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 17:51:36.484 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:51:36.484 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:51:36.485 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:51:36.485 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: ，就相当于; 响应时间: 0; JD机器人回复: 
2025-06-30 17:51:36.485 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:51:36.485 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:51:36.486 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:51:36.587 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 17:51:37.810 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:51:43.536000
2025-06-30 17:51:37.810 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1919
2025-06-30 17:51:37.866 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:37.871 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:37.881 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:51:38.273 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:38.274 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:38.422 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 17:51:38.425 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:51:38.425 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:51:38.425 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:51:38.434 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: ，就相当于; 响应时间: 0; JD机器人回复: 在Photoshop中，如果你想把眼睛的部分处理得更好，
2025-06-30 17:51:38.434 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:51:38.434 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:51:38.434 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:51:38.527 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:51:38.554 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 17:51:40.014 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你有那个PC文件吗？, 时间戳: 2025-06-30 17:51:45.741000
2025-06-30 17:51:41.217 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:51:46.942000
2025-06-30 17:51:41.217 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1201
2025-06-30 17:51:41.302 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:41.302 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:41.308 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:51:41.652 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:41.654 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:42.011 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:42.015 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:42.303 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:42.307 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:42.701 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:42.702 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:42.935 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:42.943 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:43.430 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:43.437 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:43.748 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:43.751 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:44.067 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:44.073 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:44.403 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:44.407 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:44.714 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:44.725 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:45.138 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:45.141 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:45.685 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:45.686 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:45.994 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:45.994 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:46.124 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:51:46.311 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:46.322 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:46.596 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:46.599 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:46.958 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:46.969 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:47.330 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:47.334 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:47.609 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:47.615 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:47.844 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 17:51:47.850 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:51:47.851 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:51:47.851 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:51:47.857 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 你有那个PC文件吗？; 响应时间: 0; JD机器人回复: 看起来您想了解如何在Photoshop中处理图像中的眼睛部分，并将其导出或导入到其他软件中。以下是一个可能的步骤指南：

1. 导入图像：首先，打开Photoshop并导入您想要编辑的图像。

2. 选择眼睛区域：使用选择工具（如套索工具、快速选择工具或魔棒工具）选择图像中的眼睛部分。确保选择区域准确。

3. 复制和粘贴：选中眼睛部分后，按 `Ctrl+C`（Windows）或 `Cmd+C`（Mac）复制选中的区域，然后按 `Ctrl+V`（Windows）或 `Cmd+V`（Mac）将其粘贴到一个新的图层中。

4. 编辑和调整：在新的图层中，您可以对眼睛进行任何必要的编辑和调整，例如调整颜色、对比度、亮度等。

5. 拆分图层：
2025-06-30 17:51:47.858 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:51:47.858 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:51:47.858 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:51:47.877 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:51:47.952 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 17:51:48.623 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，小度, 时间戳: 2025-06-30 17:51:54.350000
2025-06-30 17:51:49.767 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 17:51:49.772 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:51:49.776 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:51:49.776 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:51:49.783 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 你好，小度; 响应时间: 0; JD机器人回复: 
2025-06-30 17:51:49.783 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:51:49.783 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:51:49.783 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:51:49.877 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 17:51:51.448 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:51:57.174000
2025-06-30 17:51:51.448 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 2824
2025-06-30 17:51:51.521 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:51.528 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:51.533 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:51:51.819 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:51.823 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:52.174 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:52.174 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:52.440 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:52.444 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:52.776 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 17:51:52.784 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:51:52.787 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:51:52.787 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:51:52.789 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: 你好，小度; 响应时间: 0; JD机器人回复: 你好！在Photoshop中处理眼睛部分的步骤如下：

1. 导入图像：打开Photoshop并导入你想要编辑的图像。

2. 选择眼睛区域：
2025-06-30 17:51:52.789 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:51:52.789 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:51:52.789 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:51:52.882 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:51:52.888 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 17:51:53.581 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，它好容易被误换出来, 时间戳: 2025-06-30 17:51:59.307000
2025-06-30 17:51:54.648 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:52:00.373000
2025-06-30 17:51:54.649 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1066
2025-06-30 17:51:54.742 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:54.746 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:54.754 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:51:55.091 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:55.100 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:55.427 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:55.429 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:55.815 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:55.824 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:56.158 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:56.163 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:56.554 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:56.557 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:56.879 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:56.881 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:57.209 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:57.219 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:57.539 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:57.551 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:57.882 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:51:57.882 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:51:58.058 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 17:51:58.059 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:51:58.061 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:51:58.061 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:51:58.063 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: a40e1d06-5594-11f0-b3b0-dc4546c07870; requestId: 15ef805a-d069-4551-8681-d47d05d29c2b_joyinside; asr: ，它好容易被误换出来; 响应时间: 0; JD机器人回复: 你好！看起来您在使用Photoshop时遇到了一些问题。以下是处理眼睛部分的步骤，希望能帮到您：

1. 导入图像：打开Photoshop并导入您想要编辑的图像。

2. 选择眼睛区域：
   - 使用套索工具、快速选择工具或魔棒工具选择图像中的眼睛部分。确保选择区域准确。

3. 复制和粘贴：
   - 选中眼睛部分后，按 `Ctrl+C`（Windows）
2025-06-30 17:51:58.063 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:51:58.063 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:51:58.063 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:51:58.099 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:51:58.163 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 17:51:58.716 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退出, 时间戳: 2025-06-30 17:52:04.427000
2025-06-30 17:51:59.765 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:52:05.487000
2025-06-30 17:51:59.765 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1060
2025-06-30 17:51:59.832 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:00.173 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:00.484 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:00.747 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:01.231 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:01.818 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:01.921 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:02.210 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:02.628 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:03.270 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:03.523 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:03.846 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:04.174 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:04.484 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:04.794 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:05.117 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:05.696 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:06.013 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:07.221 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:07.534 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:07.862 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:52:07.863 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:52:23.645 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:52:23.645 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:52:23.697 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:52:23.698 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:52:24.755 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:52:24.765 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
