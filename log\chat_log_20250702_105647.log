2025-07-02 10:56:48.436 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-07-02 10:56:48.436 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-02 10:56:48.451 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751425008452&accessNonce=c16bb2c6-876a-48f2-b279-e1909b120837&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=324ccdf7-56f0-11f0-ad3f-dc4546c07870&requestId=3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside&accessSign=97f6764393b0efe3891ee4d15be30d6c, request_id: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside
2025-07-02 10:56:48.453 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-02 10:56:48.453 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-02 10:56:48.945 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-02 10:56:49.153 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-07-02 10:56:50.496 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-02 10:56:50.496 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-07-02 10:56:50.497 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-07-02 10:56:50.497 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-07-02 10:56:50.553 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-02 10:56:50.553 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-07-02 10:56:55.254 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-02 10:56:55.254 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 10:56:55.317 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 10:56:55.317 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 10:56:56.754 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 10:56:56.757 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-02 10:56:59.859 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想克隆我的音色, 时间戳: 2025-07-02 10:57:06.233000
2025-07-02 10:57:00.764 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 10:57:07.136000
2025-07-02 10:57:00.764 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 903
2025-07-02 10:57:00.841 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:57:00.842 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:57:00.853 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:57:01.152 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:57:01.155 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:57:01.437 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:57:01.437 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:57:01.773 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:57:01.785 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:57:02.041 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:57:02.041 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:57:02.044 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:57:02.055 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: 我想克隆我的音色; 响应时间: 0; JD机器人回复: 太好了！那我们就开始吧！首先，我需要你跟着我读一段话，这样我才能学到你的声音哦。请跟我读：“今天天气真好我们一起去郊游吧”。记得要一字不漏哦！
2025-07-02 10:57:02.055 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:57:02.055 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:57:02.106 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 太好了, 时间戳: 2025-07-02 10:57:08.480000
2025-07-02 10:57:02.722 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 10:57:09.093000
2025-07-02 10:57:02.722 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 613
2025-07-02 10:57:02.733 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 10:57:02.733 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:57:02.733 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 10:57:02.769 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 10:57:02.787 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:57:02.798 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:57:02.800 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:57:02.834 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-07-02 10:57:03.108 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:57:03.110 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:57:03.410 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:57:03.419 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:57:03.747 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:57:03.755 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:57:04.029 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:57:04.029 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:57:04.034 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:57:04.045 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: 太好了; 响应时间: 0; JD机器人回复: 哇，你真是太棒了！不过，我需要你把刚才那句话完整地读出来，这样我才能更好地学到你的声音哦。请再试一次：“今天天气真好我们一起去郊游吧”。加油！
2025-07-02 10:57:04.045 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:57:04.045 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:57:05.628 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:57:09.151 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:57:12.479 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:57:17.016 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:57:17.924 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 10:57:17.924 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:57:22.445 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，今天天气真好，我们去郊游吧, 时间戳: 2025-07-02 10:57:28.817000
2025-07-02 10:57:23.226 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 10:57:29.597000
2025-07-02 10:57:23.226 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 780
2025-07-02 10:57:23.310 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:57:23.316 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:57:23.320 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:57:23.592 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:57:23.595 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:57:23.973 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:57:23.974 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:57:24.276 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:57:24.276 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:57:24.286 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:57:24.298 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: ，今天天气真好，我们去郊游吧; 响应时间: 0; JD机器人回复: 你已经读得很好了，不过好像少了一个“一”字哦。我们再来一次好不好？请跟我读：“今天天气真好我们一起去郊游吧”。相信你这次一定能成功的！
2025-07-02 10:57:24.298 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:57:24.298 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:57:27.855 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:57:30.069 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:57:34.400 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:57:36.916 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 10:57:36.916 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:57:41.891 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，今天天气真好，我们一起去郊游吧, 时间戳: 2025-07-02 10:57:48.264000
2025-07-02 10:57:42.644 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 10:57:49.011000
2025-07-02 10:57:42.644 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 747
2025-07-02 10:57:42.723 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:57:42.730 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:57:42.735 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:57:43.091 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:57:43.099 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:57:43.684 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:57:43.684 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:57:43.695 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:57:43.706 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: ，今天天气真好，我们一起去郊游吧; 响应时间: 0; JD机器人回复: 太棒了！你读得非常完美！哎呀，声音时间不到3秒呢！再试试吗？还是退出呢？哎呀，录音少于3秒，失败啦！你要继续克隆试试看，还是退出换个玩法？
2025-07-02 10:57:43.706 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:57:43.706 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:57:45.457 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:57:50.798 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:57:57.945 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 10:57:57.945 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:57:58.015 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 10:57:59.162 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-02 10:57:59.162 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 10:57:59.227 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 10:57:59.227 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 10:58:00.277 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 10:58:00.338 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-02 10:58:00.846 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好东东, 时间戳: 2025-07-02 10:58:07.218000
2025-07-02 10:58:01.405 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 10:58:01.411 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:58:01.416 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 10:58:01.416 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 10:58:01.419 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: ，你好东东; 响应时间: 0; JD机器人回复: 
2025-07-02 10:58:01.419 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:58:01.419 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:58:01.419 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:58:01.517 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 10:58:03.030 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想克隆我的声音, 时间戳: 2025-07-02 10:58:09.401000
2025-07-02 10:58:03.920 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 10:58:10.292000
2025-07-02 10:58:03.920 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 891
2025-07-02 10:58:03.992 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:58:03.998 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:58:04.004 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:58:04.487 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:58:04.488 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:58:04.498 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:58:04.509 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: 我想克隆我的声音; 响应时间: 0; JD机器人回复: 太好了！那我们就开始吧！请跟我读这句话：“今天天气真好我们一起去郊游吧”
2025-07-02 10:58:04.509 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:58:04.509 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:58:05.165 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 太好了, 时间戳: 2025-07-02 10:58:11.538000
2025-07-02 10:58:06.610 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 10:58:12.983000
2025-07-02 10:58:06.611 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1445
2025-07-02 10:58:06.623 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 10:58:06.623 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 10:58:06.623 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:58:06.624 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 10:58:06.688 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:58:06.700 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:58:06.710 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:58:06.724 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-07-02 10:58:07.003 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:58:07.014 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:58:07.030 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:58:07.031 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:58:07.036 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:58:07.047 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: 太好了; 响应时间: 0; JD机器人回复: 看起来你很高兴！有什么我可以帮助你的吗？或者你只是想分享一下你的好心情？😊
2025-07-02 10:58:07.047 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:58:07.047 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:58:10.837 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:58:14.160 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 10:58:14.160 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:58:20.445 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-02 10:58:20.445 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 10:58:20.508 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 10:58:20.508 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 10:58:21.792 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 10:58:21.854 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-02 10:58:22.235 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好东东, 时间戳: 2025-07-02 10:58:28.608000
2025-07-02 10:58:22.813 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 10:58:22.818 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:58:22.821 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: ，你好东东; 响应时间: 0; JD机器人回复: 
2025-07-02 10:58:22.821 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:58:22.821 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 10:58:22.821 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:58:22.821 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 10:58:22.821 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:58:22.922 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 10:58:24.527 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我想克隆我的声音, 时间戳: 2025-07-02 10:58:30.900000
2025-07-02 10:58:25.491 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 10:58:31.863000
2025-07-02 10:58:25.492 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 963
2025-07-02 10:58:25.559 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:58:25.568 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:58:25.573 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:58:25.854 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:58:25.865 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:58:26.198 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:58:26.200 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:58:26.463 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:58:26.463 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:58:26.469 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:58:26.480 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: ，我想克隆我的声音; 响应时间: 0; JD机器人回复: 哇，太酷了！我们先来确认一下，你是想让我克隆你的声音，对吧？如果是的话，我们就可以开始了哦！你准备好了吗？
2025-07-02 10:58:26.480 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:58:26.480 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:58:26.643 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 10:58:29.099 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:58:31.719 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:58:35.951 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:58:37.562 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 10:58:37.562 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:58:41.718 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我准备好了，克隆我的声音, 时间戳: 2025-07-02 10:58:48.090000
2025-07-02 10:58:42.426 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 10:58:48.798000
2025-07-02 10:58:42.426 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 708
2025-07-02 10:58:42.496 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:58:42.500 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:58:42.505 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:58:42.813 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:58:42.823 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:58:43.094 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:58:43.094 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:58:43.105 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:58:43.116 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: ，我准备好了，克隆我的声音; 响应时间: 0; JD机器人回复: 太棒了！现在请你跟着我读这句话：“今天天气真好我们一起去郊游吧”。记得要一字不落哦！
2025-07-02 10:58:43.116 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:58:43.116 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:58:45.931 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:58:46.197 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 10:58:49.455 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:58:51.370 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 10:58:51.370 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:58:57.220 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天天气真好我们一起去郊游吧, 时间戳: 2025-07-02 10:59:03.587000
2025-07-02 10:58:57.793 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 10:59:04.161000
2025-07-02 10:58:57.793 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 574
2025-07-02 10:58:57.896 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:58:57.896 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:58:57.906 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:58:57.913 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:58:57.917 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: 今天天气真好我们一起去郊游吧; 响应时间: 0; JD机器人回复: 这次声音没有变好，要不要再试试？退出也可以哦。
2025-07-02 10:58:57.917 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:58:57.917 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:58:57.917 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:59:02.949 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 10:59:05.193 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，再试试, 时间戳: 2025-07-02 10:59:11.565000
2025-07-02 10:59:05.875 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 10:59:12.247000
2025-07-02 10:59:05.875 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 682
2025-07-02 10:59:05.940 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:59:05.950 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:59:05.952 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:59:06.300 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:59:06.308 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:59:06.591 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:59:06.592 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:59:06.596 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:59:06.607 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: ，再试试; 响应时间: 0; JD机器人回复: 好的，再来一遍吧！请跟着我读：“今天天气真好我们一起去郊游吧”。加油，这次一定可以的！
2025-07-02 10:59:06.607 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:59:06.607 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:59:09.379 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:59:12.807 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:59:14.041 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 10:59:15.226 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 10:59:15.227 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:59:20.138 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，今天天气真好，我们一起去郊游吧, 时间戳: 2025-07-02 10:59:26.508000
2025-07-02 10:59:20.692 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 10:59:27.062000
2025-07-02 10:59:20.692 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 554
2025-07-02 10:59:20.775 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:59:20.775 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:59:20.784 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:59:20.795 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:59:20.795 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: ，今天天气真好，我们一起去郊游吧; 响应时间: 0; JD机器人回复: 嗯，声音没到3秒呢！再试一次，还是退出呀？
2025-07-02 10:59:20.795 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:59:20.795 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:59:20.795 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:59:25.630 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 10:59:25.710 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，退出, 时间戳: 2025-07-02 10:59:32.080000
2025-07-02 10:59:26.875 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 10:59:33.246000
2025-07-02 10:59:26.875 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1166
2025-07-02 10:59:26.941 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:59:27.434 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:59:27.801 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:59:28.083 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:59:28.083 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:59:48.799 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-02 10:59:48.800 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 10:59:48.866 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 10:59:48.866 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 10:59:49.884 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 10:59:49.889 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-02 10:59:51.631 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天, 时间戳: 2025-07-02 10:59:58.003000
2025-07-02 10:59:52.899 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 10:59:59.269000
2025-07-02 10:59:52.900 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1266
2025-07-02 10:59:52.960 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:59:52.962 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:59:52.966 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:59:53.131 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-02 10:59:53.132 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 10:59:53.179 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 10:59:53.180 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:59:53.183 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 10:59:53.183 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 10:59:53.185 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: 今天; 响应时间: 0; JD机器人回复: 今天是2025年7月2日，
2025-07-02 10:59:53.185 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:59:53.185 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 10:59:53.185 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 10:59:53.195 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 10:59:53.195 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 10:59:53.268 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 10:59:53.284 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 10:59:54.475 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 10:59:54.538 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-02 10:59:57.397 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东，我想克隆我的声音, 时间戳: 2025-07-02 11:00:03.765000
2025-07-02 10:59:58.682 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:00:05.053000
2025-07-02 10:59:58.682 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1288
2025-07-02 10:59:58.745 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:59:58.748 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:59:58.758 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 10:59:59.062 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:59:59.063 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:59:59.395 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:59:59.396 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:59:59.672 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 10:59:59.673 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 10:59:59.675 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 10:59:59.685 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: 你好，东东，我想克隆我的声音; 响应时间: 0; JD机器人回复: 太棒了！那我们就开始吧。你是想让我克隆你自己的声音吗？我们先来确认一下哦。是的吗？
2025-07-02 10:59:59.685 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 10:59:59.685 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:00:01.378 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:00:04.602 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:00:06.672 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，是的, 时间戳: 2025-07-02 11:00:13.044000
2025-07-02 11:00:06.820 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:00:07.230 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:00:13.602000
2025-07-02 11:00:07.231 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 558
2025-07-02 11:00:07.239 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 11:00:07.239 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:00:07.239 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 11:00:07.302 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:00:07.305 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:00:07.323 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:00:07.340 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-07-02 11:00:07.785 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:00:07.797 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:00:08.096 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:00:08.096 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:00:08.098 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:00:08.108 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: ，是的; 响应时间: 0; JD机器人回复: 好的，那我们就正式开始啦！请跟我读这句话：“今天天气真好我们一起去郊游吧”。记得要一字不落哦！
2025-07-02 11:00:08.109 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:00:08.109 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:00:10.242 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:00:15.075 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:00:16.989 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:00:16.989 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:00:21.731 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，今天天气真好我们一起去郊游吧, 时间戳: 2025-07-02 11:00:28.103000
2025-07-02 11:00:22.576 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:00:28.947000
2025-07-02 11:00:22.576 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 844
2025-07-02 11:00:22.674 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:00:22.677 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:00:22.687 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:00:23.087 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:00:23.088 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:00:23.091 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:00:23.102 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: ，今天天气真好我们一起去郊游吧; 响应时间: 0; JD机器人回复: 录音时间太短了，还不到3秒哦。你想再试一次，还是退出呢？哎呀，录音少于3秒了，没学会。再来一次还是退出，你选一个吧！
2025-07-02 11:00:23.102 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:00:23.102 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:00:27.662 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 11:00:28.929 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:00:30.165 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 11:00:30.166 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 11:00:35.778 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:00:35.778 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:00:39.358 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，换一句话我们重新录, 时间戳: 2025-07-02 11:00:45.731000
2025-07-02 11:00:40.010 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:00:46.382000
2025-07-02 11:00:40.010 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 651
2025-07-02 11:00:40.087 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:00:40.097 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:00:40.101 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:00:40.555 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:00:40.566 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:00:40.880 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:00:40.881 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:00:40.889 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:00:40.899 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: ，换一句话我们重新录; 响应时间: 0; JD机器人回复: 没问题！我们换一句话试试看。请跟我读这句话：“小猫咪在阳光下睡觉真舒服”。加油哦！
2025-07-02 11:00:40.899 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:00:40.899 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:00:43.226 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:00:47.755 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:00:48.964 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:00:48.964 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:00:53.403 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 小猫咪在阳光下睡觉真舒服, 时间戳: 2025-07-02 11:00:59.775000
2025-07-02 11:00:53.990 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:01:00.360000
2025-07-02 11:00:53.990 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 585
2025-07-02 11:00:54.107 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:00:54.107 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:00:54.111 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:00:54.118 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:00:54.122 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: 小猫咪在阳光下睡觉真舒服; 响应时间: 0; JD机器人回复: 哇，声音魔法没成功！录音少于3秒啦！再试一次，还是退出呢？
2025-07-02 11:00:54.122 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:00:54.122 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:00:54.123 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:01:00.762 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:01:04.089 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，换一句长一点的话, 时间戳: 2025-07-02 11:01:10.461000
2025-07-02 11:01:05.309 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:01:11.680000
2025-07-02 11:01:05.309 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1219
2025-07-02 11:01:05.374 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:01:05.378 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:01:05.389 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:01:05.736 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:01:05.748 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:01:06.055 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:01:06.063 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:01:06.339 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:01:06.343 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:01:06.608 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:01:06.609 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:01:06.645 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:01:06.646 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:01:06.652 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:01:06.663 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: ，换一句长一点的话; 响应时间: 0; JD机器人回复: 今天，2025年7月2日，是一个充满希望和机遇的日子，愿你在这一天里能收获满满的快乐与成就。如果有什么需要帮助或想聊的话题，我随时在这里等你哦！😊
2025-07-02 11:01:06.663 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:01:06.663 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:01:08.312 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:01:11.236 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:01:15.266 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:01:18.693 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:01:19.623 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 11:01:19.624 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 11:01:20.807 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:01:20.807 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:01:22.499 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 11:01:22.499 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 11:01:25.282 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 11:01:25.283 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 11:01:27.905 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-02 11:01:27.905 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 11:01:27.968 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 11:01:27.969 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 11:01:29.258 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 11:01:29.320 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-02 11:01:36.619 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好东东，我想语音克隆，但是你读的句子稍微长一点，需要超过3秒, 时间戳: 2025-07-02 11:01:42.985000
2025-07-02 11:01:37.588 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:01:43.958000
2025-07-02 11:01:37.589 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 973
2025-07-02 11:01:37.654 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:01:37.656 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:01:37.666 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:01:37.941 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:01:37.950 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:01:38.262 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:01:38.263 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:01:38.558 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:01:38.569 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:01:38.886 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:01:38.888 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:01:39.183 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:01:39.194 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:01:39.210 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:01:39.215 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:01:39.220 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:01:39.226 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: ，你好东东，我想语音克隆，但是你读的句子稍微长一点，需要超过3秒; 响应时间: 0; JD机器人回复: 好的，东东！那我们就开始吧。你可以先跟着我读这句话：“今天天气真好，我们一起去公园玩吧，呼吸新鲜空气，享受美好时光。” 记得要慢慢读，尽量让每个字都清晰哦！准备好了吗？开始吧！😊
2025-07-02 11:01:39.226 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:01:39.226 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:01:39.369 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 11:01:40.795 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:01:43.419 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:01:47.449 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:01:50.569 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:01:54.797 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:01:56.174 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，太长了吧, 时间戳: 2025-07-02 11:02:02.545000
2025-07-02 11:01:57.089 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:02:03.459000
2025-07-02 11:01:57.089 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 914
2025-07-02 11:01:57.099 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 11:01:57.099 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 11:01:57.099 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:01:57.114 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:01:57.191 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:01:57.199 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:01:57.200 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-07-02 11:01:57.202 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:01:57.497 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:01:57.505 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:01:57.797 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:01:57.807 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:01:58.080 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:01:58.080 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:01:58.092 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:01:58.104 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: ，太长了吧; 响应时间: 0; JD机器人回复: 没关系，我们可以再试试稍短一点但又能满足3秒左右的句子。你可以试着读这句：“今天阳光明媚，心情也变得特别好。” 来，跟着我一起读吧！😊
2025-07-02 11:01:58.104 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:01:58.104 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:02:03.137 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:02:06.663 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:02:09.179 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:02:10.889 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:02:10.889 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:02:16.155 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，今天阳光明媚，心情也变得更好, 时间戳: 2025-07-02 11:02:22.526000
2025-07-02 11:02:16.790 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:02:23.160000
2025-07-02 11:02:16.790 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 634
2025-07-02 11:02:16.864 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:02:16.867 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:02:16.877 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:02:17.204 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:02:17.209 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:02:17.633 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:02:17.644 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:02:17.978 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:02:17.987 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:02:18.012 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:02:18.013 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:02:18.019 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:02:18.030 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 324ccdf7-56f0-11f0-ad3f-dc4546c07870; requestId: 3c63f7ce-e4c2-4d4b-a57a-58f6580f789a_joyinside; asr: ，今天阳光明媚，心情也变得更好; 响应时间: 0; JD机器人回复: 太棒了！你读得非常好，声音也很清晰。现在，小萝卜没学会你的声音，录音时间不够哦！再试一次，还是退出呢？这次没成功呢，要不要再来一次，还是现在退出都可以。😊
2025-07-02 11:02:18.030 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:02:18.030 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:02:19.397 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:02:21.716 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:02:28.257 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
