2025-07-18 09:11:34.402 - chat_with_robot - chat_with_robot.py - check_network_connect - line 138 - ERROR - 网络检查失败: 'utf-8' codec can't decode byte 0xd3 in position 422: invalid continuation byte
2025-07-18 09:11:34.403 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 84 - INFO - Windows 系统，使用统一音频控制器播放: ./asserts/tts/network_tts.mp3
2025-07-18 09:11:34.981 - chat_with_robot - chat_with_robot.py - <module> - line 640 - INFO - use_action: dont
2025-07-18 09:11:34.981 - chat_with_robot - chat_with_robot.py - <module> - line 641 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-18 09:11:34.981 - chat_with_robot - chat_with_robot.py - init_websocket - line 319 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1752801094981&accessNonce=46be8422-3062-409c-ae8e-f502dd6bc5eb&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=25c9a5c5-6374-11f0-b655-dc4546c07870&requestId=ca35bf83-9d7f-4981-b89a-e92a1f8348cd_joyinside&accessSign=d24453d6269dedb0b6856c416ccc55d7, request_id: ca35bf83-9d7f-4981-b89a-e92a1f8348cd_joyinside
2025-07-18 09:11:34.981 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-18 09:11:34.982 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-18 09:11:35.787 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-18 09:11:35.887 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-07-18 09:11:37.819 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-18 09:11:37.820 - chat_with_robot - voice.py - _setup_audio_stream - line 324 - INFO - 使用音频设备: 1
2025-07-18 09:11:37.820 - chat_with_robot - voice.py - _setup_audio_stream - line 325 - INFO - channels: 4 <class 'int'>
2025-07-18 09:11:37.820 - chat_with_robot - voice.py - _setup_audio_stream - line 326 - INFO - rate: 44100.0 <class 'float'>
2025-07-18 09:11:37.894 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-18 09:11:37.894 - chat_with_robot - voice.py - init_wakeup - line 311 - INFO - 本地流式KWS检测器启动成功
2025-07-18 09:11:38.895 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-18 09:11:38.895 - chat_with_robot - chat_with_robot.py - play_audio - line 525 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-07-18 09:11:38.896 - chat_with_robot - chat_with_robot.py - play_audio - line 527 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-07-18 09:11:38.896 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 84 - INFO - Windows 系统，使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-07-18 09:11:45.551 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-07-18 09:11:45.552 - chat_with_robot - voice.py - stop - line 421 - INFO - 已停止local_streaming检测器
