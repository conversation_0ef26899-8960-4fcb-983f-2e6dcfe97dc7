#!/bin/bash
# 检查是否已存在名为 tatata 的 tmux 会话
if tmux has-session -t tatata 2>/dev/null; then
    echo "tmux 会话 'tatata' 已存在，正在关闭..."
    tmux kill-session -t tatata
fi
pactl set-default-sink alsa_output.usb-C-Media_Electronics_Inc._USB_Audio_Device-00.analog-stereo 
pactl set-default-sink alsa_output.usb-Yealink_CP900_804005G110000152-00.analog-stereo
pactl set-default-sink alsa_output.usb-Yealink_CP900_804005H040000035-00.stereo-fallback
pactl set-default-sink alsa_output.platform-es8388-sound.stereo-fallback

pactl set-sink-volume @DEFAULT_SINK@ 100%



#if tmux has-session -t tracker 2>/dev/null; then
 #   echo "tmux 会话 'tracker' 已存在，正在关闭..."
#    tmux kill-session -t tracker
#fi
# 设置默认音频输出和输入设备

# 设置音量为最大 (100%)
#pactl set-sink-volume @DEFAULT_SINK@ 120%

# 自动连接到已保存的JD WiFi网络
#if [ -x "$(command -v nmcli)" ]; then
#    # 检查当前是否已连接
#    if ! nmcli -t -f DEVICE,STATE device | grep -q "^wlan0:connected$"; then
#        # 确保JD连接优先级较高
#        nmcli connection modify "FM_191273" connection.autoconnect-priority 100 >/dev/null 2>&1
#        # 直接使用已保存的配置连接JD
#        nmcli connection up "FM_191273" >/dev/null 2>&1
#    fi
#fi

# 等待系统完全启动
sleep 10
CURRENT_DIR=$(pwd)

# 启动音频切换脚本
# nohup ~/audio-switch.sh &
#关闭魔法原子自身交互程序
sudo systemctl disable aiui_speech.service
#sudo systemctl enable aiui_speech.service

cd ~/magicdog_atom/scripts/
./sdk_init_aarch64 t
#./sdk_init_aarch64 f 关闭sdk
export PYTHONPATH=$HOME/magicdog_atom/lib:$PYTHONPATH


# 创建新的 tmux 会话，但不立即附加到该会话
tmux new-session -d -s tatata
#tmux new-session -d -s tracker

# 在 tmux 会话中发送命令
tmux send-keys -t tatata "eval \"\$(conda shell.bash hook)\"" C-m
tmux send-keys -t tracker "source ~/miniconda3/etc/profile.d/conda.sh" C-m

tmux send-keys -t tatata "conda activate tatata" C-m
tmux send-keys -t tracker "conda deactivate" C-m

origindir="$CURRENT_DIR"
echo "origindir="$origindir
# 在 tmux 会话中发送命令
tmux send-keys -t tatata "cd $origindir" C-m

tmux send-keys -t tracker "export GST_PLUGIN_PATH=/usr/lib/aarch64-linux-gnu/gstreamer-1.0/:$GST_PLUGIN_PATH" C-m
#魔法原子sdk
tmux send-keys -t tracker "export PYTHONPATH=$HOME/magicdog_atom/lib:$PYTHONPATH" C-m

tmux send-keys -t tatata "rosnode kill qnx2ros" C-m
#tmux send-keys -t tracker "python3 /home/<USER>/lite_cog/track/src/run_tracker.py" C-m

tmux send-keys -t tatata "python chat_with_robot.py --chat_log 0 --sound_th 1000 --kws local_streaming --use_action  magicdog --echo_cancel True" C-m
# --echo_cancel True

echo "tmux 会话 'tatata' 已启动，运行了 chat_jd.py 脚本"
echo "要查看会话，请在终端中运行: tmux attach -t tatata"

