[16:13:00.993] INFO     | audio_action_controller.py:144 - Modbus 功能已禁用（pymodbus 未安装）
[16:13:04.642] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: ./asserts/ding.wav
[16:13:04.643] INFO     | audio_action_controller.py:193 - 音频队列处理线程已启动
[16:13:04.643] INFO     | audio_action_controller.py:230 - 音频已添加到队列: ./asserts/ding.wav
[16:13:04.644] INFO     | audio_action_controller.py:176 - 队列播放音频: ./asserts/ding.wav
[16:13:04.644] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: asserts/tts/dog_ok.mp3
[16:13:04.645] INFO     | audio_action_controller.py:230 - 音频已添加到队列: asserts/tts/dog_ok.mp3
[16:13:04.696] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:13:04.698] INFO     | audio_action_controller.py:78 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[16:13:04.699] INFO     | audio_action_controller.py:245 - 正在加载音频: ./asserts/ding.wav
[16:13:04.700] INFO     | audio_action_controller.py:50 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[16:13:04.700] INFO     | audio_action_controller.py:259 - 音频长度(从文件获取): 0.9675963521003723秒
[16:13:04.700] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:13:04.701] INFO     | audio_action_controller.py:277 - 开始播放音频
[16:13:04.701] INFO     | audio_action_controller.py:282 - 嘴部动作控制线程已启动
[16:13:04.702] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:04.702] INFO     | audio_action_controller.py:285 - 脖子动作控制线程已启动
[16:13:04.702] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:04.823] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:04.854] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:04.944] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:05.005] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:05.065] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:05.156] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:05.187] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:05.307] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:05.308] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:05.428] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:05.458] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:05.549] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:05.609] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:05.711] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:13:05.711] INFO     | audio_action_controller.py:302 - 音频播放完成
[16:13:05.712] INFO     | audio_action_controller.py:176 - 队列播放音频: asserts/tts/dog_ok.mp3
[16:13:05.760] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:05.797] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:13:05.798] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:13:05.798] INFO     | audio_action_controller.py:245 - 正在加载音频: asserts/tts/dog_ok.mp3
[16:13:05.804] INFO     | audio_action_controller.py:50 - 音频音量设置为: 85.0% (针对WAV无损格式优化)
[16:13:05.804] INFO     | audio_action_controller.py:259 - 音频长度(从文件获取): 4.439773082733154秒
[16:13:05.805] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:13:05.805] INFO     | audio_action_controller.py:277 - 开始播放音频
[16:13:05.806] INFO     | audio_action_controller.py:282 - 嘴部动作控制线程已启动
[16:13:05.806] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:05.806] INFO     | audio_action_controller.py:285 - 脖子动作控制线程已启动
[16:13:05.807] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:05.927] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:05.957] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:06.048] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:06.108] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:06.169] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:06.258] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:06.290] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:06.409] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:06.411] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:06.532] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:06.560] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:06.653] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:06.711] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:06.774] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:06.785] INFO     | audio_action_controller.py:207 - 收到音频打断信号
[16:13:06.785] INFO     | audio_action_controller.py:213 - 已停止当前音频播放
[16:13:06.787] INFO     | audio_action_controller.py:221 - 已清空音频播放队列
[16:13:06.788] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: asserts/shenmeshi.wav
[16:13:06.788] INFO     | audio_action_controller.py:230 - 音频已添加到队列: asserts/shenmeshi.wav
[16:13:06.814] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:13:06.814] INFO     | audio_action_controller.py:304 - 音频播放被打断结束
[16:13:06.815] INFO     | audio_action_controller.py:176 - 队列播放音频: asserts/shenmeshi.wav
[16:13:06.863] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:06.894] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:13:06.895] INFO     | audio_action_controller.py:78 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[16:13:06.895] INFO     | audio_action_controller.py:245 - 正在加载音频: asserts/shenmeshi.wav
[16:13:06.895] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.014] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.018] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.055] INFO     | audio_action_controller.py:50 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[16:13:07.056] INFO     | audio_action_controller.py:259 - 音频长度(从文件获取): 0.7745804786682129秒
[16:13:07.056] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:13:07.057] INFO     | audio_action_controller.py:277 - 开始播放音频
[16:13:07.057] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.057] INFO     | audio_action_controller.py:282 - 嘴部动作控制线程已启动
[16:13:07.058] INFO     | audio_action_controller.py:285 - 脖子动作控制线程已启动
[16:13:07.058] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.138] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.164] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.178] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.209] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.259] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.299] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.316] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.361] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.380] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.420] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.466] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.501] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.512] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.541] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.617] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.622] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.662] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.663] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.743] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.768] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.813] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.863] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:13:07.863] INFO     | audio_action_controller.py:302 - 音频播放完成
[16:13:07.864] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.919] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:07.986] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:08.070] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:08.106] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:08.221] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:08.227] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:08.348] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:08.372] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:08.469] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:08.523] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:08.590] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:08.674] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:08.711] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:08.825] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:08.832] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:08.952] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:08.976] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:09.073] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:09.127] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:09.194] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:09.278] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:09.315] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:09.428] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:09.436] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:09.557] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:09.579] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:09.677] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:09.730] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:09.799] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:09.881] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:09.920] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:10.032] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:10.041] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:10.183] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:10.334] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:10.485] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:10.636] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:10.787] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:10.938] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:11.089] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:13:12.599] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:12.601] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:12.601] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:12.685] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:13:12.686] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:13:12.687] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:12.688] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3'.
[16:13:12.919] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:12.920] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:12.920] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:12.997] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:13:12.998] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:13:12.998] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:13.000] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3'.
[16:13:13.284] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:13.285] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:13.285] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:13.355] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:13:13.355] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:13:13.357] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:13.357] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3'.
[16:13:13.774] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:13.775] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:13.775] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:13.857] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:13:13.858] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:13:13.860] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:13.860] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3'.
[16:13:14.088] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:14.089] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:14.089] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:14.165] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:13:14.166] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:13:14.168] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:14.168] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3'.
[16:13:14.380] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:14.380] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:14.380] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:14.436] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:13:14.438] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:13:14.438] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:14.439] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3'.
[16:13:14.686] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:14.686] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:14.686] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:14.745] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:13:14.745] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:13:14.746] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
[16:13:14.746] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3'.
