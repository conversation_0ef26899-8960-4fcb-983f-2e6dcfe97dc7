#!/usr/bin/env python3
"""
统一音频控制器测试脚本
用于测试嘴巴控制的Modbus连接和动作
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.unified_audio_controller import get_audio_controller
from loguru import logger

def test_modbus_connection():
    """测试Modbus连接"""
    print("=== 统一音频控制器 Modbus 连接测试 ===")
    print()
    
    # 获取音频控制器实例
    controller = get_audio_controller()
    
    # 检查Modbus连接状态
    if controller.client:
        print("✓ Modbus客户端已连接")
        logger.info(f"Modbus连接状态: 已连接到 {controller.modbus_ip}:{controller.modbus_port}")
        
        # 测试嘴巴动作
        print("\n正在测试嘴巴动作...")
        try:
            # 张嘴
            controller._modbus_sender([controller.mouth_mode, '1032-30', controller.mouth_speed])
            print("✓ 发送张嘴命令")
            time.sleep(1)
            
            # 闭嘴
            controller._modbus_sender([controller.mouth_mode, controller.mouth_angle_close, controller.mouth_speed])
            print("✓ 发送闭嘴命令")
            
            print("✓ 嘴巴动作测试完成")
            
        except Exception as e:
            print(f"✗ 嘴巴动作测试失败: {e}")
            
        # 测试眼睛控制
        print("\n正在测试眼睛控制...")
        try:
            # 开灯
            controller._eyes_action(1)
            print("✓ 发送开灯命令")
            time.sleep(1)
            
            # 关灯
            controller._eyes_action(0)
            print("✓ 发送关灯命令")
            
            print("✓ 眼睛控制测试完成")
            
        except Exception as e:
            print(f"✗ 眼睛控制测试失败: {e}")
            
        return True
        
    else:
        print("✗ Modbus客户端未连接")
        logger.error(f"无法连接到Modbus服务器: {controller.modbus_ip}:{controller.modbus_port}")
        
        print("\n故障排除建议：")
        print("1. 检查机器人是否已开机")
        print("2. 检查网络连接是否正常")
        print(f"3. 确认机器人IP地址是否为: {controller.modbus_ip}")
        print(f"4. 确认Modbus端口是否为: {controller.modbus_port}")
        print("5. 检查防火墙设置")
        print("6. 尝试ping机器人IP地址")
        
        return False

def test_audio_with_mouth():
    """测试音频播放时的嘴巴动作"""
    print("\n=== 测试音频播放时的嘴巴动作 ===")
    
    controller = get_audio_controller()
    
    # 测试短音频播放
    test_audio = "./asserts/ding.wav"
    if os.path.exists(test_audio):
        print(f"正在播放测试音频: {test_audio}")
        controller.add_audio_file(test_audio)
        
        # 等待播放完成
        controller.wait_for_completion(timeout=10)
        print("✓ 音频播放完成")
    else:
        print(f"✗ 测试音频文件不存在: {test_audio}")

def main():
    print("=== Possessed AI 统一音频控制器测试 ===")
    print()
    
    # 测试Modbus连接
    if test_modbus_connection():
        print("\n✓ Modbus连接测试通过！")
        
        # 测试音频播放
        test_audio_with_mouth()
        
    else:
        print("\n✗ Modbus连接测试失败！")
        print("请先解决连接问题再测试音频播放。")

if __name__ == "__main__":
    main()
