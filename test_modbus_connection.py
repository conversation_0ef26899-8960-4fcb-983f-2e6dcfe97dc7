#!/usr/bin/env python3
"""
Modbus连接测试脚本
用于诊断嘴巴控制的Modbus连接问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from utils.audio_action_controller import test_modbus_connection, modbus_ip, modbus_port
    from util.logger import logger
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

def main():
    print("=== Possessed AI 嘴巴控制 Modbus 连接测试 ===")
    print()
    
    # 测试连接
    if test_modbus_connection():
        print("✓ Modbus连接测试通过！嘴巴控制应该可以正常工作。")
        
        # 测试简单的嘴巴动作
        print("\n正在测试嘴巴动作...")
        try:
            from utils.audio_action_controller import modbusSender, a
            # 张嘴
            modbusSender([a, '1032-30', '1033-100'])
            print("✓ 发送张嘴命令")
            
            import time
            time.sleep(1)
            
            # 闭嘴
            modbusSender([a, '1032-0', '1033-100'])
            print("✓ 发送闭嘴命令")
            
            print("✓ 嘴巴动作测试完成")
            
        except Exception as e:
            print(f"✗ 嘴巴动作测试失败: {e}")
    else:
        print("✗ Modbus连接测试失败！")
        print("\n故障排除建议：")
        print("1. 检查机器人是否已开机")
        print("2. 检查网络连接是否正常")
        print(f"3. 确认机器人IP地址是否为: {modbus_ip}")
        print(f"4. 确认Modbus端口是否为: {modbus_port}")
        print("5. 检查防火墙设置")
        print("6. 尝试ping机器人IP地址")

if __name__ == "__main__":
    main()
