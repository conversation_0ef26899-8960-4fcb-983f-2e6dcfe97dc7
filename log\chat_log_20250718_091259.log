2025-07-18 09:13:01.167 - chat_with_robot - chat_with_robot.py - <module> - line 640 - INFO - use_action: dont
2025-07-18 09:13:01.168 - chat_with_robot - chat_with_robot.py - <module> - line 641 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-18 09:13:01.168 - chat_with_robot - chat_with_robot.py - init_websocket - line 319 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1752801181168&accessNonce=92c6da3e-5cbe-4f5c-afd4-6d6d7c0a6736&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=5928bd4e-6374-11f0-bd23-dc4546c07870&requestId=526913aa-9f2d-49cf-97af-088fd86fc314_joyinside&accessSign=635833a79ec3d72cdf92c12668c075c6, request_id: 526913aa-9f2d-49cf-97af-088fd86fc314_joyinside
2025-07-18 09:13:01.169 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-18 09:13:01.169 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-18 09:13:01.655 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-18 09:13:01.884 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-07-18 09:13:03.702 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-18 09:13:03.703 - chat_with_robot - voice.py - _setup_audio_stream - line 324 - INFO - 使用音频设备: 1
2025-07-18 09:13:03.703 - chat_with_robot - voice.py - _setup_audio_stream - line 325 - INFO - channels: 4 <class 'int'>
2025-07-18 09:13:03.703 - chat_with_robot - voice.py - _setup_audio_stream - line 326 - INFO - rate: 44100.0 <class 'float'>
2025-07-18 09:13:03.773 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-18 09:13:03.775 - chat_with_robot - voice.py - init_wakeup - line 311 - INFO - 本地流式KWS检测器启动成功
2025-07-18 09:13:04.775 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-18 09:13:04.775 - chat_with_robot - chat_with_robot.py - play_audio - line 525 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-07-18 09:13:04.777 - chat_with_robot - chat_with_robot.py - play_audio - line 527 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-07-18 09:13:04.777 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 84 - INFO - Windows 系统，使用统一音频控制器播放: asserts/tts/dog_ok.mp3
2025-07-18 09:13:36.315 - chat_with_robot - voice.py - detect_callback - line 426 - INFO - [wakeup] 检测到唤醒词
2025-07-18 09:13:36.315 - chat_with_robot - voice.py - end_streaming - line 225 - INFO - [end recording]...
2025-07-18 09:13:36.380 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-18 09:13:36.380 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-18 09:13:36.381 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-07-18 09:13:36.381 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-07-18 09:13:36.381 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-07-18 09:13:36.381 - chat_with_robot - voice.py - start_streaming - line 221 - INFO - [start recording]...
2025-07-18 09:13:36.391 - chat_with_robot - voice.py - run - line 484 - INFO - [run] 持续监听状态...
2025-07-18 09:13:37.734 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-18 09:13:37.734 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-18 09:13:40.034 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天天气怎么样？, 时间戳: 2025-07-18 09:13:39.922000
2025-07-18 09:13:41.124 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-18 09:13:40.963000
2025-07-18 09:13:41.124 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1041
2025-07-18 09:13:42.544 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-18 09:13:42.545 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: 5928bd4e-6374-11f0-bd23-dc4546c07870; requestId: 526913aa-9f2d-49cf-97af-088fd86fc314_joyinside; asr: 今天天气怎么样？; 响应时间: 0; JD机器人回复: 
2025-07-18 09:13:42.545 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-18 09:13:42.545 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-18 09:13:42.545 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-18 09:13:47.246 - chat_with_robot - voice.py - detect_callback - line 426 - INFO - [wakeup] 检测到唤醒词
2025-07-18 09:13:47.246 - chat_with_robot - voice.py - end_streaming - line 225 - INFO - [end recording]...
2025-07-18 09:13:47.310 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-18 09:13:47.311 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-18 09:13:47.311 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-07-18 09:13:47.311 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-07-18 09:13:47.312 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-07-18 09:13:47.312 - chat_with_robot - voice.py - start_streaming - line 221 - INFO - [start recording]...
2025-07-18 09:13:47.368 - chat_with_robot - voice.py - run - line 484 - INFO - [run] 持续监听状态...
2025-07-18 09:13:47.667 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-18 09:13:47.555000
2025-07-18 09:13:47.955 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-18 09:13:47.955 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-18 09:13:47.959 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: 5928bd4e-6374-11f0-bd23-dc4546c07870; requestId: 526913aa-9f2d-49cf-97af-088fd86fc314_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-18 09:13:47.959 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-18 09:13:47.959 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-18 09:13:47.959 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-18 09:13:50.799 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天成都天气怎么样？, 时间戳: 2025-07-18 09:13:50.686000
2025-07-18 09:13:52.046 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-18 09:13:51.919000
2025-07-18 09:13:52.046 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1233
2025-07-18 09:13:53.705 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-18 09:13:53.715 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: 5928bd4e-6374-11f0-bd23-dc4546c07870; requestId: 526913aa-9f2d-49cf-97af-088fd86fc314_joyinside; asr: 今天成都天气怎么样？; 响应时间: 0; JD机器人回复: 
2025-07-18 09:13:53.715 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-18 09:13:53.716 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-18 09:13:53.716 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-18 09:13:56.254 - chat_with_robot - voice.py - detect_callback - line 426 - INFO - [wakeup] 检测到唤醒词
2025-07-18 09:13:56.254 - chat_with_robot - voice.py - end_streaming - line 225 - INFO - [end recording]...
2025-07-18 09:13:56.321 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-18 09:13:56.321 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-18 09:13:56.322 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-07-18 09:13:56.322 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/shenmeshi.wav
2025-07-18 09:13:56.322 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-07-18 09:13:56.322 - chat_with_robot - voice.py - start_streaming - line 221 - INFO - [start recording]...
2025-07-18 09:13:56.379 - chat_with_robot - voice.py - run - line 484 - INFO - [run] 持续监听状态...
2025-07-18 09:13:56.858 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-18 09:13:56.746000
2025-07-18 09:13:57.113 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-18 09:13:57.115 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-18 09:13:57.122 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: 5928bd4e-6374-11f0-bd23-dc4546c07870; requestId: 526913aa-9f2d-49cf-97af-088fd86fc314_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-18 09:13:57.122 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-18 09:13:57.122 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-18 09:13:57.122 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-18 09:13:59.383 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 回锅肉怎么做？, 时间戳: 2025-07-18 09:13:59.270000
2025-07-18 09:14:00.512 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-18 09:14:00.370000
2025-07-18 09:14:00.513 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1100
2025-07-18 09:14:00.522 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-18 09:14:00.533 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-18 09:14:00.543 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-18 09:14:00.543 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-18 09:14:00.543 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-18 09:14:00.543 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-18 09:14:00.543 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-18 09:14:01.200 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-18 09:14:01.205 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-18 09:14:01.207 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-18 09:14:01.207 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-18 09:14:01.207 - chat_with_robot - audio_player.py - _play_single_audio - line 173 - INFO - 使用统一音频控制器播放大模型语音
2025-07-18 09:14:01.207 - chat_with_robot - audio_player.py - _play_single_audio - line 176 - INFO - 大模型语音播放完成
2025-07-18 09:14:01.208 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-18 09:14:01.766 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-18 09:14:01.771 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: 5928bd4e-6374-11f0-bd23-dc4546c07870; requestId: 526913aa-9f2d-49cf-97af-088fd86fc314_joyinside; asr: 回锅肉怎么做？; 响应时间: 0; JD机器人回复: 做回锅肉超简单的！先把五花肉煮熟切片～然后锅里放油，滋滋响的时候放蒜和豆瓣酱爆香，再把肉片倒进去炒到卷起来～最后放青蒜和调料，
2025-07-18 09:14:01.771 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-18 09:14:01.771 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-18 09:14:01.771 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-18 09:14:03.044 - chat_with_robot - voice.py - detect_callback - line 426 - INFO - [wakeup] 检测到唤醒词
2025-07-18 09:14:03.044 - chat_with_robot - voice.py - end_streaming - line 225 - INFO - [end recording]...
2025-07-18 09:14:03.111 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-18 09:14:03.111 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-18 09:14:03.111 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-07-18 09:14:03.111 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用统一音频控制器播放唤醒音频: asserts/zaine.wav
2025-07-18 09:14:03.111 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-07-18 09:14:03.111 - chat_with_robot - voice.py - start_streaming - line 221 - INFO - [start recording]...
2025-07-18 09:14:03.168 - chat_with_robot - voice.py - run - line 484 - INFO - [run] 持续监听状态...
2025-07-18 09:14:03.649 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-18 09:14:03.537000
2025-07-18 09:14:04.018 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-18 09:14:04.018 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-18 09:14:04.023 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: 5928bd4e-6374-11f0-bd23-dc4546c07870; requestId: 526913aa-9f2d-49cf-97af-088fd86fc314_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-18 09:14:04.023 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-18 09:14:04.023 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-18 09:14:04.023 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-18 09:14:05.922 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退下吧, 时间戳: 2025-07-18 09:14:05.809000
2025-07-18 09:14:05.925 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-18 09:14:05.925 - chat_with_robot - chat_with_robot.py - play_audio - line 525 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-07-18 09:14:05.925 - chat_with_robot - chat_with_robot.py - play_audio - line 527 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-07-18 09:14:06.938 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-18 09:14:06.812000
2025-07-18 09:14:06.938 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1003
2025-07-18 09:14:07.215 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
