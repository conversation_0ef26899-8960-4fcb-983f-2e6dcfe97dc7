#!/bin/bash

echo "检查recordings文件夹下的WAV文件..."
echo "----------------------------------------"

# 定义输出文件路径
output_file="others/zero_kb_files.txt"

# 找出0KB文件并保存到others文件夹
echo "正在检查0KB文件..."
find recordings -name "*.wav" -size 0 > "$output_file"

# 检查是否发现0KB文件
if [ ! -s "$output_file" ]; then
    echo "没有发现0KB文件"
else
    count=$(wc -l < "$output_file")
    echo "发现 $count 个0KB文件，文件名已保存至 $output_file"
    
    echo ""
    read -p "是否要删除这些0KB文件？ (y/n): " confirm
    if [[ $confirm == [yY] || $confirm == [yY][eE][sS] ]]; then
        echo "正在删除0KB文件..."
        find recordings -name "*.wav" -size 0 -delete
        echo "已删除所有0KB文件"
    else
        echo "操作已取消"
    fi
fi

echo "----------------------------------------"
echo "检查完成"