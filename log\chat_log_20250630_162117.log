2025-06-30 16:21:18.479 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-06-30 16:21:18.479 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-06-30 16:21:18.495 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751271678496&accessNonce=dd78568d-253a-4c7b-9054-8fa87b4ff0b5&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=328634ea-558b-11f0-a347-dc4546c07870&requestId=92ec4d47-0ab8-45a1-a0f2-cfe884aa912a_joyinside&accessSign=e86182a19770720b0ca0d58a9cef72e5, request_id: 92ec4d47-0ab8-45a1-a0f2-cfe884aa912a_joyinside
2025-06-30 16:21:18.496 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-06-30 16:21:18.496 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-06-30 16:21:19.048 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-06-30 16:21:19.186 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-06-30 16:21:20.733 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
