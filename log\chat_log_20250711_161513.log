2025-07-11 16:15:14.483 - chat_with_robot - chat_with_robot.py - <module> - line 640 - INFO - use_action: dont
2025-07-11 16:15:14.483 - chat_with_robot - chat_with_robot.py - <module> - line 641 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-11 16:15:14.483 - chat_with_robot - chat_with_robot.py - init_websocket - line 319 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1752221714484&accessNonce=5b4f4f6d-c04b-4ff3-9f29-aae06fa23842&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=2c19aa1c-5e2f-11f0-98ed-dc4546c07870&requestId=824f9458-f2cc-442d-a276-93bf6c2feeb3_joyinside&accessSign=de82442c1b5f1def8d1f31005014df82, request_id: 824f9458-f2cc-442d-a276-93bf6c2feeb3_joyinside
2025-07-11 16:15:14.485 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-11 16:15:14.485 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-11 16:15:14.858 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-11 16:15:15.026 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-07-11 16:15:16.385 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-11 16:15:16.385 - chat_with_robot - voice.py - _setup_audio_stream - line 324 - INFO - 使用音频设备: 1
2025-07-11 16:15:16.385 - chat_with_robot - voice.py - _setup_audio_stream - line 325 - INFO - channels: 4 <class 'int'>
2025-07-11 16:15:16.385 - chat_with_robot - voice.py - _setup_audio_stream - line 326 - INFO - rate: 44100.0 <class 'float'>
2025-07-11 16:15:16.435 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-11 16:15:16.435 - chat_with_robot - voice.py - init_wakeup - line 311 - INFO - 本地流式KWS检测器启动成功
2025-07-11 16:15:17.436 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-11 16:15:17.437 - chat_with_robot - chat_with_robot.py - play_audio - line 525 - INFO - 使用 audio_action_controller 播放: ./asserts/ding.wav
2025-07-11 16:15:17.438 - chat_with_robot - chat_with_robot.py - play_audio - line 527 - INFO - audio_action_controller 播放完成: ./asserts/ding.wav
2025-07-11 16:15:17.438 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 84 - INFO - Windows 系统，使用 audio_action_controller 播放: asserts/tts/dog_ok.mp3
2025-07-11 16:15:19.522 - chat_with_robot - voice.py - detect_callback - line 426 - INFO - [wakeup] 检测到唤醒词
2025-07-11 16:15:19.522 - chat_with_robot - voice.py - end_streaming - line 225 - INFO - [end recording]...
2025-07-11 16:15:19.581 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-11 16:15:19.581 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-11 16:15:19.582 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-07-11 16:15:19.583 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用 audio_action_controller 播放唤醒音频: asserts/shenmeshi.wav
2025-07-11 16:15:19.583 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-07-11 16:15:19.583 - chat_with_robot - voice.py - start_streaming - line 221 - INFO - [start recording]...
2025-07-11 16:15:19.587 - chat_with_robot - voice.py - run - line 484 - INFO - [run] 持续监听状态...
2025-07-11 16:15:23.544 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想知道成都天气怎么样, 时间戳: 2025-07-11 16:15:23.568000
2025-07-11 16:15:24.838 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-11 16:15:24.845000
2025-07-11 16:15:24.838 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1277
2025-07-11 16:15:24.861 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:15:24.870 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:15:24.876 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:15:24.876 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:15:24.877 - chat_with_robot - audio_player.py - _play_single_audio - line 185 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\temp_tts_audio_f38d3cb8.mp3
2025-07-11 16:15:24.879 - chat_with_robot - audio_player.py - _play_single_audio - line 189 - INFO - 大模型语音播放完成
2025-07-11 16:15:24.879 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:15:25.264 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:15:25.266 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:15:25.276 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:15:25.276 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:15:25.277 - chat_with_robot - audio_player.py - _play_single_audio - line 185 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\temp_tts_audio_63565bd8.mp3
2025-07-11 16:15:25.278 - chat_with_robot - audio_player.py - _play_single_audio - line 189 - INFO - 大模型语音播放完成
2025-07-11 16:15:25.279 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:15:25.572 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:15:25.582 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:15:25.593 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:15:25.593 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:15:25.593 - chat_with_robot - audio_player.py - _play_single_audio - line 185 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\temp_tts_audio_583b7bc8.mp3
2025-07-11 16:15:25.594 - chat_with_robot - audio_player.py - _play_single_audio - line 189 - INFO - 大模型语音播放完成
2025-07-11 16:15:25.595 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:15:25.913 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:15:25.914 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:15:25.923 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:15:25.923 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:15:25.924 - chat_with_robot - audio_player.py - _play_single_audio - line 185 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\temp_tts_audio_b8a317e8.mp3
2025-07-11 16:15:25.925 - chat_with_robot - audio_player.py - _play_single_audio - line 189 - INFO - 大模型语音播放完成
2025-07-11 16:15:25.926 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:15:26.182 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:15:26.184 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-11 16:15:26.193 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:15:26.200 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:15:26.200 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:15:26.201 - chat_with_robot - audio_player.py - _play_single_audio - line 185 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\temp_tts_audio_12791451.mp3
2025-07-11 16:15:26.203 - chat_with_robot - audio_player.py - _play_single_audio - line 189 - INFO - 大模型语音播放完成
2025-07-11 16:15:26.204 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: 2c19aa1c-5e2f-11f0-98ed-dc4546c07870; requestId: 824f9458-f2cc-442d-a276-93bf6c2feeb3_joyinside; asr: 我想知道成都天气怎么样; 响应时间: 0; JD机器人回复: 今天成都的天气是阵雨，最高气温30度，最低气温24度。夜晚多云，体感温度25度，空气质量不错。建议外出时携带雨具，享受凉爽的天气。
2025-07-11 16:15:26.204 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-11 16:15:26.204 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-11 16:15:26.204 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:15:26.204 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-11 16:15:41.602 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:19:26.970 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:19:26.971 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:19:28.270 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:19:28.272 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:20:27.440 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:20:27.441 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:21:20.131 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:21:20.132 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:21:27.319 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:21:27.321 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:21:52.770 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:21:52.771 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:22:21.811 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:22:21.812 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:22:22.528 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-11 16:22:22.546000
2025-07-11 16:22:23.827 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-11 16:22:23.820000
2025-07-11 16:22:23.827 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1274
2025-07-11 16:22:23.836 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:22:23.838 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:22:23.838 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:22:23.838 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:22:23.862 - chat_with_robot - audio_player.py - _play_single_audio - line 185 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\temp_tts_audio_b79f2dc8.mp3
2025-07-11 16:22:23.865 - chat_with_robot - audio_player.py - _play_single_audio - line 189 - INFO - 大模型语音播放完成
2025-07-11 16:22:23.866 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:22:24.152 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:22:24.157 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:22:24.163 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:22:24.163 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:22:24.165 - chat_with_robot - audio_player.py - _play_single_audio - line 185 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\temp_tts_audio_c072d68b.mp3
2025-07-11 16:22:24.166 - chat_with_robot - audio_player.py - _play_single_audio - line 189 - INFO - 大模型语音播放完成
2025-07-11 16:22:24.168 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:22:24.532 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:22:24.539 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:22:24.544 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:22:24.544 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:22:24.545 - chat_with_robot - audio_player.py - _play_single_audio - line 185 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\temp_tts_audio_17d5c1a7.mp3
2025-07-11 16:22:24.547 - chat_with_robot - audio_player.py - _play_single_audio - line 189 - INFO - 大模型语音播放完成
2025-07-11 16:22:24.549 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:22:24.801 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:22:24.802 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-11 16:22:24.809 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:22:24.809 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:22:24.809 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:22:24.812 - chat_with_robot - audio_player.py - _play_single_audio - line 185 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\temp_tts_audio_94d89725.mp3
2025-07-11 16:22:24.815 - chat_with_robot - audio_player.py - _play_single_audio - line 189 - INFO - 大模型语音播放完成
2025-07-11 16:22:24.816 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:22:24.820 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: 2c19aa1c-5e2f-11f0-98ed-dc4546c07870; requestId: 824f9458-f2cc-442d-a276-93bf6c2feeb3_joyinside; asr: 嗯; 响应时间: 0; JD机器人回复: 嗯，是不是在想待会踢球会不会下雨？其实阵雨过后空气特别清新，踢球更舒服！不过记得带把伞，别淋湿了哦～
2025-07-11 16:22:24.820 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-11 16:22:24.820 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-11 16:22:24.820 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-11 16:22:46.036 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:22:46.867 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:22:46.869 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:22:58.517 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:22:58.520 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:22:59.477 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-11 16:22:59.496000
2025-07-11 16:23:01.094 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-11 16:23:01.085000
2025-07-11 16:23:01.094 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1589
2025-07-11 16:23:01.114 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:23:01.124 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:23:01.134 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:23:01.135 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:23:01.137 - chat_with_robot - audio_player.py - _play_single_audio - line 185 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\temp_tts_audio_1aa1cbd4.mp3
2025-07-11 16:23:01.139 - chat_with_robot - audio_player.py - _play_single_audio - line 189 - INFO - 大模型语音播放完成
2025-07-11 16:23:01.140 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:23:01.406 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:23:01.408 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:23:01.408 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:23:01.408 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:23:01.410 - chat_with_robot - audio_player.py - _play_single_audio - line 185 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\temp_tts_audio_987c4889.mp3
2025-07-11 16:23:01.413 - chat_with_robot - audio_player.py - _play_single_audio - line 189 - INFO - 大模型语音播放完成
2025-07-11 16:23:01.414 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:23:01.734 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:23:01.742 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:23:01.752 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:23:01.752 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:23:01.754 - chat_with_robot - audio_player.py - _play_single_audio - line 185 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\temp_tts_audio_ca635faf.mp3
2025-07-11 16:23:01.757 - chat_with_robot - audio_player.py - _play_single_audio - line 189 - INFO - 大模型语音播放完成
2025-07-11 16:23:01.757 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:23:02.043 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:23:02.053 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:23:02.063 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:23:02.063 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:23:02.064 - chat_with_robot - audio_player.py - _play_single_audio - line 185 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\temp_tts_audio_15d49fe2.mp3
2025-07-11 16:23:02.067 - chat_with_robot - audio_player.py - _play_single_audio - line 189 - INFO - 大模型语音播放完成
2025-07-11 16:23:02.068 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:23:02.350 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:23:02.351 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-11 16:23:02.353 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:23:02.361 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:23:02.361 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:23:02.363 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: 2c19aa1c-5e2f-11f0-98ed-dc4546c07870; requestId: 824f9458-f2cc-442d-a276-93bf6c2feeb3_joyinside; asr: 嗯; 响应时间: 0; JD机器人回复: 嗯，看来你在考虑出门踢球的事情呢！阵雨后的天气确实很适合运动，但还是要记得带伞。对了，你对今天的天气有什么特别打算吗？或者，你更喜欢在雨天做些别的事情？
2025-07-11 16:23:02.363 - chat_with_robot - audio_player.py - _play_single_audio - line 185 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\temp_tts_audio_bc5ef9f3.mp3
2025-07-11 16:23:02.364 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-11 16:23:02.364 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-11 16:23:02.364 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-11 16:23:02.366 - chat_with_robot - audio_player.py - _play_single_audio - line 189 - INFO - 大模型语音播放完成
2025-07-11 16:23:02.367 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:23:03.440 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:25:28.442 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:25:28.443 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:26:03.073 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:26:03.074 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:26:07.641 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:26:07.641 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:26:29.482 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:26:29.483 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:26:46.517 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:26:46.518 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:26:59.512 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-07-11 16:26:59.512 - chat_with_robot - voice.py - stop - line 421 - INFO - 已停止local_streaming检测器
