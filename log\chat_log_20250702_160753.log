2025-07-02 16:07:54.328 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-07-02 16:07:54.328 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-02 16:07:54.344 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751443674344&accessNonce=33b4bad0-61e5-4a12-866d-baac0de67a78&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=a809e76a-571b-11f0-9898-dc4546c07870&requestId=de10a12a-d019-4408-870a-ffae9ee37f9e_joyinside&accessSign=88ed958dc9c1f65cb521152ad3cdebcb, request_id: de10a12a-d019-4408-870a-ffae9ee37f9e_joyinside
2025-07-02 16:07:54.345 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-02 16:07:54.345 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-02 16:07:54.708 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-02 16:07:54.997 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-07-02 16:07:56.371 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-02 16:08:16.125 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-07-02 16:08:16.127 - chat_with_robot - voice.py - stop - line 401 - INFO - 已停止local_streaming检测器
