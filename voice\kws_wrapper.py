import os
import time
import threading

import numpy as np
import sherpa_onnx

from util.logger import logger


class KWSStreamDetector:
    """流式关键词唤醒系统Python包装器（基于sherpa_onnx）"""
    
    def __init__(self, 
                 model_path="models"):
        """
        初始化流式KWS检测器
        
        参数:
            model_path: 模型文件所在的路径
        """
        self.model_path = model_path
        
        # 从kws_v3_demo确定模型文件路径
        kws_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                  "kws/kws_v5_demo")
        
        # 初始化sherpa_onnx关键词检测器
        try:
            self.detector = sherpa_onnx.KeywordSpotter(
                tokens=os.path.join(kws_path, "tokens.txt"),
                encoder=os.path.join(kws_path, "encoder-epoch-20-avg-2-chunk-16-left-64.onnx"),
                decoder=os.path.join(kws_path, "decoder-epoch-20-avg-2-chunk-16-left-64.onnx"),
                joiner=os.path.join(kws_path, "joiner-epoch-20-avg-2-chunk-16-left-64.onnx"),
                num_threads=2,
                max_active_paths=2,
                keywords_file=os.path.join(kws_path, "keywords.txt"),
                # keywords_score=0.01,
                # keywords_threshold=0.7,
                num_trailing_blanks=0,
                provider="cpu",
            )
            self.stream = self.detector.create_stream()
            logger.info("sherpa_onnx流式KWS检测器初始化成功")
        except Exception as e:
            logger.error(f"sherpa_onnx流式KWS检测器初始化失败: {e}")
            raise
        
        # 检测结果
        self.detected = False
        self.detection_callback = None
        
        # 运行状态
        self.running = False
        self.processing_thread = None
        
        self.last_wakeup_time = 0
        self.wakeup_cooldown = 0.5  # 唤醒冷却时间（秒）
        
        # 采样率
        self.sample_rate = 16000
    
    def accept_audio(self, audio_data):
        """接收音频数据并进行处理（由主程序音频回调提供）"""
        if not self.running:
            return
            
        try:
            # 将音频数据转换为float32格式
            samples = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0

            samples = samples.reshape(-1)
            # 送入检测器
            self.stream.accept_waveform(self.sample_rate, samples)
        except Exception as e:
            logger.error(f"处理音频数据出错: {e}")
    
    def set_detection_callback(self, callback):
        """设置检测到唤醒词时的回调函数"""
        self.detection_callback = callback
    
    def _detection_thread(self):
        """检测线程"""
        while self.running:
            try:
                # 检查是否有数据可处理
                if self.detector.is_ready(self.stream):
                    # 解码流
                    self.detector.decode_stream(self.stream)
                    # 获取结果
                    result = self.detector.get_result(self.stream)
                    #print(f"result: {result}")
                    # 如果检测到唤醒词
                    if result:
                        if time.time() - self.last_wakeup_time >= self.wakeup_cooldown:
                            self.detection_callback()
                            self.last_wakeup_time = time.time()
                        self.detector.reset_stream(self.stream)
                
                # 短暂休眠，降低CPU使用率
                #time.sleep(0.01)
            except Exception as e:
                logger.error(f"检测线程出错: {e}")
                time.sleep(0.1)  # 错误后稍长休眠
    
    def start(self):
        """启动检测线程"""
        if self.running:
            return
            
        self.running = True
        self.processing_thread = threading.Thread(target=self._detection_thread)
        self.processing_thread.daemon = True
        self.processing_thread.start()
        logger.info("sherpa_onnx流式KWS检测线程已启动")
    
    def stop(self):
        """停止检测线程"""
        self.running = False
        if self.processing_thread:
            self.processing_thread.join(timeout=1.0)
            self.processing_thread = None
            #self.processing_thread.terminate()
        logger.info("sherpa_onnx流式KWS检测线程已停止")
