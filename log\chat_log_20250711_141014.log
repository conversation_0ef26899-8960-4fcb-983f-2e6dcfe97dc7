2025-07-11 14:10:17.955 - chat_with_robot - chat_with_robot.py - <module> - line 640 - INFO - use_action: dont
2025-07-11 14:10:17.955 - chat_with_robot - chat_with_robot.py - <module> - line 641 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-11 14:10:17.972 - chat_with_robot - chat_with_robot.py - init_websocket - line 319 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1752214217971&accessNonce=6451640b-feba-4974-9100-a00376a89021&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=b7d48141-5e1d-11f0-99c8-dc4546c07870&requestId=d3090afd-0d53-4b15-ba8c-f62edbf311c3_joyinside&accessSign=14a68222d7795a008d3c6351d25e3787, request_id: d3090afd-0d53-4b15-ba8c-f62edbf311c3_joyinside
2025-07-11 14:10:17.972 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-11 14:10:17.972 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-11 14:10:18.453 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-11 14:10:18.656 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-07-11 14:10:20.173 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-11 14:10:26.774 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-07-11 14:10:26.774 - chat_with_robot - voice.py - stop - line 421 - INFO - 已停止local_streaming检测器
