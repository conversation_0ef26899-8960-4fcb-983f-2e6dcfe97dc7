[15:50:28.450] INFO     | audio_action_controller.py:136 - Modbus 功能已禁用（pymodbus 未安装）
[15:50:32.336] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[15:50:32.336] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[15:50:32.339] INFO     | audio_action_controller.py:292 - 正在加载音频: ./asserts/ding.wav
[15:50:32.339] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[15:50:32.340] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.9675963521003723秒
[15:50:32.340] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[15:50:32.340] INFO     | audio_action_controller.py:327 - 开始播放音频
[15:50:32.341] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[15:50:32.341] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.342] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[15:50:32.342] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.342] INFO     | audio_action_controller.py:354 - 音频播放已启动（非阻塞模式）
[15:50:32.421] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[15:50:32.423] INFO     | audio_action_controller.py:67 - 检测到MP3文件，已应用针对压缩音频的优化设置
[15:50:32.424] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/tts/dog_ok.mp3
[15:50:32.428] INFO     | audio_action_controller.py:42 - 音频音量设置为: 85.0% (针对WAV无损格式优化)
[15:50:32.429] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 4.439773082733154秒
[15:50:32.430] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[15:50:32.430] INFO     | audio_action_controller.py:327 - 开始播放音频
[15:50:32.431] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[15:50:32.431] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.432] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[15:50:32.432] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.432] INFO     | audio_action_controller.py:354 - 音频播放已启动（非阻塞模式）
[15:50:32.463] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.493] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.553] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.584] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.584] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.645] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.674] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.705] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.735] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.795] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.796] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.826] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.886] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.916] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.947] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:32.947] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.036] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.037] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.068] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.098] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.157] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.188] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.189] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.249] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.278] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.339] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.399] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.400] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.490] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.519] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.640] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.641] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.761] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.792] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.882] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:33.946] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:34.003] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:34.096] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:34.124] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:34.245] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:34.247] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:34.366] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:34.398] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:34.487] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:34.549] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:34.608] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:34.700] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:34.728] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:34.849] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:34.851] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:34.969] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:35.002] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:35.090] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:35.153] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:35.211] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:35.304] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:35.333] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:35.453] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:35.455] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:35.574] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:35.605] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:35.695] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:35.756] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:35.816] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:35.907] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:35.937] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:36.058] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:36.058] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:36.179] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:36.209] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:36.300] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:36.360] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:36.421] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:36.511] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:36.542] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:36.662] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:36.663] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:36.813] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:36.876] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[15:50:36.876] INFO     | audio_action_controller.py:348 - 音频播放完成
[15:50:36.961] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[15:50:36.961] INFO     | audio_action_controller.py:348 - 音频播放完成
[15:50:36.964] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:37.114] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:37.265] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:37.416] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:37.567] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[15:50:37.718] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
