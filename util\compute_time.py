import re
from datetime import datetime


def parse_log_file(log_file_path):
    """解析日志文件中的响应时间"""
    response_times = []
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            for line in f:
                # 使用正则表达式匹配"响应时间: X.XXX"的模式
                time_match = re.search(r'响应时间: (\d+\.\d+)', line)
                if time_match:
                    response_time = float(time_match.group(1))
                    response_times.append(response_time)
    
    except Exception as e:
        print(f"解析文件时出错: {e}")
        return []
    
    return response_times

def print_statistics(response_times):
    """打印统计信息"""
    if not response_times:
        print("未找到响应时间数据")
        return
    
    count = len(response_times)
    avg_time = sum(response_times) / count
    min_time = min(response_times)
    max_time = max(response_times)
    
    print(f"\n=== 响应时间统计 ===")
    print(f"总次数: {count}")
    print(f"平均响应时间: {avg_time:.3f} 秒")
    print(f"最短响应时间: {min_time:.3f} 秒")
    print(f"最长响应时间: {max_time:.3f} 秒")

def main():
    log_file_path = "/home/<USER>/HardwareAIAgent_web/log/chat_log_20250414_150215.log"  # 替换为实际的日志文件路径
    response_times = parse_log_file(log_file_path)
    print_statistics(response_times)

if __name__ == "__main__":
    main()