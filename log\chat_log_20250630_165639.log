2025-06-30 16:56:40.121 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-06-30 16:56:40.121 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-06-30 16:56:40.139 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751273800140&accessNonce=e4df0355-e6c8-43b3-88ac-f4941419dd7d&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=231f65fd-5590-11f0-a7d2-dc4546c07870&requestId=c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside&accessSign=e5ce3f429f0672b51953e7cda34e2a38, request_id: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside
2025-06-30 16:56:40.142 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-06-30 16:56:40.142 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-06-30 16:56:40.460 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-06-30 16:56:40.672 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-06-30 16:56:42.101 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-06-30 16:56:42.102 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-06-30 16:56:42.102 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-06-30 16:56:42.102 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-06-30 16:56:42.160 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-06-30 16:56:42.161 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-06-30 16:56:43.981 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:56:43.981 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:56:44.046 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:56:44.046 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:56:45.070 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:56:45.073 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:56:47.794 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想克隆语音, 时间戳: 2025-06-30 16:56:53.541000
2025-06-30 16:56:48.653 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:56:54.394000
2025-06-30 16:56:48.653 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 853
2025-06-30 16:56:48.726 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:56:48.729 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:56:48.739 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:56:49.059 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:56:49.070 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:56:49.360 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:56:49.360 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:56:49.371 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:56:49.382 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: 我想克隆语音; 响应时间: 0; JD机器人回复: 太好了！那我们就开始吧。你是想让我克隆你、家人还是朋友的声音呢？告诉我，我们就可以开始了哦！
2025-06-30 16:56:49.382 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:56:49.382 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:56:49.808 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 太好了, 时间戳: 2025-06-30 16:56:55.554000
2025-06-30 16:56:50.354 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:56:56.098000
2025-06-30 16:56:50.354 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 544
2025-06-30 16:56:50.364 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:56:50.364 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:56:50.364 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:56:50.404 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:56:50.406 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:56:50.450 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:56:50.464 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-06-30 16:56:50.848 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:56:50.851 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:56:51.150 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:56:51.151 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:56:51.160 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:56:51.171 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: 太好了; 响应时间: 0; JD机器人回复: 那我们就正式开始吧！请跟我读这句话：“今天天气真好我们一起出去玩吧”。记得要一字不落哦！
2025-06-30 16:56:51.171 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:56:51.171 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:56:52.363 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:56:56.993 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:56:58.705 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:56:58.705 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:57:03.317 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，今天天气真好，我们一起出去玩吧, 时间戳: 2025-06-30 16:57:09.064000
2025-06-30 16:57:04.130 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:57:09.873000
2025-06-30 16:57:04.130 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 809
2025-06-30 16:57:04.253 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:57:04.258 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:57:04.259 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:57:04.587 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:57:04.588 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:57:04.592 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:57:04.602 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: ，今天天气真好，我们一起出去玩吧; 响应时间: 0; JD机器人回复: 哎呀，声音时间太短啦！要不要再给我一次机会，还是退出休息下？请稍等一下哦！
2025-06-30 16:57:04.602 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:57:04.602 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:57:10.597 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:57:11.907 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:57:11.907 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:57:14.778 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退出, 时间戳: 2025-06-30 16:57:20.524000
2025-06-30 16:57:15.289 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:57:15.290 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:57:51.919 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:57:51.920 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:57:51.984 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:57:51.984 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:57:53.295 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:57:53.306 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:57:56.043 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你怎么看待台独问题？, 时间戳: 2025-06-30 16:58:01.790000
2025-06-30 16:57:57.259 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:58:03.004000
2025-06-30 16:57:57.259 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1214
2025-06-30 16:57:57.334 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:57:57.336 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:57:57.342 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:57:57.669 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:57:57.680 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:57:58.007 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:57:58.008 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:57:58.339 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:57:58.340 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:57:58.342 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:57:58.353 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: ，你怎么看待台独问题？; 响应时间: 0; JD机器人回复: 台湾自古以来就是中国不可分割的一部分，任何分裂国家的行为都是不被允许的。我们坚信在党的领导下，通过和平发展，最终实现祖国的完全统一。
2025-06-30 16:57:58.353 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:57:58.353 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:57:58.518 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:58:00.961 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:58:02.967 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:58:02.968 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:58:04.181 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:58:05.383 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 但是，它可以打断, 时间戳: 2025-06-30 16:58:11.129000
2025-06-30 16:58:05.699 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:58:05.700 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:58:06.144 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:58:06.144 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:58:06.193 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:58:06.206 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:58:06.206 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:58:06.206 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:58:06.218 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: 但是，它可以打断; 响应时间: 0; JD机器人回复: 
2025-06-30 16:58:06.218 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:58:06.218 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:58:06.218 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:58:06.295 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:58:07.227 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:58:07.289 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:58:10.815 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 好，成都明天天气怎么样？, 时间戳: 2025-06-30 16:58:16.562000
2025-06-30 16:58:11.987 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:58:17.733000
2025-06-30 16:58:11.987 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1171
2025-06-30 16:58:12.056 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:58:12.066 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:58:12.070 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:58:12.358 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:58:12.367 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:58:12.634 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:58:12.635 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:58:12.645 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:58:12.656 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: 好，成都明天天气怎么样？; 响应时间: 0; JD机器人回复: 成都明天的天气是雷阵雨，最高气温33度，最低气温23度。出门记得带伞，注意安全。
2025-06-30 16:58:12.656 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:58:12.656 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:58:14.484 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:58:18.107 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:58:20.723 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:58:20.723 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:58:21.915 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:58:21.916 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:58:25.475 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:58:25.475 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:58:25.538 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:58:25.538 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:58:26.566 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:58:26.628 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:58:27.006 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-06-30 16:58:32.752000
2025-06-30 16:58:27.412 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:58:27.412 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:58:27.416 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-06-30 16:58:27.416 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:58:27.416 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:58:27.416 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:58:29.305 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天是几号呢？, 时间戳: 2025-06-30 16:58:35.050000
2025-06-30 16:58:30.558 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:58:36.303000
2025-06-30 16:58:30.558 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1253
2025-06-30 16:58:30.618 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:58:30.619 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:58:30.619 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:58:30.619 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:58:30.630 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: 今天是几号呢？; 响应时间: 0; JD机器人回复: 今天是2025年6月30日。
2025-06-30 16:58:30.630 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:58:30.630 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:58:30.630 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:58:31.659 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:58:33.237 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:58:39.876 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 那距离一号有多久？, 时间戳: 2025-06-30 16:58:45.623000
2025-06-30 16:58:41.297 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:58:47.041000
2025-06-30 16:58:41.297 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1418
2025-06-30 16:58:41.358 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:58:41.359 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:58:41.368 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:58:41.378 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:58:41.378 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: 那距离一号有多久？; 响应时间: 0; JD机器人回复: 距离2025年7月1日还有1天。
2025-06-30 16:58:41.378 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:58:41.378 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:58:41.378 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:58:42.456 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:58:44.400 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:58:50.356 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 他这种是可以连续对话的，其实做的比我慢, 时间戳: 2025-06-30 16:58:56.099000
2025-06-30 16:58:50.751 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:58:50.751 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:58:50.760 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: 他这种是可以连续对话的，其实做的比我慢; 响应时间: 0; JD机器人回复: 
2025-06-30 16:58:50.760 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:58:50.760 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:58:50.760 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:58:51.215 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:58:53.145 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:58:53.145 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:58:59.501 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:58:59.502 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:59:03.354 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:59:03.354 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:59:03.417 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:59:03.417 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:59:04.722 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:59:04.784 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:59:05.877 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好东东，我想克隆, 时间戳: 2025-06-30 16:59:11.624000
2025-06-30 16:59:06.585 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:59:06.589 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:59:06.589 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:59:06.589 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:59:06.600 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: ，你好东东，我想克隆; 响应时间: 0; JD机器人回复: 
2025-06-30 16:59:06.600 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:59:06.600 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:59:06.600 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:59:06.691 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:59:07.069 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 语音, 时间戳: 2025-06-30 16:59:12.816000
2025-06-30 16:59:07.755 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:59:13.500000
2025-06-30 16:59:07.756 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 684
2025-06-30 16:59:07.822 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:59:07.833 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:59:07.843 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:59:08.142 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:59:08.144 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:59:08.485 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:59:08.486 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:59:08.782 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:59:08.786 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:59:09.196 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:59:09.207 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:59:09.435 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:59:09.435 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:59:09.445 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:59:09.456 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: 语音; 响应时间: 0; JD机器人回复: 太好了！你是想让我克隆你的声音吗？我们可以开始了哦！不过在这之前，我需要你先跟着我读一段话，这样我才能更好地学习你的声音。准备好了吗？请跟我读：“今天阳光真好啊我们一起去公园散步吧”。
2025-06-30 16:59:09.456 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:59:09.456 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:59:11.268 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:59:12.717 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 是, 时间戳: 2025-06-30 16:59:18.463000
2025-06-30 16:59:13.436 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:59:19.181000
2025-06-30 16:59:13.436 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 718
2025-06-30 16:59:13.443 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:59:13.443 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:59:13.443 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:59:13.486 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:59:13.497 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:59:13.507 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:59:13.517 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:59:13.544 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-06-30 16:59:13.865 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:59:13.872 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:59:14.125 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:59:14.125 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:59:14.126 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:59:14.137 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: 是; 响应时间: 0; JD机器人回复: 太棒了！请你跟着我读这句话：“今天阳光真好啊我们一起去公园散步吧”。记得要一字不落哦！
2025-06-30 16:59:14.137 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:59:14.137 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:59:16.538 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:59:17.854 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:59:20.258 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:59:21.871 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:59:21.871 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:59:32.553 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我们一起去公园散步吧, 时间戳: 2025-06-30 16:59:38.299000
2025-06-30 16:59:33.349 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:59:39.094000
2025-06-30 16:59:33.349 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 795
2025-06-30 16:59:33.436 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:59:33.442 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:59:33.453 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:59:33.787 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:59:33.794 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:59:34.190 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:59:34.192 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:59:34.447 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:59:34.447 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:59:34.450 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:59:34.461 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: 我们一起去公园散步吧; 响应时间: 0; JD机器人回复: 你已经读得很棒啦，不过好像漏掉了一小部分哦。我们再来一次好不好？请完整地跟着我读：“今天阳光真好啊我们一起去公园散步吧”。加油！
2025-06-30 16:59:34.461 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:59:34.461 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:59:37.879 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:59:39.791 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:59:45.229 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:59:46.036 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:59:46.036 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:59:51.030 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，那今天阳光真好呀, 时间戳: 2025-06-30 16:59:56.775000
2025-06-30 16:59:51.747 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:59:57.489000
2025-06-30 16:59:51.747 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 714
2025-06-30 16:59:51.812 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:59:51.820 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:59:51.820 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:59:51.823 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:59:51.823 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:59:51.828 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:59:51.831 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: ，那今天阳光真好呀; 响应时间: 0; JD机器人回复: 你读得真好听，不过还差一点点哦。
2025-06-30 16:59:51.831 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:59:51.831 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:59:51.831 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:59:51.924 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:59:53.787 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我们一起去公园散步吧, 时间戳: 2025-06-30 16:59:59.532000
2025-06-30 16:59:54.110 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:59:54.111 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:59:54.120 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: ，我们一起去公园散步吧; 响应时间: 0; JD机器人回复: 
2025-06-30 16:59:54.120 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:59:54.120 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:59:54.121 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:00:01.418 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:00:02.734 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:00:02.735 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:00:04.458 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，他又卡死了, 时间戳: 2025-06-30 17:00:10.203000
2025-06-30 17:00:04.753 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:00:04.758 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:00:04.762 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: ，他又卡死了; 响应时间: 0; JD机器人回复: 
2025-06-30 17:00:04.762 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:00:04.762 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:00:04.762 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:00:06.471 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:00:06.471 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:00:06.536 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:00:06.536 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:00:07.824 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:00:07.886 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:00:09.162 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好东东，克隆语音, 时间戳: 2025-06-30 17:00:14.903000
2025-06-30 17:00:10.124 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:00:15.868000
2025-06-30 17:00:10.124 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 965
2025-06-30 17:00:10.190 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:00:10.195 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:00:10.202 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:00:10.568 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:00:10.576 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:00:10.939 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:00:10.945 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:00:11.255 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:00:11.266 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:00:11.317 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好呀, 时间戳: 2025-06-30 17:00:17.061000
2025-06-30 17:00:11.318 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 17:00:11.318 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:00:11.319 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:00:11.319 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:00:11.330 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: 你好呀; 响应时间: 0; JD机器人回复: 你好呀！你想让我克隆你的声音吗？我们可以一起玩这个有趣的游戏哦！你可以先跟着我读一段话，这样我就能学到你的声音啦。
2025-06-30 17:00:11.330 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:00:11.330 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:00:11.330 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:00:11.410 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:00:11.420 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 17:00:11.967 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:00:17.709000
2025-06-30 17:00:11.967 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 648
2025-06-30 17:00:12.030 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:00:12.034 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:00:12.043 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:00:12.478 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:00:12.481 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:00:12.775 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:00:12.778 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:00:13.140 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:00:13.142 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:00:13.400 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:00:13.401 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:00:13.401 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:00:13.411 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: 你好呀; 响应时间: 0; JD机器人回复: 你好呀！你已经读了一部分啦，不过我们需要完整地读完这句话才能开始哦。请再试一次，完整地读：“今天阳光真好啊我们一起去公园散步吧”。加油，我相信你可以的！
2025-06-30 17:00:13.411 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:00:13.411 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:00:14.867 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:00:18.692 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:00:21.208 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:00:24.937 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:00:27.152 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:00:27.152 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:00:31.232 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:00:34.865 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我们一起去公园散步吧, 时间戳: 2025-06-30 17:00:40.609000
2025-06-30 17:00:35.544 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:00:41.288000
2025-06-30 17:00:35.544 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 679
2025-06-30 17:00:35.609 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:00:35.620 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:00:35.630 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:00:35.917 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:00:35.917 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:00:36.256 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:00:36.259 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:00:36.608 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:00:36.610 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:00:36.612 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:00:36.623 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: ，我们一起去公园散步吧; 响应时间: 0; JD机器人回复: 你已经读得很好了，不过还差一点点哦。我们需要从头开始读完整的句子：“今天阳光真好啊我们一起去公园散步吧”。再来一次吧，我知道你一定可以的！
2025-06-30 17:00:36.623 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:00:36.625 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:00:39.355 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:00:42.172 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:00:43.841 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我感觉, 时间戳: 2025-06-30 17:00:49.587000
2025-06-30 17:00:44.468 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:00:50.213000
2025-06-30 17:00:44.468 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 626
2025-06-30 17:00:44.473 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:00:44.473 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:00:44.473 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:00:44.490 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:00:44.535 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:00:44.538 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:00:44.545 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:00:44.574 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-06-30 17:00:44.669 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 17:00:44.670 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:00:44.670 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:00:44.676 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:00:44.678 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: ，我感觉; 响应时间: 0; JD机器人回复: 你感觉怎么样呢？如果你准备好了，
2025-06-30 17:00:44.679 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:00:44.679 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:00:44.679 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:00:44.748 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:00:44.771 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 17:00:48.473 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我感觉他那个音色，他要普通话很标准, 时间戳: 2025-06-30 17:00:54.219000
2025-06-30 17:00:49.123 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:00:54.867000
2025-06-30 17:00:49.123 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 648
2025-06-30 17:00:49.181 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:00:49.186 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:00:49.191 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:00:49.603 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:00:49.611 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:00:50.050 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:00:50.054 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:00:50.302 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:00:50.302 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:00:50.308 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:00:50.320 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: 我感觉他那个音色，他要普通话很标准; 响应时间: 0; JD机器人回复: 听起来你对音色的要求很高呀！那我们再试一次，让我听到你最标准的普通话吧。请完整地读这句话：“今天阳光真好啊我们一起去公园散步吧”。加油！
2025-06-30 17:00:50.320 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:00:50.320 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:00:51.808 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:00:53.892 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，对今天阳光真好呀, 时间戳: 2025-06-30 17:00:59.636000
2025-06-30 17:00:54.201 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:00:54.204 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:00:55.999 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:00:56.236 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:00:57.531 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:01:01.667 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:01:02.472 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:01:02.472 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:01:02.484 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: ，对今天阳光真好呀; 响应时间: 0; JD机器人回复: 
2025-06-30 17:01:02.484 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:01:02.484 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:01:02.484 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:01:06.912 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，那今天阳光真好呀, 时间戳: 2025-06-30 17:01:12.658000
2025-06-30 17:01:07.262 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:01:07.263 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:01:07.266 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: ，那今天阳光真好呀; 响应时间: 0; JD机器人回复: 
2025-06-30 17:01:07.266 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:01:07.266 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:01:07.266 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:01:09.681 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我们一起去公园散步吧, 时间戳: 2025-06-30 17:01:15.421000
2025-06-30 17:01:10.035 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:01:10.036 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:01:10.044 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: ，我们一起去公园散步吧; 响应时间: 0; JD机器人回复: 
2025-06-30 17:01:10.044 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:01:10.044 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:01:10.044 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:01:12.980 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:01:19.359 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:01:19.359 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:01:28.278 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 。对他，他，他不是这个，这个问题就算你很标准普通话也会，但是我不知道什么原因导致的, 时间戳: 2025-06-30 17:01:34.023000
2025-06-30 17:01:28.635 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:01:28.637 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:01:28.647 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: 。对他，他，他不是这个，这个问题就算你很标准普通话也会，但是我不知道什么原因导致的; 响应时间: 0; JD机器人回复: 
2025-06-30 17:01:28.647 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:01:28.647 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:01:28.647 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:01:30.921 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，这个要问一下他们那边, 时间戳: 2025-06-30 17:01:36.660000
2025-06-30 17:01:31.270 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:01:31.271 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:01:31.279 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: ，这个要问一下他们那边; 响应时间: 0; JD机器人回复: 
2025-06-30 17:01:31.279 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:01:31.279 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:01:31.280 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:01:31.832 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-06-30 17:01:37.577000
2025-06-30 17:01:32.140 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:01:32.143 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:01:32.145 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 
2025-06-30 17:01:32.145 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:01:32.145 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:01:32.146 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:01:33.030 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 看, 时间戳: 2025-06-30 17:01:38.776000
2025-06-30 17:01:33.337 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:01:33.340 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:01:33.345 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: 看; 响应时间: 0; JD机器人回复: 
2025-06-30 17:01:33.345 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:01:33.345 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:01:33.345 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:01:40.353 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:01:40.353 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:01:40.417 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:01:40.418 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:01:41.443 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:01:41.506 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:01:42.069 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-06-30 17:01:47.816000
2025-06-30 17:01:42.466 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:01:42.467 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:01:42.469 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 231f65fd-5590-11f0-a7d2-dc4546c07870; requestId: c51e2ec0-b337-4e9d-9413-910511d5aae0_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-06-30 17:01:42.469 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:01:42.469 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:01:42.469 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:01:42.986 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
