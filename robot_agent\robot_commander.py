"""
@Author: <EMAIL> <EMAIL>
@Create Date: 2025.03.28
@Description: 

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
"""
import socket
import struct
import threading
import time

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from util.logger import logger
from robot_agent.robot_monitor import *
from robot_agent.robot_config import ROBOT_COMMANDS, ROBOT_CONFIG

class RobotCommander:
    def __init__(self):
        # 从配置文件读取机器人配置
        ctl_ip = ROBOT_CONFIG.get("ctl_ip", "*************")
        ctl_port = ROBOT_CONFIG.get("ctl_port", 43893)
        self.local_port = ROBOT_CONFIG.get("local_port", 20001)
        self.heartbeat_interval = ROBOT_CONFIG.get("heartbeat_interval", 0.1)
        # 从配置文件获取机器人命令
        self.robot_commands = ROBOT_COMMANDS
        # 创建socket连接
        self.cmd_sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM, 0)
        self.cmd_sock.bind(('0.0.0.0', self.local_port))
        self.ctrl_addr = (ctl_ip, ctl_port)
        self.robot_monitor = RobotMonitor()
        # 创建心跳线程
        self.heartbeat_thread = None
        self.heartbeat_running = False
        self.command_lock = threading.Lock()
        self.start_heartbeat()
        logger.info(f"初始化机器人控制器，连接到 {ctl_ip}:{ctl_port}")

    def continuous_command(self, code, param1=0, param2=0):
        # 心跳指令使用is_heartbeat=True标记，不打印日志
        if code == 0x21040001:  # 心跳指令代码
            return self.send_simple(code, param1, param2, is_heartbeat=True)
        return self.send_simple(code, param1, param2)

    def send_command(self, code, param1=0, param2=0):
        # 主动发送的命令（不包括心跳）都打印日志
        #logger.info(f"发送命令：Code={hex(code)}, Param1={param1}, Param2={param2}")
        return self.send_simple(code, param1, param2, is_heartbeat=False)

    def send_simple(self, code, param1=0, param2=0, is_heartbeat=False,timeout=1.0):
        try:
            with self.command_lock:  # 使用线程锁保护发送过程
                self.cmd_sock.settimeout(timeout)
                payload = struct.pack('<3i', code, param1, param2)
                bytes_sent = self.cmd_sock.sendto(payload, self.ctrl_addr)
                # if not is_heartbeat:  # 只有非心跳指令才打印
                #     logger.info(f"发送命令：Code={hex(code)}, Param1={param1}, Param2={param2}, 发送字节数: {bytes_sent}")
                return True
        except socket.timeout:
            if not is_heartbeat:
                logger.error(f"发送命令超时：Code={hex(code)}")
            return False
        except Exception as e:
            if not is_heartbeat:  # 只有非自动心跳指令才打印错误
                logger.info(f"发送命令时出错：{e}")
            return False

    def start_heartbeat(self):
        self.heartbeat_running = True
        self.heartbeat_thread = threading.Thread(target=self._heartbeat_task)
        self.heartbeat_thread.daemon = True
        self.heartbeat_thread.start()
        
    def _heartbeat_task(self):
        heartbeat_failures = 0
        while self.heartbeat_running:
            try:
                result = self.continuous_command(0x21040001)
                if not result:
                    heartbeat_failures += 1
                    if heartbeat_failures > 5:
                        logger.info(f"警告: 连续{heartbeat_failures}次心跳发送失败")
                else:
                    heartbeat_failures = 0
                time.sleep(self.heartbeat_interval)  # 每0.1秒执行一次
            except Exception as e:
                logger.error(f"心跳任务出错：{e}")
                heartbeat_failures += 1
                time.sleep(1)  # 出错后等待较长时间再重试

    def stop_continuous_command(self):
        self.heartbeat_running = False
        if self.heartbeat_thread:
            self.heartbeat_thread.join(timeout=1.0)

    def on_closing(self):
        logger.info("关闭机器人控制器...")
        self.stop_continuous_command()
        self.cmd_sock.close()
        self.robot_monitor.monitor_running = False
        logger.info("机器人控制器已关闭")

    def run(self, text):
        try:
            if text not in ROBOT_COMMANDS:
                logger.info(f"错误: 未知命令 '{text}'，可用命令: {list(ROBOT_COMMANDS.keys())}")
                return False
            # logger.info(f"执行命令: '{text}'")  # 执行命令打印log - 开关
            s = time.time()
            result = self.send_command(ROBOT_COMMANDS[text]['code'],
                                       ROBOT_COMMANDS[text]['param1'],
                                       ROBOT_COMMANDS[text]['param2'])
            e = time.time()
            #logger.info(f"执行耗时: '{e-s}'")
            # # TODO:是否有必要？？？
            # if text in ["前进", "后退", "左平移", "右平移", "左转", "右转"]:
            #     # 如果是移动命令，确保发送成功，再次发送相同命令，确保机器人收到
            #     time.sleep(0.05)
            #     self.send_command(ROBOT_COMMANDS[text]['code'],
            #                       ROBOT_COMMANDS[text]['param1'],
            #                       ROBOT_COMMANDS[text]['param2'])
            return result
        except Exception as e:
            logger.error(f"运行命令 '{text}' 时出错：{e}")
            return False

if __name__ == "__main__":
    commander = RobotCommander()
    
    # 测试基本命令
    logger.info("\n测试基本命令...")
    commander.run('心跳')
    time.sleep(1)
    
    # 测试起立/趴下
    logger.info("\n测试起立/趴下...")
    commander.run('起立/趴下')
    time.sleep(3)
    
    # 测试移动命令
    logger.info("\n测试移动命令...")
    commander.run('移动模式')
    time.sleep(1)
    commander.run('前进')
    time.sleep(2)
    commander.run('停止移动')
    
    logger.info("\n测试完成，关闭连接")
    commander.on_closing()
