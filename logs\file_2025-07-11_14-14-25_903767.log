[14:14:25.910] INFO     | audio_action_controller.py:136 - Modbus 功能已禁用（pymodbus 未安装）
[14:14:28.206] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[14:14:28.206] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[14:14:28.206] INFO     | audio_action_controller.py:292 - 正在加载音频: ./asserts/ding.wav
[14:14:28.207] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[14:14:28.207] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.9675963521003723秒
[14:14:28.207] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:14:28.207] INFO     | audio_action_controller.py:327 - 开始播放音频
[14:14:28.207] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[14:14:28.208] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:28.208] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[14:14:28.208] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:28.329] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:28.359] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:28.450] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:28.510] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:28.571] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:28.662] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:28.692] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:28.812] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:28.813] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:28.933] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:28.963] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:29.054] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:29.114] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:29.214] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:14:29.214] INFO     | audio_action_controller.py:348 - 音频播放完成
[14:14:29.265] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:29.266] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[14:14:29.266] INFO     | audio_action_controller.py:67 - 检测到MP3文件，已应用针对压缩音频的优化设置
[14:14:29.266] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/tts/dog_ok.mp3
[14:14:29.272] INFO     | audio_action_controller.py:42 - 音频音量设置为: 85.0% (针对WAV无损格式优化)
[14:14:29.272] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 4.439773082733154秒
[14:14:29.273] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:14:29.273] INFO     | audio_action_controller.py:327 - 开始播放音频
[14:14:29.273] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[14:14:29.273] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:29.274] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[14:14:29.274] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:29.394] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:29.425] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:29.515] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:29.576] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:29.636] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:29.727] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:29.757] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:29.877] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:29.877] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:29.999] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:30.028] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:30.119] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:30.179] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:30.240] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:30.330] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:30.361] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:30.480] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:30.482] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:30.603] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:30.631] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:30.723] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:30.782] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:30.844] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:30.933] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:30.965] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:31.084] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:31.086] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:31.208] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:31.235] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:31.328] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:31.386] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:31.449] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:31.536] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:31.570] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:31.688] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:31.691] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:31.812] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:31.838] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:31.932] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:31.989] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:32.053] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:32.140] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:32.174] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:32.291] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:32.295] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:32.416] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:32.442] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:32.536] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:32.592] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:32.657] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:32.743] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:32.777] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:32.894] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:32.898] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:33.019] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:33.045] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:33.140] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:33.195] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:33.261] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:33.346] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:33.381] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:33.497] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:33.502] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:33.648] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:33.799] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:33.804] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:14:33.804] INFO     | audio_action_controller.py:348 - 音频播放完成
[14:14:33.949] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:34.100] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:34.251] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:34.402] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:14:34.552] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
