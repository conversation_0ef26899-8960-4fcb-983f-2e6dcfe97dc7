"""
@Author: <EMAIL> <EMAIL>
@Create Date: 2025.03.28
@Description: 

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
"""
import socket
import struct
import time
import threading
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from util.logger import logger
from robot_agent.robot_config import ROBOT_COMMANDS, ROBOT_CONFIG, ROBOT_STATES, get_robot_state

class RobotMonitor:
    def __init__(self):
        self.mon_sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        mon_ip = ROBOT_CONFIG.get("mon_ip", "*************")
        mon_port = ROBOT_CONFIG.get("mon_port", 43897)
        server_address = (mon_ip, mon_port)
        self.mon_sock.bind(server_address)
        self.robot_state = None
        self.monitor_running = True
        self.mon_thread = threading.Thread(target=self.run, daemon=True)
        self.mon_thread.start()

    def run(self):
        while self.monitor_running == True:
            buffer, _ = self.mon_sock.recvfrom(1024)
            header_size = struct.calcsize('3i')
            data_size = struct.calcsize('2i18d1I1?1I1i1d1i2?2d')
            total_size = header_size + data_size
            if len(buffer) == total_size:
                code, size, cons_code = struct.unpack('3i', buffer[:header_size])
                if code == 0x0901:
                    data_format = '2i18d1I1?1I1i1d1i2?2d'
                    data = struct.unpack(data_format, buffer[header_size:])
                    basic_state = data[0]
                    gait_state = data[1]
                    motion_state = data[23]
                    self.robot_state = get_robot_state(basic_state, gait_state, motion_state)
                    # logger.info(f"robot_state: {self.robot_state}")
                    # print(f"robot_state: {self.robot_state}")
            time.sleep(0.01)
        self.on_closing()
    
    def on_closing(self):
        logger.info("关闭机器人监控器")
        self.mon_thread.join()
        self.mon_sock.close()

if __name__ == "__main__":
    monitor = RobotMonitor()
    try:
        print("监控机器人状态中，按Ctrl+C停止...")
        # 保持程序运行并等待状态更新
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("停止监控")
        monitor.monitor_running = False
        time.sleep(0.5)
