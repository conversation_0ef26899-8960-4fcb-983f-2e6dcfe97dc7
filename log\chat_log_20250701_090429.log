2025-07-01 09:04:30.674 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-07-01 09:04:30.674 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-01 09:04:30.691 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751331870691&accessNonce=9ff019b1-dedc-4fbe-86e9-c2036ea95b5a&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=57de3ab2-5617-11f0-8901-dc4546c07870&requestId=3d3b6439-eb45-4525-a260-5927f3c05e7b_joyinside&accessSign=1419517de4c7796fa24297bcad4f4a13, request_id: 3d3b6439-eb45-4525-a260-5927f3c05e7b_joyinside
2025-07-01 09:04:30.693 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-01 09:04:30.693 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-01 09:04:31.219 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-01 09:04:31.475 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-07-01 09:04:32.836 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-01 09:04:32.838 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-07-01 09:04:32.838 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-07-01 09:04:32.838 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-07-01 09:04:32.901 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-01 09:04:32.902 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-07-01 09:05:47.321 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 09:05:47.321 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 09:05:47.387 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 09:05:47.387 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 09:05:48.411 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 09:05:48.412 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 09:05:49.683 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:05:49.683 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:05:50.391 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:05:50.391 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:05:54.721 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯，我没, 时间戳: 2025-07-01 09:06:01.030000
2025-07-01 09:05:55.075 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:05:55.076 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:05:55.081 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 57de3ab2-5617-11f0-8901-dc4546c07870; requestId: 3d3b6439-eb45-4525-a260-5927f3c05e7b_joyinside; asr: 嗯，我没; 响应时间: 0; JD机器人回复: 
2025-07-01 09:05:55.081 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:05:55.081 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:05:55.081 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
