2025-07-01 16:11:14.620 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-07-01 16:11:14.620 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-01 16:11:14.635 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751357474636&accessNonce=86899776-025a-4cb8-8824-ac286662a849&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=f502411d-5652-11f0-8acc-dc4546c07870&requestId=8cd217af-22bc-4f84-8982-990de1f14e75_joyinside&accessSign=23f6bc68c242e3d7a2a116f49b9a8360, request_id: 8cd217af-22bc-4f84-8982-990de1f14e75_joyinside
2025-07-01 16:11:14.637 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-01 16:11:14.637 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-01 16:11:14.961 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-01 16:11:14.995 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-07-01 16:11:16.450 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-01 16:11:16.451 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-07-01 16:11:16.451 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-07-01 16:11:16.451 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-07-01 16:11:16.506 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-01 16:11:16.506 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-07-01 16:11:24.686 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 16:11:24.686 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 16:11:24.751 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 16:11:24.751 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 16:11:25.850 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 16:11:25.862 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 16:11:28.179 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 16:11:28.180 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 16:11:30.844 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 告诉我今天天气, 时间戳: 2025-07-01 16:11:36.987000
2025-07-01 16:11:32.241 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 16:11:38.381000
2025-07-01 16:11:32.241 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1394
2025-07-01 16:11:32.351 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:11:32.354 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:11:32.363 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:11:32.657 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:11:32.664 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:11:33.019 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:11:33.030 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:11:33.326 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:11:33.326 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:11:33.639 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:11:33.640 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 16:11:33.651 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:11:33.662 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f502411d-5652-11f0-8acc-dc4546c07870; requestId: 8cd217af-22bc-4f84-8982-990de1f14e75_joyinside; asr: 告诉我今天天气; 响应时间: 0; JD机器人回复: 今天北京有雷阵雨，最高气温31度，最低气温24度，体感温度31度，相对湿度70%，风力3级，能见度18公里。出门记得带伞，注意安全！
2025-07-01 16:11:33.662 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 16:11:33.662 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 16:11:33.959 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 16:11:35.821 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 16:11:35.821 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 16:11:35.883 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 16:11:35.883 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 16:11:35.883 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 16:11:35.887 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 16:11:37.327 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 16:11:37.389 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 16:11:40.694 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 告你好东东，告诉我成都天气, 时间戳: 2025-07-01 16:11:46.836000
2025-07-01 16:11:42.409 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 16:11:48.550000
2025-07-01 16:11:42.409 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1714
2025-07-01 16:11:42.482 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:11:42.484 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:11:42.495 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:11:42.777 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:11:42.778 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:11:43.125 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:11:43.136 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:11:43.388 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:11:43.400 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:11:43.731 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:11:43.739 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:11:44.024 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:11:44.028 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:11:44.371 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:11:44.372 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 16:11:44.372 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:11:44.383 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f502411d-5652-11f0-8acc-dc4546c07870; requestId: 8cd217af-22bc-4f84-8982-990de1f14e75_joyinside; asr: 告你好东东，告诉我成都天气; 响应时间: 0; JD机器人回复: 你好！今天成都的天气是多云转阵雨，最高气温32度，最低气温23度。体感温度34度，相对湿度52%，东北风3级，紫外线很强，能见度2公里，大气压946百帕。建议外出时带上伞，注意防晒。适合外出，但要注意天气变化。
2025-07-01 16:11:44.384 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 16:11:44.384 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 16:11:44.876 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 16:11:46.127 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:11:49.756 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:11:53.687 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:11:56.303 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:12:00.029 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:12:02.952 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:12:05.875 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 16:12:05.875 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 16:12:10.275 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 16:12:10.275 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 16:12:10.340 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 16:12:10.341 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 16:12:11.705 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 16:12:11.769 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 16:12:12.319 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-01 16:12:18.460000
2025-07-01 16:12:12.669 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 16:12:12.670 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 16:12:12.674 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f502411d-5652-11f0-8acc-dc4546c07870; requestId: 8cd217af-22bc-4f84-8982-990de1f14e75_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-01 16:12:12.675 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 16:12:12.675 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 16:12:12.675 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 16:12:14.850 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你如何看待太多问题？, 时间戳: 2025-07-01 16:12:20.992000
2025-07-01 16:12:15.140 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 16:12:15.140 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 16:12:15.144 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f502411d-5652-11f0-8acc-dc4546c07870; requestId: 8cd217af-22bc-4f84-8982-990de1f14e75_joyinside; asr: 你如何看待太多问题？; 响应时间: 0; JD机器人回复: 
2025-07-01 16:12:15.144 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 16:12:15.144 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 16:12:15.144 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 16:12:22.986 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 16:12:22.987 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 16:12:23.050 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 16:12:23.051 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 16:12:24.072 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 16:12:24.134 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 16:12:24.513 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-01 16:12:30.654000
2025-07-01 16:12:24.521 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 16:12:24.523 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 16:12:24.534 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f502411d-5652-11f0-8acc-dc4546c07870; requestId: 8cd217af-22bc-4f84-8982-990de1f14e75_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-01 16:12:24.534 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 16:12:24.534 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 16:12:24.535 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 16:12:26.876 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 16:12:26.887 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 16:12:26.887 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 16:12:26.960 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 16:12:26.960 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 16:12:26.988 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 16:12:27.024 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 16:12:27.024 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 16:12:28.043 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 16:12:28.096 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 16:12:31.046 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好，东东，你如何看待台独问题？, 时间戳: 2025-07-01 16:12:37.186000
2025-07-01 16:12:31.354 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 16:12:31.356 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 16:12:31.367 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f502411d-5652-11f0-8acc-dc4546c07870; requestId: 8cd217af-22bc-4f84-8982-990de1f14e75_joyinside; asr: ，你好，东东，你如何看待台独问题？; 响应时间: 0; JD机器人回复: 
2025-07-01 16:12:31.367 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 16:12:31.367 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 16:12:31.367 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 16:12:48.203 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 16:12:48.203 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 16:12:48.267 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 16:12:48.267 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 16:12:49.285 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 16:12:49.347 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 16:12:49.851 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-01 16:12:55.991000
2025-07-01 16:12:49.858 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 16:12:49.860 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 16:12:49.867 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f502411d-5652-11f0-8acc-dc4546c07870; requestId: 8cd217af-22bc-4f84-8982-990de1f14e75_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-01 16:12:49.867 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 16:12:49.867 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 16:12:49.867 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 16:12:51.372 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 16:12:51.381 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 16:12:51.381 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 16:12:51.482 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 16:12:51.800 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你会什么技能？, 时间戳: 2025-07-01 16:12:57.942000
2025-07-01 16:12:52.105 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 16:12:52.106 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 16:12:52.112 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f502411d-5652-11f0-8acc-dc4546c07870; requestId: 8cd217af-22bc-4f84-8982-990de1f14e75_joyinside; asr: 你会什么技能？; 响应时间: 0; JD机器人回复: 
2025-07-01 16:12:52.112 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 16:12:52.112 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 16:12:52.112 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 16:13:29.894 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 16:13:33.453 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 16:13:33.453 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 16:13:33.519 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 16:13:33.519 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 16:13:34.537 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 16:13:34.590 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 16:13:34.966 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-01 16:13:41.107000
2025-07-01 16:13:34.976 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 16:13:34.978 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 16:13:34.981 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f502411d-5652-11f0-8acc-dc4546c07870; requestId: 8cd217af-22bc-4f84-8982-990de1f14e75_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-01 16:13:34.981 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 16:13:34.981 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 16:13:34.981 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 16:13:38.213 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你会什么技能？, 时间戳: 2025-07-01 16:13:44.353000
2025-07-01 16:13:38.214 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 16:13:38.219 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 16:13:38.219 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 16:13:38.224 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 16:13:38.225 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 16:13:38.226 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f502411d-5652-11f0-8acc-dc4546c07870; requestId: 8cd217af-22bc-4f84-8982-990de1f14e75_joyinside; asr: 你会什么技能？; 响应时间: 0; JD机器人回复: 
2025-07-01 16:13:38.226 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 16:13:38.226 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 16:13:38.226 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 16:13:38.320 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 16:14:12.652 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-07-01 16:14:12.652 - chat_with_robot - voice.py - stop - line 401 - INFO - 已停止local_streaming检测器
