2025-07-01 09:06:11.242 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-07-01 09:06:11.242 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-01 09:06:11.258 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751331971258&accessNonce=216e4c6d-add8-4b13-8b28-46d372958f08&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=93cfa2c9-5617-11f0-80b9-dc4546c07870&requestId=9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside&accessSign=5994bb892d557e257a717a78ceacafbe, request_id: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside
2025-07-01 09:06:11.260 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-01 09:06:11.260 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-01 09:06:11.682 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-01 09:06:11.941 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-07-01 09:06:13.424 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-01 09:06:13.424 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-07-01 09:06:13.424 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-07-01 09:06:13.424 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-07-01 09:06:13.475 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-01 09:06:13.475 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-07-01 09:07:22.496 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 09:07:22.496 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 09:07:22.560 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 09:07:22.560 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 09:07:23.884 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 09:07:23.895 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 09:08:22.397 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:08:22.441 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:08:24.195 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:08:24.197 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:08:27.176 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:08:27.176 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:08:40.262 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:08:40.263 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:08:44.474 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:08:44.474 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:08:53.218 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:08:53.219 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:08:56.703 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:08:56.704 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:10:07.996 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:10:07.997 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:10:16.378 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:10:16.379 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:10:30.670 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-01 09:10:36.978000
2025-07-01 09:10:30.988 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:10:30.989 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:10:30.994 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 09:10:30.994 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:10:30.994 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:10:30.994 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:10:41.936 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-01 09:10:48.245000
2025-07-01 09:10:42.307 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:10:42.307 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:10:42.313 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 09:10:42.313 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:10:42.313 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:10:42.313 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:10:52.857 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:11:06.666 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:11:06.666 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:11:07.502 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:11:07.502 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:12:01.985 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:12:01.985 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:13:37.264 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:13:37.265 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:13:42.660 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:13:42.660 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:01.737 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:01.738 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:33.912 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:33.913 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:35.345 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:35.345 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:36.662 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:36.663 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:38.102 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:38.102 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:39.662 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:39.663 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:41.107 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:41.108 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:42.662 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:42.662 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:44.217 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:44.217 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:45.657 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:45.657 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:47.097 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:47.098 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:48.661 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:48.661 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:50.099 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:50.100 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:51.540 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:51.541 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:52.983 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:52.983 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:54.540 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:54.541 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:55.993 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:55.993 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:57.420 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:57.420 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:14:58.747 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:14:58.747 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:00.175 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:00.176 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:01.625 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:01.626 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:03.057 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:03.057 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:04.501 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:04.501 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:05.937 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:05.937 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:07.498 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:07.499 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:09.061 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:09.062 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:10.621 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:10.622 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:12.062 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:12.062 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:13.619 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:13.620 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:15.059 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:15.059 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:16.619 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:16.620 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:18.183 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:18.183 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:19.618 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:19.619 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:21.067 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:21.067 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:22.505 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:22.505 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:23.941 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:23.941 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:25.383 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:25.383 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:26.824 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:26.825 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:28.257 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:28.257 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:29.699 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:29.700 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:31.137 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:31.137 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:32.582 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:32.583 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:34.022 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:34.022 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:35.581 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:35.582 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:37.018 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:37.019 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:38.460 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:38.461 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:40.017 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:40.019 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:41.457 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:41.458 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:43.022 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:43.023 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:44.698 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:44.699 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:46.136 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:46.136 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:47.579 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:47.579 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:49.019 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:49.019 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:50.458 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:50.458 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:51.779 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:51.779 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:53.335 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:53.336 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:54.779 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:54.781 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:56.461 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:56.462 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:57.896 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:57.896 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:15:59.340 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:15:59.341 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:00.896 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:00.897 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:02.457 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:02.457 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:04.020 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:04.020 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:05.455 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:05.456 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:06.779 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:06.779 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:08.336 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:08.336 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:10.138 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:10.138 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:11.582 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:11.582 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:13.017 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:13.017 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:14.460 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:14.460 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:16.016 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:16.017 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:17.473 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:17.473 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:18.781 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:18.781 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:20.337 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:20.337 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:21.779 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:21.780 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:23.217 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:23.219 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:24.777 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:24.777 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:26.336 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:26.337 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:27.779 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:27.779 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:29.220 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:29.221 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:30.659 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:30.660 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:32.101 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:32.102 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:33.659 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:33.659 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:35.107 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:35.108 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:36.538 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:36.540 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:37.976 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:37.976 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:39.420 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:39.421 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:41.102 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:41.103 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:42.666 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:42.666 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:44.218 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:44.219 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:45.660 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:45.660 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:47.101 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:47.102 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:48.666 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:48.667 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:50.216 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:50.217 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:51.661 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:51.662 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:53.215 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:53.217 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:54.781 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:54.782 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:56.217 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:56.218 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:57.781 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:57.782 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:16:59.459 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:16:59.460 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:00.901 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:00.902 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:02.344 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:02.345 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:03.903 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:03.904 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:05.337 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:05.337 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:06.783 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:06.784 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:08.338 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:08.338 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:09.779 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:09.780 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:11.218 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:11.219 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:12.541 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:12.541 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:14.098 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:14.099 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:15.424 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:15.424 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:16.979 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:16.979 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:18.660 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:18.660 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:20.217 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:20.217 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:21.782 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:21.783 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:23.338 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:23.339 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:24.776 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:24.778 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:26.216 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:26.218 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:27.781 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:27.782 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:29.217 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:29.218 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:30.780 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:30.781 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:32.339 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:32.339 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:33.778 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:33.779 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:35.337 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:35.337 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:36.902 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:36.903 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:38.458 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:38.458 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:39.900 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:39.901 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:41.339 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:41.340 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:42.900 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:42.901 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:44.341 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:44.341 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:45.900 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:45.901 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:47.580 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:47.581 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:49.019 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:49.020 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:50.456 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:50.457 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:51.905 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:51.906 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:53.373 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:53.373 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:54.663 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:54.663 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:56.225 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:56.225 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:17:58.031 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:17:58.031 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:18:16.013 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:18:16.015 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:18:44.587 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:18:44.588 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:18:45.171 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:18:45.171 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:19:03.538 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:19:03.538 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:19:32.702 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:19:32.702 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:21:47.111 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:21:47.111 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:24:59.241 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-01 09:25:05.546000
2025-07-01 09:24:59.646 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:24:59.646 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:24:59.647 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 09:24:59.647 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:24:59.647 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:24:59.647 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:25:37.132 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:25:58.025 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:25:58.025 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:27:49.763 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，怎么处理？, 时间戳: 2025-07-01 09:27:56.068000
2025-07-01 09:27:50.052 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:27:50.055 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:27:50.056 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，怎么处理？; 响应时间: 0; JD机器人回复: 
2025-07-01 09:27:50.056 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:27:50.056 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:27:50.056 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:27:53.952 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 09:27:53.953 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 09:27:54.017 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 09:27:54.018 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 09:27:55.299 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 09:27:55.361 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 09:27:55.704 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-01 09:28:02.000000
2025-07-01 09:27:56.046 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:27:56.046 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:27:56.054 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-01 09:27:56.054 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:27:56.054 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:27:56.054 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:28:05.858 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:28:07.973 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-01 09:28:14.278000
2025-07-01 09:28:08.222 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:28:08.223 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:28:08.229 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 09:28:08.229 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:28:08.229 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:28:08.229 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:28:08.852 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-01 09:28:15.156000
2025-07-01 09:28:09.103 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:28:09.108 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:28:09.112 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 09:28:09.112 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:28:09.112 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:28:09.112 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:29:13.301 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，中国音, 时间戳: 2025-07-01 09:29:19.603000
2025-07-01 09:29:13.675 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:29:13.675 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:29:13.677 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，中国音; 响应时间: 0; JD机器人回复: 
2025-07-01 09:29:13.677 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:29:13.677 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:29:13.677 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:29:23.080 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我说是减20，我最多的是, 时间戳: 2025-07-01 09:29:29.382000
2025-07-01 09:29:23.395 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:29:23.397 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:29:23.406 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 我说是减20，我最多的是; 响应时间: 0; JD机器人回复: 
2025-07-01 09:29:23.406 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:29:23.407 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:29:23.407 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:29:26.071 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 对我，最多时候18充50, 时间戳: 2025-07-01 09:29:32.375000
2025-07-01 09:29:26.327 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:29:26.330 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:29:26.335 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 对我，最多时候18充50; 响应时间: 0; JD机器人回复: 
2025-07-01 09:29:26.335 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:29:26.335 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:29:26.335 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:29:33.220 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，如果你开个他的那个什么，他的那个数字钱包, 时间戳: 2025-07-01 09:29:39.524000
2025-07-01 09:29:33.502 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:29:33.502 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:29:33.505 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，如果你开个他的那个什么，他的那个数字钱包; 响应时间: 0; JD机器人回复: 
2025-07-01 09:29:33.505 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:29:33.505 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:29:33.505 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:29:37.898 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，中国银行的数字钱包，它有时候还能叠加，就是20, 时间戳: 2025-07-01 09:29:44.202000
2025-07-01 09:29:38.196 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:29:38.201 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:29:38.212 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，中国银行的数字钱包，它有时候还能叠加，就是20; 响应时间: 0; JD机器人回复: 
2025-07-01 09:29:38.212 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:29:38.212 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:29:38.213 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:29:43.176 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 以前我姐，我我是受邀用户嘛，我, 时间戳: 2025-07-01 09:29:49.472000
2025-07-01 09:29:43.481 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:29:43.483 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:29:43.492 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 以前我姐，我我是受邀用户嘛，我; 响应时间: 0; JD机器人回复: 
2025-07-01 09:29:43.492 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:29:43.492 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:29:43.492 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:29:47.292 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 对, 时间戳: 2025-07-01 09:29:53.595000
2025-07-01 09:29:47.577 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:29:47.577 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:29:47.588 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 对; 响应时间: 0; JD机器人回复: 
2025-07-01 09:29:47.588 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:29:47.588 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:29:47.588 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:29:51.029 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我最近都只有几块钱, 时间戳: 2025-07-01 09:29:57.332000
2025-07-01 09:29:51.309 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:29:51.309 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:29:51.311 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 我最近都只有几块钱; 响应时间: 0; JD机器人回复: 
2025-07-01 09:29:51.311 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:29:51.311 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:29:51.311 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:32:47.793 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你是不是有同一个？, 时间戳: 2025-07-01 09:32:54.094000
2025-07-01 09:32:48.141 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:32:48.142 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:32:48.144 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，你是不是有同一个？; 响应时间: 0; JD机器人回复: 
2025-07-01 09:32:48.144 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:32:48.144 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:32:48.144 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:32:59.627 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 09:32:59.627 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 09:32:59.691 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 09:32:59.691 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 09:33:00.715 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 09:33:00.780 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 09:33:07.443 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 09:33:07.443 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 09:33:07.507 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 09:33:07.507 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 09:33:08.777 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 09:33:08.839 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 09:34:13.405 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 09:34:13.406 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 09:34:13.471 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 09:34:13.472 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 09:34:14.747 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 09:34:14.809 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 09:34:19.555 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 09:34:19.555 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 09:34:19.620 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 09:34:19.621 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 09:34:20.895 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 09:34:20.956 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 09:34:24.141 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 09:34:24.141 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 09:34:24.207 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 09:34:24.208 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 09:34:25.242 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 09:34:25.306 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 09:35:15.253 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退一下, 时间戳: 2025-07-01 09:35:21.555000
2025-07-01 09:35:15.864 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 09:35:22.162000
2025-07-01 09:35:15.864 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 607
2025-07-01 09:35:15.953 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 09:35:15.953 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:35:15.955 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 09:35:15.965 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 09:35:15.966 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 退一下; 响应时间: 0; JD机器人回复: 车辆信息获取失败了，请重新再试试吧
2025-07-01 09:35:15.966 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:35:15.966 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:35:15.966 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:35:19.591 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 09:35:23.878 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 拜拜, 时间戳: 2025-07-01 09:35:30.178000
2025-07-01 09:35:24.172 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:35:24.172 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:51:46.525 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 09:51:46.525 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 09:51:46.591 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 09:51:46.591 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 09:51:47.619 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 09:51:47.624 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 09:51:48.288 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:54:37.496 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:54:37.496 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:55:24.777 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-07-01 09:55:31.074000
2025-07-01 09:55:25.513 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 09:55:31.807000
2025-07-01 09:55:25.513 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 733
2025-07-01 09:55:25.586 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 09:55:25.587 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:55:25.589 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 09:55:25.597 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 09:55:25.600 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 车辆信息获取失败了，请重新再试试吧
2025-07-01 09:55:25.600 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:55:25.600 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:55:25.600 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:55:25.609 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-01 09:55:31.905000
2025-07-01 09:55:26.167 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 09:55:26.168 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 09:55:26.170 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 09:55:26.170 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 09:55:26.170 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 09:55:26.170 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 09:55:29.325 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 09:58:40.372 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:58:49.723 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:58:49.723 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:58:59.199 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:58:59.199 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 09:59:05.075 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 09:59:05.076 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:01:43.596 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:01:43.598 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:04:38.214 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-01 10:04:44.506000
2025-07-01 10:04:38.220 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 10:04:38.221 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:04:38.235 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 10:04:38.235 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:04:38.235 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:04:38.235 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:04:59.596 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:05:11.071 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:09:28.956 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:10:43.515 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:10:55.351 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:11:35.805 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 10:11:35.805 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 10:11:35.870 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 10:11:35.870 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 10:11:36.936 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 10:11:36.995 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 10:13:08.442 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 10:13:08.442 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 10:13:08.504 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 10:13:08.504 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 10:13:09.878 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 10:13:09.937 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 10:14:55.048 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 10:14:55.058 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 10:14:55.058 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 10:14:55.159 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 10:14:56.543 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:14:56.543 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:14:58.429 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 相当于读的就, 时间戳: 2025-07-01 10:15:04.720000
2025-07-01 10:14:58.678 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 10:14:58.681 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:14:58.690 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 相当于读的就; 响应时间: 0; JD机器人回复: 
2025-07-01 10:14:58.690 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:14:58.690 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:14:58.690 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:15:01.288 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:15:04.792 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:15:04.794 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:15:05.363 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:15:05.365 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:15:14.308 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 电视看的迷迷糊糊, 时间戳: 2025-07-01 10:15:20.597000
2025-07-01 10:15:14.567 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 10:15:14.568 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:15:14.575 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 电视看的迷迷糊糊; 响应时间: 0; JD机器人回复: 
2025-07-01 10:15:14.575 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:15:14.575 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:15:14.575 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:15:28.175 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:15:30.323 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:15:30.324 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:22:31.539 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:22:31.540 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:22:34.783 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:22:34.784 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:22:36.813 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:22:36.814 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:23:46.724 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 10:23:46.724 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 10:23:46.787 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 10:23:46.787 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 10:23:47.835 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 10:23:47.895 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 10:28:21.019 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-07-01 10:28:27.306000
2025-07-01 10:28:21.317 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 10:28:21.317 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:28:21.324 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 10:28:21.324 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:28:21.324 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:28:21.324 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:31:08.178 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:33:10.829 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:33:10.830 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:37:41.898 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-01 10:37:48.181000
2025-07-01 10:37:41.904 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 10:37:41.905 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:37:41.916 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 10:37:41.916 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:37:41.916 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:37:41.916 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:37:44.641 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 10:37:44.647 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 10:37:44.647 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 10:37:44.748 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 10:37:45.252 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，不影响, 时间戳: 2025-07-01 10:37:51.535000
2025-07-01 10:37:45.867 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 10:37:52.149000
2025-07-01 10:37:45.867 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 614
2025-07-01 10:37:45.946 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 10:37:45.946 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:37:45.949 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 10:37:45.954 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 10:37:45.959 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，不影响; 响应时间: 0; JD机器人回复: 车辆信息获取失败了，请重新再试试吧
2025-07-01 10:37:45.959 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:37:45.959 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:37:45.959 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:37:49.340 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，等一会儿, 时间戳: 2025-07-01 10:37:55.623000
2025-07-01 10:37:49.581 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 10:37:49.906 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 10:37:56.188000
2025-07-01 10:37:49.906 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 565
2025-07-01 10:37:49.976 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 10:37:49.977 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:37:49.986 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 10:37:49.990 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 10:37:49.997 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，等一会儿; 响应时间: 0; JD机器人回复: 车辆信息获取失败了，请重新再试试吧
2025-07-01 10:37:49.997 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:37:49.997 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:37:49.997 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:37:53.614 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 10:38:32.648 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:39:02.540 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:39:02.540 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:46:05.696 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:46:05.696 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:46:08.835 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:46:08.835 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:46:09.494 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:46:09.494 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:46:20.653 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:46:20.653 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:49:44.064 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-07-01 10:49:50.343000
2025-07-01 10:49:44.377 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 10:49:44.378 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:49:44.382 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 10:49:44.382 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:49:44.382 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:49:44.382 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:51:55.214 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:52:15.602 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:52:15.602 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:52:46.408 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，哎，你们那个做的那个版本是啥呀？, 时间戳: 2025-07-01 10:52:52.687000
2025-07-01 10:52:46.729 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 10:52:46.731 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:52:46.733 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，哎，你们那个做的那个版本是啥呀？; 响应时间: 0; JD机器人回复: 
2025-07-01 10:52:46.733 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:52:46.733 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:52:46.733 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:52:50.898 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:52:54.427 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你们那个做的版本是啥？, 时间戳: 2025-07-01 10:53:00.701000
2025-07-01 10:52:54.686 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 10:52:54.692 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:52:54.696 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 你们那个做的版本是啥？; 响应时间: 0; JD机器人回复: 
2025-07-01 10:52:54.696 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:52:54.696 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:52:54.697 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:52:56.777 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:52:58.698 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:52:58.699 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:53:00.371 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:53:00.371 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:53:01.443 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:53:01.444 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:53:03.378 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:53:03.378 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:53:05.338 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 看v3吧, 时间戳: 2025-07-01 10:53:11.617000
2025-07-01 10:53:06.234 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 10:53:12.510000
2025-07-01 10:53:06.235 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 893
2025-07-01 10:53:06.278 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 10:53:06.281 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 10:53:06.285 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 10:53:06.557 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 10:53:06.558 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:53:06.558 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 10:53:06.569 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 看v3吧; 响应时间: 0; JD机器人回复: 车辆信息获取失败了，请重新再试试吧
2025-07-01 10:53:06.569 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:53:06.569 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:53:08.400 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 10:53:09.060 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我们拍摄好像那个, 时间戳: 2025-07-01 10:53:15.338000
2025-07-01 10:53:09.363 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 10:53:09.366 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:53:10.012 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 10:53:10.012 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:53:10.023 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，我们拍摄好像那个; 响应时间: 0; JD机器人回复: 
2025-07-01 10:53:10.023 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:53:10.023 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:53:10.023 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:53:10.708 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:53:12.971 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:53:12.972 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:53:13.813 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:53:13.814 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:53:18.149 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我在看, 时间戳: 2025-07-01 10:53:24.428000
2025-07-01 10:53:18.412 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 10:53:18.413 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:53:18.422 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 我在看; 响应时间: 0; JD机器人回复: 
2025-07-01 10:53:18.422 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:53:18.422 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:53:18.422 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:53:21.824 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 他啊因为他我我再去看一眼, 时间戳: 2025-07-01 10:53:28.103000
2025-07-01 10:53:22.838 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 10:53:22.839 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:53:22.841 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 他啊因为他我我再去看一眼; 响应时间: 0; JD机器人回复: 
2025-07-01 10:53:22.841 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:53:22.841 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:53:22.841 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:53:24.609 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 10:53:27.410 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，看他有支持的, 时间戳: 2025-07-01 10:53:33.688000
2025-07-01 10:53:27.774 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 10:53:27.774 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:53:27.777 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，看他有支持的; 响应时间: 0; JD机器人回复: 
2025-07-01 10:53:27.777 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:53:27.777 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:53:27.777 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:53:28.582 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-07-01 10:53:34.860000
2025-07-01 10:53:28.835 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 10:53:28.836 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:53:28.842 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 10:53:28.842 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:53:28.842 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:53:28.842 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:53:34.965 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，应该有版本, 时间戳: 2025-07-01 10:53:41.244000
2025-07-01 10:53:35.226 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 10:53:35.228 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:53:35.232 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，应该有版本; 响应时间: 0; JD机器人回复: 
2025-07-01 10:53:35.232 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:53:35.233 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:53:35.233 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:53:42.288 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你先看吧，到时候不行你应该哦，2.1、 3.0以上版本, 时间戳: 2025-07-01 10:53:48.566000
2025-07-01 10:53:42.613 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 10:53:42.614 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:53:42.615 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，你先看吧，到时候不行你应该哦，2.1、 3.0以上版本; 响应时间: 0; JD机器人回复: 
2025-07-01 10:53:42.615 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:53:42.615 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:53:42.615 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:53:43.827 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-01 10:53:50.106000
2025-07-01 10:53:43.833 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 10:53:43.835 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 10:53:43.849 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 10:53:43.849 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 10:53:43.849 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 10:53:43.849 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 10:54:26.055 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:55:40.221 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:56:46.467 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-01 10:56:52.737000
2025-07-01 10:57:21.562 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 10:57:21.562 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 10:57:21.624 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 10:57:21.624 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 10:57:22.733 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 10:57:22.793 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 10:59:49.433 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:59:55.914 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 10:59:56.041 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 10:59:56.041 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 10:59:56.104 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 10:59:56.105 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 10:59:57.201 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 10:59:57.262 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 10:59:58.756 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:00:01.518 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:00:03.917 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:00:48.920 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:02:12.272 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 11:02:12.274 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 11:02:12.332 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 11:02:12.333 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 11:02:13.797 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 11:02:13.857 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 11:08:07.390 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:08:14.455 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:08:18.170 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:08:20.697 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:08:21.779 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:08:32.867 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:08:40.683 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 11:08:40.683 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 11:08:40.748 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 11:08:40.748 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 11:08:41.760 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 11:08:41.813 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 11:08:48.120 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 11:08:48.120 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 11:08:48.184 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 11:08:48.184 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 11:08:49.194 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 11:08:49.248 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 11:09:59.328 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:10:42.171 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:10:46.674 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 11:10:46.675 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 11:10:46.739 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 11:10:46.739 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 11:10:48.059 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 11:10:48.120 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 11:19:15.239 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:21:29.029 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:21:59.505 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:23:55.337 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:24:01.817 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:24:19.202 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:25:28.224 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:25:29.281 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:25:30.122 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-01 11:25:36.390000
2025-07-01 11:26:36.125 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-01 11:26:42.393000
2025-07-01 11:28:56.345 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 11:28:56.345 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 11:28:56.410 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 11:28:56.410 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 11:28:57.813 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 11:28:57.873 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 11:29:12.266 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:29:31.262 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:30:02.787 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:30:31.355 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:31:12.272 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:31:26.857 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:31:31.588 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:31:38.426 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:37:04.599 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:37:27.029 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:38:21.283 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:39:50.662 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:39:56.664 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:40:49.590 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 11:41:24.388 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-01 11:41:30.652000
2025-07-01 11:44:36.273 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好, 时间戳: 2025-07-01 11:44:42.534000
2025-07-01 11:44:36.273 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 11:44:36.276 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 11:44:36.276 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 11:44:36.377 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 11:44:36.598 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 11:44:36.604 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 11:44:36.606 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 你好; 响应时间: 0; JD机器人回复: 
2025-07-01 11:44:36.606 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 11:44:36.606 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 11:44:36.606 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 11:44:37.059 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 11:44:37.059 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 11:44:37.122 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 11:44:37.122 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 11:44:37.235 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 11:44:38.490 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 11:44:38.550 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 11:47:55.596 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 11:47:55.597 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 11:47:55.658 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 11:47:55.658 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 11:47:56.674 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 11:47:56.728 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 11:48:09.464 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-01 11:48:15.725000
2025-07-01 11:48:09.513 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 11:48:09.513 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 11:48:09.578 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 11:48:09.578 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 11:48:10.025 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 11:48:10.026 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 11:48:10.031 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-01 11:48:10.031 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 11:48:10.031 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 11:48:10.031 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 11:48:10.881 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 11:48:10.943 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 11:48:14.184 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 拜拜, 时间戳: 2025-07-01 11:48:20.446000
2025-07-01 11:48:14.473 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 11:48:14.473 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 12:09:06.929 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 12:09:06.929 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 12:09:06.994 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 12:09:06.994 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 12:09:08.450 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 12:09:08.452 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 12:15:21.896 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 12:15:21.896 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 12:15:21.961 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 12:15:21.961 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 12:15:23.368 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 12:15:23.429 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 12:15:49.295 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 12:15:49.295 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 12:15:49.359 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 12:15:49.359 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 12:15:50.394 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 12:15:50.456 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 12:16:03.841 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 12:16:03.842 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 12:16:03.906 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 12:16:03.906 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 12:16:04.928 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 12:16:04.990 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 12:16:11.657 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 12:16:11.657 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 12:16:11.720 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 12:16:11.720 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 12:16:12.735 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 12:16:12.789 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 12:16:17.175 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 12:16:17.175 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 12:16:17.238 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 12:16:17.239 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 12:16:18.554 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 12:16:18.616 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 12:16:21.142 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 12:16:21.142 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 12:16:21.207 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 12:16:21.207 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 12:16:22.500 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 12:16:22.562 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 12:19:25.417 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:19:58.407 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:19:58.408 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:19:59.969 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:19:59.970 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:20:29.976 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-07-01 12:20:36.221000
2025-07-01 12:20:30.313 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 12:20:30.315 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 12:20:30.319 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 12:20:30.319 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 12:20:30.319 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 12:20:30.319 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 12:20:32.359 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:20:40.417 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:20:40.419 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:21:04.525 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:21:04.526 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:21:30.285 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，哎，他那个版本画的太烦了, 时间戳: 2025-07-01 12:21:36.536000
2025-07-01 12:21:30.569 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 12:21:30.570 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 12:21:30.572 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，哎，他那个版本画的太烦了; 响应时间: 0; JD机器人回复: 
2025-07-01 12:21:30.572 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 12:21:30.572 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 12:21:30.572 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 12:21:34.979 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我也是，只要换一下就弄不了, 时间戳: 2025-07-01 12:21:41.229000
2025-07-01 12:21:35.269 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 12:21:35.269 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 12:21:35.278 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，我也是，只要换一下就弄不了; 响应时间: 0; JD机器人回复: 
2025-07-01 12:21:35.278 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 12:21:35.278 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 12:21:35.278 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 12:21:37.062 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我还不, 时间戳: 2025-07-01 12:21:43.312000
2025-07-01 12:21:37.350 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 12:21:37.352 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 12:21:37.354 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，我还不; 响应时间: 0; JD机器人回复: 
2025-07-01 12:21:37.354 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 12:21:37.354 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 12:21:37.354 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 12:21:37.724 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 12:21:37.724 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 12:21:37.786 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 12:21:37.786 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 12:21:37.953 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 这什么？, 时间戳: 2025-07-01 12:21:44.156000
2025-07-01 12:21:38.259 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 12:21:38.259 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 12:21:38.269 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 这什么？; 响应时间: 0; JD机器人回复: 
2025-07-01 12:21:38.269 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 12:21:38.269 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 12:21:38.269 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 12:21:39.175 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 12:21:39.238 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 12:21:43.977 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:21:45.801 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:21:45.802 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:21:48.530 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 潮汕不需要戴老戴那个, 时间戳: 2025-07-01 12:21:54.780000
2025-07-01 12:21:48.823 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 12:21:48.824 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 12:21:48.835 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 潮汕不需要戴老戴那个; 响应时间: 0; JD机器人回复: 
2025-07-01 12:21:48.835 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 12:21:48.835 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 12:21:48.835 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 12:21:50.038 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:21:57.831 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:21:57.831 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:21:58.666 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:21:58.667 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:22:00.982 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你看看, 时间戳: 2025-07-01 12:22:07.230000
2025-07-01 12:22:01.267 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 12:22:01.268 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 12:22:01.270 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，你看看; 响应时间: 0; JD机器人回复: 
2025-07-01 12:22:01.270 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 12:22:01.270 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 12:22:01.270 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 12:22:02.444 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 这个扔上去可能到, 时间戳: 2025-07-01 12:22:08.690000
2025-07-01 12:22:02.723 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 12:22:02.725 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 12:22:02.734 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 这个扔上去可能到; 响应时间: 0; JD机器人回复: 
2025-07-01 12:22:02.734 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 12:22:02.734 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 12:22:02.734 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 12:22:03.359 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 什么问题？, 时间戳: 2025-07-01 12:22:09.608000
2025-07-01 12:22:03.613 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 12:22:03.619 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 12:22:03.624 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 什么问题？; 响应时间: 0; JD机器人回复: 
2025-07-01 12:22:03.624 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 12:22:03.624 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 12:22:03.625 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 12:22:07.379 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 他自己写的更新到蛮快的, 时间戳: 2025-07-01 12:22:13.629000
2025-07-01 12:22:07.669 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 12:22:07.674 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 12:22:07.676 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 他自己写的更新到蛮快的; 响应时间: 0; JD机器人回复: 
2025-07-01 12:22:07.676 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 12:22:07.676 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 12:22:07.676 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 12:22:15.693 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:22:17.034 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:22:17.034 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:22:19.438 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 那种, 时间戳: 2025-07-01 12:22:25.688000
2025-07-01 12:22:19.755 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 12:22:19.755 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 12:22:19.759 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 那种; 响应时间: 0; JD机器人回复: 
2025-07-01 12:22:19.759 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 12:22:19.759 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 12:22:19.759 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 12:22:21.150 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:22:23.779 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:23:20.157 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:23:20.158 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:24:23.391 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:24:23.392 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:24:41.644 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:24:41.644 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:24:55.308 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:24:55.308 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:25:23.743 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:25:23.745 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:25:50.868 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:25:50.869 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:25:51.821 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:25:51.821 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:26:04.900 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:26:04.902 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:26:21.473 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:26:21.473 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:26:40.790 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:26:40.791 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:26:56.879 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:26:56.879 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:27:08.141 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:27:08.141 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:27:26.022 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:27:26.023 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 12:27:54.905 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 12:27:54.905 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 12:27:54.968 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 12:27:54.968 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 12:27:56.277 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 12:27:56.339 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 12:28:39.426 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-07-01 12:28:45.673000
2025-07-01 12:28:39.432 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 12:28:39.433 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 12:28:39.448 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 12:28:39.448 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 12:28:39.448 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 12:28:39.449 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 12:28:55.983 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-01 12:29:02.231000
2025-07-01 12:33:35.204 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:34:03.285 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 12:44:13.032 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:02:04.159 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:02:06.307 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:02:07.616 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:02:08.942 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:02:10.736 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:02:12.054 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:02:13.619 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:02:15.170 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:02:16.494 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:02:18.176 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:02:19.604 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:02:21.052 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:02:22.487 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:02:24.061 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:02:25.502 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:02:26.932 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:02:28.121 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:06:36.291 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:06:44.939 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:11:51.125 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 13:11:51.125 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 13:11:51.188 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:11:51.188 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:11:52.612 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 13:11:52.676 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 13:12:00.002 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 13:12:00.002 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 13:12:00.066 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:12:00.066 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:12:01.093 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 13:12:01.155 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 13:12:44.654 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-01 13:12:50.884000
2025-07-01 13:15:15.982 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 13:15:15.982 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 13:15:16.047 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:15:16.047 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:15:17.324 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 13:15:17.387 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 13:21:56.929 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:22:15.278 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:30:23.089 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:35:43.011 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:40:55.012 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:42:11.393 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 13:42:11.393 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 13:42:11.457 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:42:11.457 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:42:12.863 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 13:42:12.927 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 13:43:28.715 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 13:43:28.715 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 13:43:28.777 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:43:28.777 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:43:30.105 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 13:43:30.166 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 13:44:36.713 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 13:44:36.713 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 13:44:36.777 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:44:36.777 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:44:37.796 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 13:44:37.859 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 13:52:44.936 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:52:49.490 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:52:50.920 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 13:52:50.929 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:52:50.929 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:52:51.030 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: -1; llm_interrupt_flag: True
2025-07-01 13:52:51.244 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 13:52:51.245 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 13:52:51.308 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:52:51.308 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:52:52.391 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 13:52:52.451 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 13:52:52.835 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-01 13:52:59.051000
2025-07-01 13:52:53.189 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 13:52:53.193 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:52:53.202 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-01 13:52:53.202 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:52:53.202 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:52:53.202 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:52:56.838 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 13:52:56.838 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 13:52:56.903 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:52:56.903 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:52:58.299 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 13:52:58.361 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 13:52:59.088 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 13:53:01.746 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天成都天气怎么样？, 时间戳: 2025-07-01 13:53:07.964000
2025-07-01 13:53:03.479 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 13:53:09.696000
2025-07-01 13:53:03.481 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1732
2025-07-01 13:53:03.547 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:53:03.557 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:53:03.567 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:53:03.913 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:53:03.920 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:53:04.245 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:53:04.255 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:53:04.597 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:53:04.607 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:53:04.948 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:53:04.951 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:53:05.271 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:53:05.271 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:53:05.275 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:53:05.286 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 今天成都天气怎么样？; 响应时间: 0; JD机器人回复: 今天成都天气多云，夜晚有阵雨，温度和昨天差不多，现在体感温度31度，有东风3级，相对湿度63%，紫外线很强，能见度2公里，空气质量不错。适合外出，但晚上记得带把伞。
2025-07-01 13:53:05.286 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:53:05.286 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:53:06.688 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:53:10.721 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:53:14.247 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:53:16.547 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 13:53:16.547 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 13:53:16.611 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:53:16.611 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:53:16.611 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:53:16.667 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 13:53:17.947 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 13:53:18.011 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 13:53:23.018 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东，我想知道你是谁开发的, 时间戳: 2025-07-01 13:53:29.125000
2025-07-01 13:53:23.221 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 13:53:23.221 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:53:23.224 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 你好，东东，我想知道你是谁开发的; 响应时间: 0; JD机器人回复: 
2025-07-01 13:53:23.224 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:53:23.224 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:53:23.224 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:53:34.344 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-01 13:53:40.562000
2025-07-01 13:53:34.656 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 13:53:34.656 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:53:34.664 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 13:53:34.664 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:53:34.664 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:53:34.664 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:53:48.677 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 13:53:48.677 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 13:53:48.741 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:53:48.741 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:53:49.760 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 13:53:49.822 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 13:53:50.202 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好，东东, 时间戳: 2025-07-01 13:53:56.420000
2025-07-01 13:53:50.659 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 13:53:50.660 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:53:50.670 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-01 13:53:50.670 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:53:50.670 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:53:50.670 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:53:52.160 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你是谁开发的？, 时间戳: 2025-07-01 13:53:58.375000
2025-07-01 13:53:52.439 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 13:53:52.440 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:53:52.451 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 你是谁开发的？; 响应时间: 0; JD机器人回复: 
2025-07-01 13:53:52.451 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:53:52.451 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:53:52.451 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:53:59.069 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 13:53:59.069 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 13:53:59.133 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:53:59.133 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:54:00.433 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 13:54:00.497 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 13:54:00.945 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-01 13:54:07.163000
2025-07-01 13:54:00.951 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 13:54:00.951 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:54:00.966 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-01 13:54:00.966 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:54:00.966 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:54:00.966 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:54:02.711 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 13:54:02.712 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:54:02.712 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:54:02.813 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 13:54:03.567 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你如何看待？, 时间戳: 2025-07-01 13:54:09.786000
2025-07-01 13:54:03.883 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 13:54:03.884 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:54:03.894 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 你如何看待？; 响应时间: 0; JD机器人回复: 
2025-07-01 13:54:03.894 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:54:03.894 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:54:03.894 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:54:05.016 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 13:56:21.563 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 13:56:21.563 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 13:56:21.627 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:56:21.627 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:56:22.940 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 13:56:23.003 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 13:56:23.447 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-01 13:56:29.665000
2025-07-01 13:56:23.456 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 13:56:23.459 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:56:23.462 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-01 13:56:23.462 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:56:23.463 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:56:23.463 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:56:24.367 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 13:56:24.368 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:56:24.368 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:56:24.468 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 13:56:26.332 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想克隆声音, 时间戳: 2025-07-01 13:56:32.549000
2025-07-01 13:56:27.350 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 13:56:27.351 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:56:27.358 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 我想克隆声音; 响应时间: 0; JD机器人回复: 
2025-07-01 13:56:27.358 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:56:27.358 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:56:27.358 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:56:32.550 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 13:56:32.550 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 13:56:32.613 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:56:32.614 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:56:33.645 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 13:56:33.708 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 13:56:34.268 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好，东东, 时间戳: 2025-07-01 13:56:40.484000
2025-07-01 13:56:34.275 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 13:56:34.277 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:56:34.289 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-01 13:56:34.289 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:56:34.289 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:56:34.289 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:56:34.834 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 13:56:34.837 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:56:34.837 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:56:34.937 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 13:56:36.196 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我想克隆声音, 时间戳: 2025-07-01 13:56:42.412000
2025-07-01 13:56:36.205 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 13:56:36.206 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:56:36.210 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，我想克隆声音; 响应时间: 0; JD机器人回复: 
2025-07-01 13:56:36.210 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:56:36.210 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:56:36.210 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:56:42.167 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 什么？, 时间戳: 2025-07-01 13:56:48.384000
2025-07-01 13:56:42.168 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 13:56:42.171 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:56:42.171 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:56:42.271 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 13:56:42.467 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 13:56:42.467 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:56:42.477 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 什么？; 响应时间: 0; JD机器人回复: 
2025-07-01 13:56:42.477 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:56:42.477 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:56:42.477 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:57:04.373 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 13:57:04.373 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 13:57:04.438 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:57:04.438 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:57:05.750 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 13:57:05.812 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 13:57:06.192 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-01 13:57:12.405000
2025-07-01 13:57:06.206 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 13:57:06.207 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:57:06.208 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-07-01 13:57:06.208 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:57:06.209 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:57:06.209 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:57:07.419 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 13:57:07.427 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:57:07.427 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:57:07.528 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 13:57:08.182 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我想扣你声音, 时间戳: 2025-07-01 13:57:14.396000
2025-07-01 13:57:09.205 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 13:57:15.421000
2025-07-01 13:57:09.205 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1025
2025-07-01 13:57:09.270 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:57:09.270 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:57:09.281 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:57:09.589 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:57:09.594 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:57:09.916 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:57:09.927 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:57:10.214 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:57:10.214 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:57:10.218 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:57:10.229 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，我想扣你声音; 响应时间: 0; JD机器人回复: 太好了！那我们就开始吧。你是想让我克隆你自己的声音，还是你家人或朋友的声音呢？告诉我，我们就可以开始了哦！
2025-07-01 13:57:10.229 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:57:10.229 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:57:11.698 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:57:14.418 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:57:17.036 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:57:19.855 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 13:57:19.855 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:57:21.844 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，朋友的声音, 时间戳: 2025-07-01 13:57:28.061000
2025-07-01 13:57:22.543 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 13:57:28.758000
2025-07-01 13:57:22.543 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 697
2025-07-01 13:57:22.628 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:57:22.631 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:57:22.641 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:57:22.919 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:57:22.923 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:57:23.206 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:57:23.212 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:57:23.588 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:57:23.597 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:57:23.855 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:57:23.855 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:57:24.200 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:57:24.200 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:57:24.207 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:57:24.217 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，朋友的声音; 响应时间: 0; JD机器人回复: 好的，那我们就开始克隆你朋友的声音吧！首先，我需要你帮忙做一件事，请你跟着我读一段话，好吗？请跟我读：“今天天气真好，我们一起去公园玩吧。” 记得要一字不落哦！
2025-07-01 13:57:24.217 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:57:24.217 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:57:26.468 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:57:29.288 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:57:31.204 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:57:34.727 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:57:36.741 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:57:38.757 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 13:57:38.757 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:57:44.521 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-07-01 13:57:50.738000
2025-07-01 13:57:45.214 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 13:57:51.429000
2025-07-01 13:57:45.215 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 691
2025-07-01 13:57:45.281 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:57:45.292 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:57:45.303 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:57:45.616 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:57:45.627 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:57:45.947 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:57:45.947 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:57:46.261 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:57:46.263 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:57:46.573 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:57:46.580 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:57:46.845 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:57:46.846 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:57:46.849 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:57:46.860 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 太棒了！你已经读得很好了，不过为了让声音更像你的朋友，我们再来一遍好不好？请再跟我读一遍：“今天天气真好，我们一起去公园玩吧。” 加油！
2025-07-01 13:57:46.860 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:57:46.860 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:57:48.025 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:57:50.644 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:57:52.657 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:57:55.784 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:57:57.800 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:57:58.607 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 13:57:58.607 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:58:03.005 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 13:58:06.136 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我们一起去公园散步吧, 时间戳: 2025-07-01 13:58:12.353000
2025-07-01 13:58:06.885 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 13:58:13.100000
2025-07-01 13:58:06.886 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 747
2025-07-01 13:58:06.960 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:58:06.972 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:58:06.983 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:58:07.325 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:58:07.327 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:58:07.614 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:58:07.621 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:58:07.945 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:58:07.945 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:58:08.268 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:58:08.278 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:58:08.601 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:58:08.603 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:58:08.915 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:58:08.915 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:58:08.926 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:58:08.937 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，我们一起去公园散步吧; 响应时间: 0; JD机器人回复: 你读得真好！不过好像少了几个字哦，完整的句子是：“今天天气真好，我们一起去公园玩吧。” 让我们再试一次，好吗？请跟我读：“今天天气真好，我们一起去公园玩吧。” 一起加油！
2025-07-01 13:58:08.937 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:58:08.937 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:58:10.406 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:58:11.039 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 13:58:11.039 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 13:58:11.102 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:58:11.102 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:58:11.102 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:58:11.111 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 13:58:12.416 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 13:58:12.478 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 13:58:12.925 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好东东, 时间戳: 2025-07-01 13:58:19.141000
2025-07-01 13:58:13.555 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 13:58:13.556 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:58:13.563 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，你好东东; 响应时间: 0; JD机器人回复: 
2025-07-01 13:58:13.563 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:58:13.563 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:58:13.563 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:58:13.995 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退下, 时间戳: 2025-07-01 13:58:20.211000
2025-07-01 13:58:14.627 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 13:58:20.836000
2025-07-01 13:58:14.628 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 625
2025-07-01 13:58:14.700 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:58:14.701 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:58:30.367 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 13:58:30.367 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 13:58:30.429 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:58:30.429 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:58:31.748 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 13:58:31.759 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 13:58:33.135 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 13:58:41.903 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-07-01 13:58:48.118000
2025-07-01 13:58:42.614 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 13:58:48.828000
2025-07-01 13:58:42.614 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 710
2025-07-01 13:58:42.685 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:58:42.686 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 13:58:42.686 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 13:58:42.695 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 13:58:42.697 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 车辆信息获取失败了，请重新再试试吧
2025-07-01 13:58:42.697 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 13:58:42.697 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 13:58:42.697 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 13:58:46.421 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 13:58:48.602 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 13:58:49.638 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 13:58:49.638 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 13:58:49.703 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:58:49.703 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:58:50.733 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 13:58:50.795 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 13:58:52.150 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 13:58:52.151 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 13:58:57.461 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 13:58:57.461 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 13:58:57.525 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 13:58:57.525 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 13:58:58.843 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 13:58:58.904 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 13:59:00.264 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好东东，退下, 时间戳: 2025-07-01 13:59:06.480000
2025-07-01 13:59:00.836 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 13:59:07.050000
2025-07-01 13:59:00.836 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 570
2025-07-01 13:59:00.909 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 13:59:00.910 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:01:42.412 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 14:01:42.412 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 14:01:42.474 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 14:01:42.474 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 14:01:43.852 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 14:01:43.857 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 14:03:44.200 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 说了呀，我已经, 时间戳: 2025-07-01 14:03:50.414000
2025-07-01 14:03:44.512 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 14:03:44.513 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:03:44.521 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 说了呀，我已经; 响应时间: 0; JD机器人回复: 
2025-07-01 14:03:44.521 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:03:44.521 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:03:44.521 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:03:47.315 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 14:03:48.966 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你有没有去找他？, 时间戳: 2025-07-01 14:03:55.180000
2025-07-01 14:03:49.265 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 14:03:49.266 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:03:49.277 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 你有没有去找他？; 响应时间: 0; JD机器人回复: 
2025-07-01 14:03:49.277 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:03:49.277 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:03:49.277 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:03:52.247 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我给他发个消息, 时间戳: 2025-07-01 14:03:58.461000
2025-07-01 14:03:52.535 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 14:03:52.539 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:03:52.544 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 我给他发个消息; 响应时间: 0; JD机器人回复: 
2025-07-01 14:03:52.544 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:03:52.544 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:03:52.544 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:04:02.868 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 神经, 时间戳: 2025-07-01 14:04:09.080000
2025-07-01 14:04:03.152 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 14:04:03.152 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:04:03.160 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 神经; 响应时间: 0; JD机器人回复: 
2025-07-01 14:04:03.160 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:04:03.160 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:04:03.160 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:04:03.705 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 14:04:10.574 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 神经病吧, 时间戳: 2025-07-01 14:04:16.788000
2025-07-01 14:04:10.862 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 14:04:10.863 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:04:10.871 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 神经病吧; 响应时间: 0; JD机器人回复: 
2025-07-01 14:04:10.871 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:04:10.871 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:04:10.871 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:04:39.231 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 14:04:40.676 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，可以免输, 时间戳: 2025-07-01 14:04:46.890000
2025-07-01 14:04:41.234 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 14:04:47.446000
2025-07-01 14:04:41.234 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 556
2025-07-01 14:04:41.316 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 14:04:41.316 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:04:41.325 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 14:04:41.336 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 14:04:41.336 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，可以免输; 响应时间: 0; JD机器人回复: 车辆信息获取失败了，请重新再试试吧
2025-07-01 14:04:41.337 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:04:41.337 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:04:41.337 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:04:44.965 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 14:06:18.723 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 14:06:18.724 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 14:06:18.787 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 14:06:18.787 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 14:06:19.863 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 14:06:19.927 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 14:06:20.534 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 14:06:20.534 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 14:06:20.598 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 14:06:20.598 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 14:06:21.631 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 14:06:21.693 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 14:07:07.360 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 14:07:07.361 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 14:07:07.424 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 14:07:07.424 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 14:07:08.720 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 14:07:08.783 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 14:09:06.139 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 14:09:13.090 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 14:09:13.090 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 14:09:19.027 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 没事，有些东西不要也行, 时间戳: 2025-07-01 14:09:25.234000
2025-07-01 14:09:19.314 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 14:09:19.315 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:09:19.323 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 没事，有些东西不要也行; 响应时间: 0; JD机器人回复: 
2025-07-01 14:09:19.323 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:09:19.323 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:09:19.323 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:09:20.066 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，不知道怎么去, 时间戳: 2025-07-01 14:09:26.274000
2025-07-01 14:09:20.318 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 14:09:20.323 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:09:20.334 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，不知道怎么去; 响应时间: 0; JD机器人回复: 
2025-07-01 14:09:20.334 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:09:20.335 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:09:20.335 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:09:34.946 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 14:10:03.283 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我现在这个只有你, 时间戳: 2025-07-01 14:10:09.495000
2025-07-01 14:10:03.600 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 14:10:03.601 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:10:03.601 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，我现在这个只有你; 响应时间: 0; JD机器人回复: 
2025-07-01 14:10:03.601 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:10:03.601 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:10:03.601 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:10:42.510 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 14:10:42.510 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 14:10:42.575 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 14:10:42.575 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 14:10:43.608 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 14:10:43.671 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 14:12:11.637 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 14:12:11.637 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 14:12:11.701 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 14:12:11.701 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 14:12:12.728 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 14:12:12.789 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 14:13:59.067 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 在这参数里面，只需要做基础工作, 时间戳: 2025-07-01 14:14:05.277000
2025-07-01 14:14:00.081 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 14:14:00.083 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:14:00.088 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 在这参数里面，只需要做基础工作; 响应时间: 0; JD机器人回复: 
2025-07-01 14:14:00.088 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:14:00.088 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:14:00.088 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:14:03.187 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯，做了两个对基础动作做完了之后，把这些多余的, 时间戳: 2025-07-01 14:14:09.398000
2025-07-01 14:14:03.538 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 14:14:03.540 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:14:03.541 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，嗯，做了两个对基础动作做完了之后，把这些多余的; 响应时间: 0; JD机器人回复: 
2025-07-01 14:14:03.541 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:14:03.541 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:14:03.541 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:15:03.150 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 就相当于是一套对话, 时间戳: 2025-07-01 14:15:09.351000
2025-07-01 14:15:03.442 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 14:15:03.444 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:15:03.454 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 就相当于是一套对话; 响应时间: 0; JD机器人回复: 
2025-07-01 14:15:03.454 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:15:03.454 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:15:03.454 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:15:06.727 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，但是，我就没成功过, 时间戳: 2025-07-01 14:15:12.937000
2025-07-01 14:15:07.061 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 14:15:07.062 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:15:07.073 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，但是，我就没成功过; 响应时间: 0; JD机器人回复: 
2025-07-01 14:15:07.073 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:15:07.073 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:15:07.073 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:15:08.860 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，不行也不行, 时间戳: 2025-07-01 14:15:15.071000
2025-07-01 14:15:09.134 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 14:15:09.135 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:15:09.146 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，不行也不行; 响应时间: 0; JD机器人回复: 
2025-07-01 14:15:09.146 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:15:09.146 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:15:09.146 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:15:21.225 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，他没提，我不知道他成没成功，他也没提示成功了，他就, 时间戳: 2025-07-01 14:15:27.435000
2025-07-01 14:15:21.900 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 14:15:21.900 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:15:21.901 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，他没提，我不知道他成没成功，他也没提示成功了，他就; 响应时间: 0; JD机器人回复: 
2025-07-01 14:15:21.901 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:15:21.901 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:15:21.901 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:15:21.911 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 14:15:21.911 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 14:15:22.012 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 14:15:25.517 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 问住也没有什么提示，说克隆好的话，我可能是, 时间戳: 2025-07-01 14:15:31.728000
2025-07-01 14:15:25.815 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 14:15:25.818 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:15:25.823 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 问住也没有什么提示，说克隆好的话，我可能是; 响应时间: 0; JD机器人回复: 
2025-07-01 14:15:25.823 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:15:25.823 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:15:25.823 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:15:26.337 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 14:15:26.941 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 14:15:26.941 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 14:15:38.020 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 14:15:38.020 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 14:15:47.777 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 14:15:47.777 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 14:15:47.840 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 14:15:47.840 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 14:15:49.193 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 14:15:49.256 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 14:16:01.684 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 14:16:01.684 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 14:16:01.748 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 14:16:01.748 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 14:16:03.093 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 14:16:03.156 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 14:16:18.619 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 不像因为你们里面还有什么动作的那种数据, 时间戳: 2025-07-01 14:16:24.830000
2025-07-01 14:16:19.635 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 14:16:19.635 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:16:19.639 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: 不像因为你们里面还有什么动作的那种数据; 响应时间: 0; JD机器人回复: 
2025-07-01 14:16:19.639 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:16:19.639 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:16:19.639 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:16:20.310 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 14:16:34.385 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-07-01 14:16:40.595000
2025-07-01 14:16:34.681 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 14:16:34.683 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:16:34.685 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 14:16:34.685 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:16:34.685 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:16:34.685 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:20:57.161 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 14:20:57.162 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 14:20:57.226 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 14:20:57.227 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 14:20:58.270 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 14:20:58.335 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 14:21:37.398 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 14:21:46.656 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 14:21:46.656 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 14:22:26.901 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 14:22:26.901 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 14:22:26.966 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 14:22:26.966 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 14:22:27.994 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 14:22:28.057 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 14:25:39.210 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 14:25:39.210 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 14:25:45.146 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我要过去吗？, 时间戳: 2025-07-01 14:25:51.352000
2025-07-01 14:25:45.806 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 14:25:52.007000
2025-07-01 14:25:45.807 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 655
2025-07-01 14:25:45.882 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 14:25:45.883 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 14:25:45.883 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 14:25:45.893 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 14:25:45.894 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 93cfa2c9-5617-11f0-80b9-dc4546c07870; requestId: 9d31933b-3232-4a57-b3e7-c186a54fee1b_joyinside; asr: ，我要过去吗？; 响应时间: 0; JD机器人回复: 车辆信息获取失败了，请重新再试试吧
2025-07-01 14:25:45.894 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 14:25:45.894 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 14:25:45.894 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 14:25:48.964 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 14:25:49.520 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 14:29:12.812 - chat_with_robot - websocket_client_thread.py - _send_worker - line 355 - ERROR - 发送消息失败: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-01 14:29:12.816 - chat_with_robot - websocket_client_thread.py - _on_error - line 320 - ERROR - WebSocket错误: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-07-01 14:29:12.823 - chat_with_robot - websocket_client_thread.py - _heartbeat_worker - line 106 - ERROR - 心跳发送失败: [SSL: BAD_LENGTH] bad length (_ssl.c:2503)
2025-07-01 14:29:12.831 - chat_with_robot - websocket_client_thread.py - _on_close - line 325 - INFO - WebSocket连接关闭
2025-07-01 14:29:12.833 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-01 14:29:12.834 - chat_with_robot - websocket_client_thread.py - connect - line 141 - ERROR - WebSocket连接失败: cannot join current thread
2025-07-01 14:29:12.863 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:12.924 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:12.988 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:13.049 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:13.104 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:13.173 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:13.224 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:13.291 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:13.349 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:13.408 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:13.466 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:13.528 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:13.591 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:13.649 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:13.708 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:13.769 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:13.825 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:13.889 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:13.947 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:14.013 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:14.068 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:14.126 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:14.188 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:14.308 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:14.321 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:14.366 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:14.426 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:14.484 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:14.546 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:14.600 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:14.663 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:14.731 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:14.785 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:14.846 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:14.909 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:14.972 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:15.078 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:15.093 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:15.142 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:15.204 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:15.265 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:15.435 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:15.445 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:15.496 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:15.559 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:15.614 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:15.675 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:15.735 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:15.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:15.864 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:15.927 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:15.976 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:16.039 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:16.094 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:16.160 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:16.223 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:16.278 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:16.337 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:16.397 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:16.459 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:16.525 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:16.577 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:16.647 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:16.704 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:16.757 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:16.819 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:16.881 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:16.938 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:16.997 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:17.061 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:17.112 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:17.184 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:17.237 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:17.302 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:17.358 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:17.421 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:17.475 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:17.534 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:17.596 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:17.668 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:17.715 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:17.783 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:17.845 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:17.903 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:17.956 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:18.011 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:18.081 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:18.137 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:18.195 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:18.254 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:18.324 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:18.380 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:18.439 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:18.507 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:18.559 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:18.620 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:18.676 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:18.743 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:18.799 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:18.932 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:18.939 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:18.977 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:19.040 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:19.096 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:19.157 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:19.218 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:19.282 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:19.341 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:19.399 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:19.455 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:19.523 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:19.582 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:19.630 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:19.694 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:19.756 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:19.818 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:19.882 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:19.935 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:19.999 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:20.062 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:20.114 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:20.179 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:20.240 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:20.294 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:20.367 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:20.421 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:20.477 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:20.537 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:20.643 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:20.654 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:20.716 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:20.780 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:20.847 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:20.902 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:20.967 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:21.019 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:21.078 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:21.157 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:21.198 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:21.261 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:21.315 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:21.382 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:21.480 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:21.500 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:21.557 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:21.618 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:21.679 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:21.744 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:21.801 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:21.870 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:21.917 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:21.976 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:22.039 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:22.101 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:22.162 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:22.218 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:22.279 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:22.344 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:22.398 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:22.455 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:22.883 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:22.895 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:22.955 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:23.006 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:23.075 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:23.130 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:23.182 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:23.371 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:23.383 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:23.443 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:23.492 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:23.553 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:23.618 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:23.674 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:23.734 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:23.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:23.851 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:23.912 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:23.975 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:24.032 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:24.103 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:24.154 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:24.214 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:24.272 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:24.337 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:24.423 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:24.455 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:24.511 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:24.575 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:24.643 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:24.697 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:24.754 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:24.816 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:24.883 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:24.941 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:25.050 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:25.057 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:25.121 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:25.171 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:25.235 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:25.287 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:25.354 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:25.418 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:25.476 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:25.532 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:25.590 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:25.653 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:25.713 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:25.805 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:25.843 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:25.896 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:25.958 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:26.013 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:26.073 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:26.140 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:26.193 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:26.249 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:26.309 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:26.378 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:26.430 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:26.495 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:26.558 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:26.612 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:26.676 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:26.728 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:26.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:26.858 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:26.915 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:26.976 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:27.033 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:27.092 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:27.152 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:27.214 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:27.275 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:27.331 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:27.393 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:27.453 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:27.513 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:27.573 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:27.633 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:27.693 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:27.751 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:27.815 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:27.880 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:27.936 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:27.995 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:28.052 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:28.115 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:28.175 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:28.232 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:28.292 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:28.352 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:28.410 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:28.479 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:28.533 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:28.600 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:28.648 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:28.714 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:28.774 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:28.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:28.892 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:28.957 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:29.011 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:29.072 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:29.133 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:29.189 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:29.257 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:29.312 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:29.372 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:29.437 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:29.497 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:29.547 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:29.608 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:29.674 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:29.733 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:29.790 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:29.921 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:29.926 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:29.970 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:30.032 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:30.091 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:30.150 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:30.210 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:30.275 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:30.337 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:30.390 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:30.470 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:30.522 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:30.582 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:30.749 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:30.850 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:30.907 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:30.963 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:31.031 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:31.627 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:31.694 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:31.828 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:31.836 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:31.876 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:31.932 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:32.002 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:32.057 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:32.117 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:32.179 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:32.233 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:32.296 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:32.357 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:32.410 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:32.470 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:32.533 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:32.592 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:32.654 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:32.711 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:32.771 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:32.828 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:32.890 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:32.957 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:33.014 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:33.072 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:33.135 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:33.188 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:33.250 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:33.313 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:33.382 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:33.435 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:33.498 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:33.552 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:33.617 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:33.672 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:33.753 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:33.792 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:33.857 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:33.915 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:33.968 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:34.031 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:34.092 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:34.148 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:34.211 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:34.276 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:34.329 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:34.393 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:34.455 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:34.511 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:34.570 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:34.629 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:34.696 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:34.756 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:34.811 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:34.868 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:34.927 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:34.993 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:35.056 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:35.112 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:35.173 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:35.233 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:35.297 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:35.358 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:35.422 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:35.499 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:35.565 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:35.629 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:35.691 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:35.744 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:35.809 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:35.928 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:35.936 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:35.996 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:36.063 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:36.110 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:36.177 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:36.230 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:36.295 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:36.353 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:36.422 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:36.473 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:36.539 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:36.592 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:36.659 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:36.713 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:36.776 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:36.832 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:36.888 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:36.951 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:37.013 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:37.070 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:37.129 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:37.190 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:37.248 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:37.309 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:37.374 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:37.430 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:37.493 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:37.550 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:37.617 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:37.679 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:37.729 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:37.787 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:37.853 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:37.908 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:37.975 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:38.028 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:38.093 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:38.151 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:38.214 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:38.277 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:38.333 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:38.390 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:38.450 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:38.509 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:38.569 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:38.635 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:38.696 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:38.750 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:38.818 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:38.887 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:38.933 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:38.990 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:39.059 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:39.118 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:39.170 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:39.235 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:39.297 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:39.352 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:39.415 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:39.472 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:39.537 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:39.593 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:39.648 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:39.712 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:39.770 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:39.834 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:39.900 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:39.949 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:40.015 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:40.068 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:40.137 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:40.191 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:40.259 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:40.314 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:40.376 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:40.431 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:40.495 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:40.561 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:40.609 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:40.673 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:40.732 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:40.791 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:40.856 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:40.917 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:41.050 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:41.058 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:41.180 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:41.186 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:41.243 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:41.307 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:41.365 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:41.420 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:41.513 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:41.543 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:41.601 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:41.659 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:41.728 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:41.780 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:41.839 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:41.901 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:41.963 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:42.020 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:42.084 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:42.142 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:42.201 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:42.263 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:42.318 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:42.384 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:42.439 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:42.502 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:42.566 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:42.633 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:42.678 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:42.746 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:42.801 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:42.862 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:42.925 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:42.982 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:43.043 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:43.103 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:43.159 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:43.218 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:43.284 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:43.339 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:43.405 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:43.468 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:43.519 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:43.578 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:43.638 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:43.701 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:43.760 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:43.821 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:43.886 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:43.946 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:44.008 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:44.071 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:44.124 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:44.187 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:44.255 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:44.313 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:44.377 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:44.431 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:44.482 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:44.552 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:44.604 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:44.666 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:44.730 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:44.790 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:44.847 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:44.941 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:44.973 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:45.027 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:45.082 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:45.146 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:45.203 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:45.262 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:45.338 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:45.388 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:45.458 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:45.504 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:45.568 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:45.646 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:45.693 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:45.750 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:45.809 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:45.878 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:46.005 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:46.013 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:46.055 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:46.121 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:46.196 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:46.263 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:46.516 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:46.584 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:46.650 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:46.721 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:46.779 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:46.854 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:46.960 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:46.965 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:47.030 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:47.087 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:47.155 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:47.215 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:47.284 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:47.342 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:47.403 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:47.465 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:47.537 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:47.582 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:47.647 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:47.708 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:47.790 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:47.839 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:47.897 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:47.959 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:48.018 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:48.085 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:48.144 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:48.199 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:48.263 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:48.323 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:48.391 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:48.433 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:48.493 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:48.561 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:48.616 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:48.684 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:48.743 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:48.827 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:48.862 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:48.926 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:48.980 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:49.039 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:49.101 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:49.159 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:49.222 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:49.274 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:49.340 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:49.404 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:49.463 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:49.523 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:49.579 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:49.642 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:49.699 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:49.766 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:49.820 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:49.876 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:49.942 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:50.002 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:50.059 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:50.118 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:50.176 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:50.237 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:50.298 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:50.357 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:50.421 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:50.478 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:50.538 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:50.602 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:50.665 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:50.724 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:50.779 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:50.833 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:50.895 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:50.961 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:51.023 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:51.077 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:51.143 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:51.197 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:51.262 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:51.325 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:51.384 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:51.450 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:51.503 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:51.576 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:51.668 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:51.829 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:51.834 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.034 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.038 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.040 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.085 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.151 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.213 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.274 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.337 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.388 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.449 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.514 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.569 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.632 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.695 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.759 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.812 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.872 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.928 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:52.993 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:53.053 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:53.115 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:53.175 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:53.231 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:53.288 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:53.349 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:53.409 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:53.466 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:53.523 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:53.583 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:53.644 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:53.705 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:53.768 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:53.824 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:53.885 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:53.946 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:54.008 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:54.064 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:54.133 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:54.186 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:54.250 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:54.308 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:54.371 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:54.434 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:54.490 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:54.551 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:54.616 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:54.672 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:54.734 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:54.792 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:54.854 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:54.919 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:54.973 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:55.037 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:55.086 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:55.149 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:55.210 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:55.274 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:55.334 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:55.395 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:55.446 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:55.515 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:55.574 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:55.632 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:55.693 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:55.752 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:55.810 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:55.875 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:55.931 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:55.990 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:56.052 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:56.106 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:56.175 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:56.234 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:56.293 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:56.354 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:56.407 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:56.473 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:56.526 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:56.589 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:56.647 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:56.706 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:56.775 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:56.825 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:56.891 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:56.951 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:57.012 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:57.069 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:57.126 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:57.199 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:57.254 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:57.312 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:57.372 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:57.428 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:57.499 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:57.552 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:57.613 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:57.667 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:57.734 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:57.789 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:57.847 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:57.906 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:57.975 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:58.027 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:58.094 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:58.153 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:58.209 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:58.269 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:58.325 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:58.386 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:58.445 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:58.507 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:58.565 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:58.627 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:58.684 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:58.762 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:58.806 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:58.866 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:58.928 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:58.990 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:59.044 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:59.109 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:59.169 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:59.230 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:59.286 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:59.345 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:59.408 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:59.466 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:59.530 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:59.583 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:59.646 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:59.704 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:59.764 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:59.832 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:59.887 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:29:59.947 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:00.006 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:00.074 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:00.134 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:00.187 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:00.244 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:00.311 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:00.372 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:00.430 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:00.486 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:00.544 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:00.606 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:00.669 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:00.728 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:00.786 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:00.845 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:00.904 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:00.964 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:01.024 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:01.084 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:01.146 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:01.205 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:01.266 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:01.321 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:01.386 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:01.444 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:01.508 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:01.568 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:01.627 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:01.684 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:01.743 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:01.806 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:01.867 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:01.929 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:01.993 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:02.047 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:02.106 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:02.167 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:02.228 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:02.287 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:02.354 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:02.413 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:02.466 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:02.532 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:02.591 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:02.652 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:02.707 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:02.774 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:02.826 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:02.891 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:02.946 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:03.006 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:03.074 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:03.124 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:03.184 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:03.247 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:03.308 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:03.366 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:03.424 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:03.493 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:03.544 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:03.606 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:03.669 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:03.729 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:03.791 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:03.844 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:03.910 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:03.974 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:04.037 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:04.090 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:04.160 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:04.210 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:04.273 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:04.326 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:04.390 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:04.448 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:04.509 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:04.570 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:04.628 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:04.695 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:04.749 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:04.811 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:04.868 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:04.933 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:04.990 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:05.041 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:05.110 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:05.164 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:05.225 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:05.284 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:05.344 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:05.404 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:05.467 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:05.526 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:05.591 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:05.650 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:05.706 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:05.768 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:05.828 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:05.889 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:05.949 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:06.005 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:06.073 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:06.130 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:06.190 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:06.249 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:06.307 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:06.365 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:06.424 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:06.488 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:06.546 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:06.606 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:06.671 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:06.726 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:06.786 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:06.844 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:06.903 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:06.966 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:07.027 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:07.088 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:07.147 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:07.208 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:07.265 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:07.326 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:07.387 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:07.444 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:07.515 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:07.572 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:07.624 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:07.690 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:07.750 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:07.803 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:07.872 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:07.927 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:07.989 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:08.048 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:08.117 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:08.168 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:08.224 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:08.286 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:08.345 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:08.406 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:08.471 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:08.535 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:08.586 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:08.644 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:08.705 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:08.767 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:08.836 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:08.888 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:08.956 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:09.014 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:09.073 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:09.132 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:09.184 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:09.245 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:09.307 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:09.366 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:09.423 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:09.488 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:09.544 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:09.604 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:09.665 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:09.723 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:09.784 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:09.845 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:09.911 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:09.967 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:10.034 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:10.091 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:10.154 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:10.210 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:10.265 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:10.333 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:10.391 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:10.454 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:10.511 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:10.574 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:10.635 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:10.694 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:10.758 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:10.809 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:10.875 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:10.931 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:10.990 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:11.058 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:11.112 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:11.174 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:11.232 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:11.286 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:11.354 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:11.405 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:11.467 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:11.531 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:11.589 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:11.658 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:11.712 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:11.768 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:11.805 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 14:30:11.808 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 14:30:11.868 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 14:30:11.869 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 14:30:15.021 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 14:30:15.024 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-01 14:30:15.029 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-01 14:30:15.104 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:15.105 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 14:30:15.168 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:15.233 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:15.291 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:15.383 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:17.521 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:17.599 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:17.659 - chat_with_robot - websocket_client_thread.py - _on_error - line 320 - ERROR - WebSocket错误: Handshake status 401 Unauthorized -+-+- {'date': 'Tue, 01 Jul 2025 06:30:23 GMT', 'content-type': 'text/plain', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'no-cache, no-store, max-age=0, must-revalidate', 'expires': '0', 'pfinder': 'EAUfueIEt+XETou32gKLt9oCueIE69bO16yOjh8=', 'pragma': 'no-cache', 'referrer-policy': 'no-referrer', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'x-xss-protection': '1 ; mode=block', 'server': 'jfe', 'strict-transport-security': 'max-age=86400'} -+-+- None
2025-07-01 14:30:17.661 - chat_with_robot - websocket_client_thread.py - _on_close - line 325 - INFO - WebSocket连接关闭
2025-07-01 14:30:17.663 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-01 14:30:17.665 - chat_with_robot - websocket_client_thread.py - connect - line 141 - ERROR - WebSocket连接失败: cannot join current thread
2025-07-01 14:30:17.678 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:17.723 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:17.770 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:17.831 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:17.890 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:17.953 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:18.016 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:18.076 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:18.133 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:18.192 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:18.250 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:18.311 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:18.373 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:18.432 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:18.493 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:18.553 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:18.616 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:18.674 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:18.735 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:18.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:18.851 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:18.916 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:18.973 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:19.033 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:19.091 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:19.152 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:19.213 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:19.276 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:19.336 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:19.396 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:19.454 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:19.517 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:19.574 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:19.631 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:19.692 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:19.755 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:19.816 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:19.876 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:19.936 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:19.994 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:20.052 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:20.113 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:20.172 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:20.237 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:20.296 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:20.358 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:20.411 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:20.475 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:20.537 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:20.598 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:20.659 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:20.719 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:20.777 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:20.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:20.904 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:20.954 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:21.017 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:21.083 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:21.141 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:21.200 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:21.258 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:21.319 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:21.385 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:21.439 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:21.494 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:21.565 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:21.621 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:21.677 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:21.739 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:21.796 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:21.856 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:21.917 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:21.977 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:22.034 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:22.095 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:22.161 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:22.213 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:22.276 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:22.338 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:22.396 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:22.457 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:22.523 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:22.582 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:22.635 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:22.695 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:22.757 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:22.818 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:22.879 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:22.932 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:22.998 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:23.061 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:23.123 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:23.183 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:23.237 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:23.295 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:23.358 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:23.427 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:23.484 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:23.541 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:23.596 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:23.664 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:23.719 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:23.781 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:23.843 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:23.894 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:23.959 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:24.021 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:24.084 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:24.139 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:24.196 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:24.258 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:24.323 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:24.382 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:24.442 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:24.502 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:24.558 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:24.622 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:24.681 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:24.743 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:24.805 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:24.861 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:24.922 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:24.989 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:25.038 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:25.098 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:25.152 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:25.216 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:25.278 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:25.335 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:25.396 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:25.453 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:25.521 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:25.577 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:25.635 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:25.696 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:25.760 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:25.816 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:25.876 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:25.949 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:25.999 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:26.054 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:26.116 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:26.178 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:26.241 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:26.296 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:26.353 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:26.415 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:26.474 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:26.539 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:26.595 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:26.656 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:26.716 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:26.775 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:26.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:26.897 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:26.956 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:27.016 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:27.071 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:27.136 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:27.193 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:27.258 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:27.312 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:27.384 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:27.432 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:27.500 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:27.569 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:27.618 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:27.674 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:27.737 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:27.795 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:27.854 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:27.916 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:27.974 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:28.037 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:28.096 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:28.162 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:28.215 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:28.277 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:28.335 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:28.399 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:28.457 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:28.514 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:28.580 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:28.639 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:28.694 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:28.755 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:28.812 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:28.875 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:28.942 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:28.994 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:29.057 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:29.116 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:29.177 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:29.237 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:29.296 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:29.353 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:29.418 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:29.475 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:29.535 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:29.597 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:29.654 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:29.716 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:29.773 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:29.834 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:29.892 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:29.953 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:30.016 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:30.071 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:30.131 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:30.199 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:30.252 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:30.313 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:30.373 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:30.432 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:30.499 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:30.553 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:30.614 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:30.670 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:30.733 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:30.793 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:30.857 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:30.915 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:30.971 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:31.032 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:31.095 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:31.150 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:31.210 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:31.273 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:31.339 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:31.393 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:31.456 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:31.518 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:31.573 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:31.636 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:31.692 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:31.751 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:31.813 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:31.874 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:31.936 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:31.996 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:32.054 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:32.113 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:32.176 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:32.234 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:32.294 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:32.353 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:32.416 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:32.471 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:32.537 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:32.599 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:32.659 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:32.711 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:32.772 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:32.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:32.899 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:32.958 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:33.026 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:33.080 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:33.135 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:33.192 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:33.257 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:33.320 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:33.378 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:33.435 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:33.500 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:33.555 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:33.621 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:33.672 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:33.737 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:33.791 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:33.850 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:33.915 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:33.976 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:34.036 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:34.095 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:34.157 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:34.223 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:34.279 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:34.334 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:34.399 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:34.459 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:34.517 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:34.587 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:34.637 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:34.696 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:34.752 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:34.818 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:34.886 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:34.946 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:34.999 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:35.060 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:35.119 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:35.185 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:35.243 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:35.305 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:35.360 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:35.415 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:35.481 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:35.537 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:35.595 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:35.660 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:35.722 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:35.778 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:35.845 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:35.898 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:35.956 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:36.020 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:36.082 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:36.145 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:36.198 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:36.259 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:36.325 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:36.377 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:36.439 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:36.499 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:36.558 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:36.622 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:36.677 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:36.740 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:36.800 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:36.859 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:36.916 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:36.985 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:37.038 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:37.109 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:37.155 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:37.218 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:37.278 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:37.333 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:37.396 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:37.453 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:37.513 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:37.575 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:37.634 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:37.701 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:37.753 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:37.815 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:37.875 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:37.939 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:37.999 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:38.057 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:38.115 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:38.176 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:38.234 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:38.291 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:38.352 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:38.416 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:38.475 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:38.533 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:38.596 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:38.654 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:38.715 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:38.774 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:38.839 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:38.894 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:38.952 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:39.017 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:39.087 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:39.141 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:39.200 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:39.253 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:39.314 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:39.382 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:39.433 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:39.493 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:39.560 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:39.622 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:39.681 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:39.734 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:39.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:39.856 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:39.912 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:39.982 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:40.045 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:40.098 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:40.163 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:40.216 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:40.277 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:40.379 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:40.408 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:40.475 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:40.530 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:40.601 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:40.655 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:40.710 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:40.765 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:40.828 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:40.888 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:40.945 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:41.005 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:41.065 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:41.135 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:41.190 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:41.248 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:41.307 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:41.370 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:41.436 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:41.490 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:41.547 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:41.612 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:41.665 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:41.733 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:41.791 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:41.847 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:41.905 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:41.969 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:42.026 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:42.090 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:42.149 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:42.212 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:42.267 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:42.329 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:42.390 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:42.450 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:42.515 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:42.570 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:42.628 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:42.690 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:42.747 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:42.808 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:42.869 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:42.928 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:42.992 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:43.057 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:43.113 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:43.167 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:43.234 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:43.285 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:43.352 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:43.409 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:43.470 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:43.528 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:43.587 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:43.646 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:43.713 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:43.765 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:43.833 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:43.900 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:43.948 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:44.008 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:44.065 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:44.127 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:44.187 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:44.248 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:44.309 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:44.367 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:44.427 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:44.494 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:44.554 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:44.613 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:44.674 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:44.737 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:44.806 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:44.864 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:44.921 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:44.978 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:45.047 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:45.105 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:45.162 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:45.218 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:45.276 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:45.343 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:45.395 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:45.457 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:45.515 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:45.581 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:45.640 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:45.699 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:45.757 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:45.818 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:45.885 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:45.949 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:45.996 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:46.066 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:46.123 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:46.181 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:46.241 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:46.297 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:46.309 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 14:30:46.310 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 14:30:46.363 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 14:30:46.364 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 14:30:51.333 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 14:30:51.337 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-01 14:30:51.340 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-01 14:30:55.052 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:55.053 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 14:30:55.135 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:55.205 - chat_with_robot - websocket_client_thread.py - _on_error - line 320 - ERROR - WebSocket错误: Handshake status 401 Unauthorized -+-+- {'date': 'Tue, 01 Jul 2025 06:31:01 GMT', 'content-type': 'text/plain', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'no-cache, no-store, max-age=0, must-revalidate', 'expires': '0', 'pfinder': 'EAUfueIEt+XETu3f5ALt3+QCueIEkZ/Siq6Ojh8=', 'pragma': 'no-cache', 'referrer-policy': 'no-referrer', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'x-xss-protection': '1 ; mode=block', 'server': 'jfe', 'strict-transport-security': 'max-age=86400'} -+-+- None
2025-07-01 14:30:55.208 - chat_with_robot - websocket_client_thread.py - _on_close - line 325 - INFO - WebSocket连接关闭
2025-07-01 14:30:55.209 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-01 14:30:55.210 - chat_with_robot - websocket_client_thread.py - connect - line 141 - ERROR - WebSocket连接失败: cannot join current thread
2025-07-01 14:30:55.222 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:55.255 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:55.313 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:55.370 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:55.432 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:55.495 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:55.553 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:55.614 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:55.675 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:55.729 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:55.787 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:55.849 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:55.915 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:55.974 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:56.035 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:56.093 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:56.153 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:56.211 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:56.272 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:56.331 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:56.389 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:56.452 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:56.509 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:56.575 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:56.630 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:56.694 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:56.756 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:56.812 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:56.871 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:56.938 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:56.993 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:57.056 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:57.113 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:57.175 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:57.232 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:57.290 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:57.349 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:57.419 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:57.473 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:57.531 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:57.592 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:57.656 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:57.720 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:57.776 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:57.837 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:57.889 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:57.950 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:58.011 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:58.075 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:58.139 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:58.194 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:58.259 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:58.312 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:58.376 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:58.433 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:58.498 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:58.553 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:58.620 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:58.678 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:58.737 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:58.793 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:58.852 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:58.916 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:58.978 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:59.040 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:59.099 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:59.153 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:59.217 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:59.280 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:59.337 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:59.398 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:59.458 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:59.513 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:59.575 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:59.632 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:59.692 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:59.756 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:59.815 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:59.870 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:59.935 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:30:59.996 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:00.053 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:00.114 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:00.179 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:00.241 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:00.297 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:00.354 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:00.415 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:00.475 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:00.534 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:00.595 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:00.653 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:00.712 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:00.772 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:00.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:00.901 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:00.960 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:01.017 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:01.078 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:01.135 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:01.195 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:01.255 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:01.323 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:01.378 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:01.431 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:01.499 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:01.557 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:01.619 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:01.673 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:01.735 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:01.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:01.852 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:01.915 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:01.977 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:02.034 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:02.090 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:02.176 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:02.246 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:02.291 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:02.347 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:02.407 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:02.467 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:02.527 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:02.593 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:02.665 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:02.731 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:02.782 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:02.850 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:02.901 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:02.961 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:03.027 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:03.082 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:03.153 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:03.205 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:03.265 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:03.323 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:03.379 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:03.440 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:03.508 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:03.566 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:03.621 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:03.685 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:03.746 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:03.806 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:03.866 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:03.926 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:03.986 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:04.043 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:04.106 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:04.166 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:04.226 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:04.290 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:04.343 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:04.403 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:04.459 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:04.526 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:04.587 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:04.648 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:04.710 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:04.775 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:04.835 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:04.886 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:04.945 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:05.002 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:05.064 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:05.121 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:05.191 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:05.247 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:05.309 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:05.367 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:05.435 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:05.487 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:05.545 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:05.600 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:05.662 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:05.727 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:05.789 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:05.843 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:05.903 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:05.964 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:06.027 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:06.079 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:06.140 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:06.208 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:06.262 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:06.324 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:06.385 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:06.445 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:06.505 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:06.563 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:06.632 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:06.687 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:06.746 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:06.801 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:06.865 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:06.924 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:06.998 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:07.080 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:07.153 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:07.225 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:07.285 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:07.374 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:07.421 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:07.493 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:07.539 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:07.594 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:07.662 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:07.723 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:07.772 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:07.830 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:07.891 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:07.952 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:08.015 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:08.075 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:08.131 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:08.190 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:08.256 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:08.318 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:08.375 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:08.439 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:08.497 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:08.555 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:08.622 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:08.677 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:08.738 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:08.798 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:08.852 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:08.916 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:08.981 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:09.035 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:09.096 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:09.158 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:09.215 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:09.279 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:09.332 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:09.391 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:09.453 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:09.515 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:09.579 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:09.635 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:09.699 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:09.753 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:09.814 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:09.874 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:09.936 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:09.994 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:10.053 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:10.110 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:10.171 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-07-01 14:31:10.230 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
