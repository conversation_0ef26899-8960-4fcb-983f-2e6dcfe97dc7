STATE_STAND_LOW = "力控状态（静止站立）且步态为平地低速步态"
STATE_STAND_MEDIUM = "力控状态（静止站立）且步态为平地中速步态"
STATE_STAND_HIGH = "力控状态（静止站立）且步态为平地高速步态"
STATE_DOWN = "趴下状态"
STATE_STEP_LOW = ["正在以平地低速步态踏步或正在根据轴指令扭动身体"]
STATE_STEP_MEDIUM = ["正在以平地中速步态踏步"]
STATE_STEP_HIGH = ["正在以平地高速步态踏步"]
STATE_DOING_STANDING = ["准备起立状态", "正在起立状态"]

COMPATIBLE_STANDING_STATES = [STATE_STAND_LOW, STATE_STAND_MEDIUM, STATE_STAND_HIGH]

ALL_STATES = [
    "趴下状态",
    "正在执行向前跳",
    "准备起立状态",
    "正在起立状态",
    "力控状态（静止站立）且步态为平地低速步态",
    "正在以平地低速步态踏步或正在根据轴指令扭动身体",
    "正在执行扭身体",
    "正在执行扭身跳",
    "力控状态（静止站立）且步态为通用越障步态",
    "正在以通用越障步态踏步",
    "力控状态（静止站立）且步态为平地中速步态",
    "正在以平地中速步态踏步",
    "力控状态（静止站立）且步态为平地高速步态",
    "正在以平地高速步态踏步",
    "力控状态（静止站立）且步态为抓地越障步态",
    "正在以抓地越障步态踏步",
    "正在执行太空步",
    "力控状态（静止站立）且步态为高踏步越障步态",
    "正在以高踏步越障步态踏步",
    "正在趴下状态",
    "失控保护状态",
    "姿态调整状态",
    "正在执行翻身",
    "回零状态",
    "正在执行后空翻",
    "正在执行打招呼"
]

valid_start_state = {
    "前进": {
        "start_states": COMPATIBLE_STANDING_STATES,
    },
    "后退": {
        "start_states": COMPATIBLE_STANDING_STATES,
    },
    "左平移": {
        "start_states": COMPATIBLE_STANDING_STATES,
    },
    "右平移": {
        "start_states": COMPATIBLE_STANDING_STATES,
    },
    "左转": {
        "start_states": COMPATIBLE_STANDING_STATES,
    },
    "右转": {
        "start_states": COMPATIBLE_STANDING_STATES,
    },
    "起立": {
        "start_states": [STATE_DOWN],
    },
    "趴下": {
        "start_states": COMPATIBLE_STANDING_STATES,
        "end_state": STATE_DOWN,
    },
    "打招呼": {
        "start_states": COMPATIBLE_STANDING_STATES + [STATE_DOWN],
    },
    "向前跳": {
        "start_states": COMPATIBLE_STANDING_STATES,
    },
    "摇尾巴撒娇": {
        "start_states": COMPATIBLE_STANDING_STATES,
    },
    "抬头": {
        "start_states": COMPATIBLE_STANDING_STATES,
    },
    "悠闲": {
        "start_states": COMPATIBLE_STANDING_STATES,
    },
    "兴奋": {
        "start_states": COMPATIBLE_STANDING_STATES,
    },
    "停止运动": {
        "start_states": ALL_STATES + ["未知模式"],
    },
    "沮丧": {
        "start_states": COMPATIBLE_STANDING_STATES,
    },
    "倾听": {
        "start_states": COMPATIBLE_STANDING_STATES,
    },
    "关闭遇到障碍停止模式": {
        "start_states": COMPATIBLE_STANDING_STATES,
    },
    "开启跟随模式": {
        "start_states": COMPATIBLE_STANDING_STATES + STATE_DOING_STANDING,  # 加入 STATE_DOING_STANDING 兜底起立状态信息泄露
    },
    "开启遇到障碍停止模式":{
        "start_states": COMPATIBLE_STANDING_STATES,
    },
    "低速": {
        "start_states": [STATE_STAND_MEDIUM, STATE_STAND_HIGH, STATE_STEP_MEDIUM, STATE_STEP_HIGH],
        "end_state": STATE_STAND_LOW,
    },
    "中速": {
        "start_states": [STATE_STAND_LOW, STATE_STAND_HIGH, STATE_STEP_LOW, STATE_STEP_HIGH],
        "end_state": STATE_STAND_MEDIUM,
    },
    "高速": {
        "start_states": [STATE_STAND_LOW, STATE_STAND_MEDIUM, STATE_STEP_LOW, STATE_STEP_MEDIUM],
        "end_state": STATE_STAND_HIGH,
    },
    "扭身跳": {
        "start_states": COMPATIBLE_STANDING_STATES,
    },
    "关闭跟随模式": {
        "start_states": ALL_STATES + ["未知模式"],
    },
    "舞蹈": {
    "start_states": COMPATIBLE_STANDING_STATES,
    },
}