[13:07:14.136] ERROR    | audio_action_controller.py:130 - Modbus 客户端连接失败: 192.168.2.88:502
[13:07:17.976] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:07:17.977] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:07:17.977] INFO     | audio_action_controller.py:292 - 正在加载音频: ./asserts/ding.wav
[13:07:17.979] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:07:17.979] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.9675963521003723秒
[13:07:17.980] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:07:17.980] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:07:17.981] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:17.981] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:07:17.982] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:07:17.982] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:18.103] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:18.134] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:18.224] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:18.286] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:18.345] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:18.437] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:18.466] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:18.587] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:18.588] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:18.708] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:18.739] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:18.830] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:18.890] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:18.992] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:07:18.993] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:07:19.041] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:19.076] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:07:19.076] INFO     | audio_action_controller.py:67 - 检测到MP3文件，已应用针对压缩音频的优化设置
[13:07:19.076] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/tts/dog_ok.mp3
[13:07:19.084] INFO     | audio_action_controller.py:42 - 音频音量设置为: 85.0% (针对WAV无损格式优化)
[13:07:19.084] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 4.439773082733154秒
[13:07:19.085] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:07:19.085] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:07:19.086] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:07:19.086] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:19.086] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:07:19.086] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:19.208] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:19.238] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:19.329] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:19.389] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:19.449] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:19.541] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:19.570] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:19.691] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:19.691] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:19.812] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:19.844] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:19.933] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:19.995] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:20.055] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:20.147] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:20.176] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:20.296] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:20.297] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:20.417] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:20.449] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:20.538] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:20.599] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:20.659] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:20.750] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:20.780] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:20.901] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:20.901] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:21.022] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:21.052] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:21.143] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:21.204] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:21.264] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:21.355] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:21.386] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:21.505] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:21.507] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:21.628] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:21.657] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:21.749] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:21.807] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:21.870] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:21.958] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:21.991] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:22.110] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:22.112] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:22.233] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:22.262] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:22.354] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:22.413] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:22.475] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:22.564] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:22.596] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:22.716] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:22.718] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:22.839] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:22.867] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:22.960] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:23.019] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:23.081] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:23.170] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:23.203] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:23.321] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:23.324] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:23.473] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:23.523] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:07:23.524] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:07:23.624] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:23.776] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:23.927] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:24.079] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:24.230] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:24.381] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:27.516] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:07:27.516] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:07:27.516] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/shenmeshi.wav
[13:07:27.684] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:07:27.684] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.7745804786682129秒
[13:07:27.684] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:07:27.686] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:07:27.686] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:07:27.686] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:27.687] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:07:27.687] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:27.807] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:27.838] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:27.928] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:27.989] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:28.049] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:28.141] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:28.170] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:28.290] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:28.291] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:28.442] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:07:28.492] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:07:28.493] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:14:48.324] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:14:48.324] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:14:48.324] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/shenmeshi.wav
[13:14:48.666] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:14:48.666] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.7745804786682129秒
[13:14:48.667] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:14:48.668] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:14:48.669] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:14:48.669] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:14:48.669] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:14:48.670] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:14:48.790] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:14:48.821] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:14:48.911] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:14:48.972] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:14:49.031] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:14:49.124] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:14:49.153] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:14:49.274] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:14:49.274] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:14:49.425] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:14:49.474] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:14:49.474] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:16:37.724] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:16:37.724] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:16:37.724] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/shenmeshi.wav
[13:16:37.893] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:16:37.893] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.7745804786682129秒
[13:16:37.893] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:16:37.894] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:16:37.894] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:16:37.894] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:16:37.895] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:16:37.895] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:16:38.017] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:16:38.047] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:16:38.138] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:16:38.198] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:16:38.258] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:16:38.349] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:16:38.379] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:16:38.500] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:16:38.500] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:16:38.651] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:16:38.701] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:16:38.701] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:17:24.617] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:17:24.617] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:17:24.617] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/shenmeshi.wav
[13:17:24.781] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:17:24.782] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.7745804786682129秒
[13:17:24.782] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:17:24.782] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:17:24.783] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:17:24.784] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:24.784] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:24.785] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:17:24.905] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:24.936] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:25.026] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:25.087] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:25.147] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:25.238] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:25.268] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:25.390] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:25.390] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:25.541] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:25.592] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:17:25.592] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:17:40.819] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:17:40.820] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:17:40.820] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/shenmeshi.wav
[13:17:40.985] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:17:40.987] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.7745804786682129秒
[13:17:40.987] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:17:40.987] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:17:40.988] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:17:40.988] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:40.988] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:17:40.988] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:41.110] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:41.140] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:41.231] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:41.291] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:41.352] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:41.442] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:41.473] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:41.594] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:41.594] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:41.745] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:17:41.794] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:17:41.794] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:18:24.467] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:18:24.467] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:18:24.467] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/shenmeshi.wav
[13:18:24.637] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:18:24.637] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.7745804786682129秒
[13:18:24.638] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:18:24.638] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:18:24.638] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:18:24.638] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:24.640] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:18:24.640] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:24.761] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:24.792] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:24.882] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:24.943] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:25.003] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:25.094] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:25.124] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:25.245] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:25.245] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:25.396] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:25.448] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:18:25.448] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:18:36.155] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:18:36.155] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:18:36.156] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/shenmeshi.wav
[13:18:36.331] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:18:36.331] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.7745804786682129秒
[13:18:36.332] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:18:36.332] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:18:36.333] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:18:36.333] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:36.334] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:18:36.334] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:36.454] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:36.485] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:36.575] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:36.636] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:36.696] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:36.787] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:36.817] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:36.937] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:36.937] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:37.089] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:37.140] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:18:37.140] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:18:47.848] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:18:47.848] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:18:47.848] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/shenmeshi.wav
[13:18:48.019] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:18:48.019] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.7745804786682129秒
[13:18:48.020] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:18:48.020] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:18:48.021] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:18:48.021] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:48.022] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:18:48.022] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:48.143] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:48.174] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:48.265] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:48.324] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:48.386] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:48.475] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:48.506] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:48.626] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:48.627] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:48.777] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:48.829] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:18:48.829] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:18:56.335] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:18:56.337] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:18:56.337] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/zaine.wav
[13:18:56.636] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:18:56.636] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.8769161105155945秒
[13:18:56.637] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:18:56.637] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:18:56.637] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:18:56.637] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:56.638] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:18:56.638] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:56.759] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:56.790] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:56.880] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:56.941] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:57.001] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:57.092] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:57.122] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:57.243] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:57.243] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:57.364] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:57.394] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:57.545] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:18:57.547] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:18:57.547] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:19:54.654] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:19:54.655] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:19:54.655] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/zaine.wav
[13:19:54.950] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:19:54.951] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.8769161105155945秒
[13:19:54.951] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:19:54.951] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:19:54.952] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:19:54.952] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:54.953] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:54.953] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:19:55.074] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:55.105] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:55.195] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:55.256] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:55.316] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:55.407] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:55.437] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:55.559] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:55.559] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:55.679] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:55.710] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:55.861] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:55.861] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:19:55.863] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:19:58.213] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:19:58.215] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:19:58.215] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/shenmeshi.wav
[13:19:58.383] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:19:58.383] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.7745804786682129秒
[13:19:58.384] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:19:58.384] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:19:58.385] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:19:58.385] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:58.385] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:19:58.387] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:58.507] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:58.538] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:58.627] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:58.689] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:58.749] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:58.840] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:58.869] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:58.990] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:58.991] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:59.142] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:19:59.191] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:19:59.191] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:24:09.907] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:24:09.908] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:24:09.908] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/shenmeshi.wav
[13:24:10.078] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:24:10.078] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.7745804786682129秒
[13:24:10.079] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:24:10.079] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:24:10.080] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:24:10.080] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:10.080] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:24:10.080] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:10.201] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:10.232] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:10.322] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:10.382] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:10.444] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:10.533] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:10.565] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:10.684] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:10.686] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:10.834] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:10.886] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:24:10.886] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:24:34.734] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:24:34.736] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:24:34.736] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/zaine.wav
[13:24:35.036] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:24:35.037] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.8769161105155945秒
[13:24:35.037] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:24:35.037] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:24:35.038] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:24:35.039] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:35.039] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:24:35.039] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:35.160] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:35.191] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:35.281] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:35.342] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:35.402] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:35.492] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:35.523] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:35.644] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:35.645] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:35.765] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:35.795] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:35.946] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:24:35.946] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:24:35.946] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:25:04.910] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:25:04.910] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:25:04.910] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/zaine.wav
[13:25:05.225] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:25:05.225] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.8769161105155945秒
[13:25:05.225] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:25:05.227] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:25:05.227] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:25:05.227] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:25:05.228] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:25:05.228] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:25:05.349] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:25:05.379] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:25:05.470] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:25:05.530] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:25:05.591] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:25:05.682] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:25:05.712] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:25:05.833] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:25:05.833] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:25:05.955] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:25:05.985] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:25:06.135] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:25:06.135] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:25:06.136] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:01.659] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:26:01.659] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:26:01.659] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/zaine.wav
[13:26:01.974] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:26:01.975] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.8769161105155945秒
[13:26:01.975] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:26:01.975] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:26:01.977] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:26:01.977] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:01.977] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:26:01.977] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:02.099] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:02.129] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:02.220] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:02.281] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:02.341] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:02.431] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:02.462] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:02.583] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:02.583] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:02.704] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:02.734] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:02.885] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:02.885] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:26:02.885] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:26:04.305] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:26:04.307] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:26:04.307] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/shenmeshi.wav
[13:26:04.472] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:26:04.472] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.7745804786682129秒
[13:26:04.474] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:26:04.474] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:26:04.474] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:26:04.475] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:04.475] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:26:04.475] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:04.597] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:04.627] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:04.718] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:04.778] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:04.838] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:04.929] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:04.960] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:05.079] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:05.080] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:05.231] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:26:05.281] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:26:05.281] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:29:02.070] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:29:02.071] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:29:02.071] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/shenmeshi.wav
[13:29:02.246] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:29:02.247] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.7745804786682129秒
[13:29:02.248] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:29:02.248] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:29:02.248] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:29:02.248] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:29:02.249] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:29:02.249] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:29:02.370] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:29:02.400] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:29:02.491] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:29:02.551] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:29:02.613] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:29:02.702] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:29:02.734] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:29:02.853] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:29:02.855] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:29:03.004] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:29:03.054] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:29:03.054] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:51:27.039] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:51:27.040] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:51:27.040] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/zaine.wav
[13:51:28.031] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:51:28.032] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.8769161105155945秒
[13:51:28.033] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:51:28.034] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:51:28.036] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:51:28.036] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:51:28.038] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:51:28.038] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:51:28.160] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:51:28.192] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:51:28.282] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:51:28.343] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:51:28.405] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:51:28.495] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:51:28.526] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:51:28.646] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:51:28.647] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:51:28.769] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:51:28.797] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:51:28.949] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:51:28.952] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:51:28.952] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:54:56.031] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:54:56.031] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:54:56.031] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/shenmeshi.wav
[13:54:56.305] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:54:56.305] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.7745804786682129秒
[13:54:56.307] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:54:56.307] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:54:56.308] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:54:56.308] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:54:56.314] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:54:56.316] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:54:56.434] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:54:56.468] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:54:56.556] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:54:56.620] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:54:56.678] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:54:56.771] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:54:56.799] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:54:56.922] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:54:56.924] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:54:57.075] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:54:57.122] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:54:57.123] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:57:55.046] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:57:55.047] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:57:55.047] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/zaine.wav
[13:57:55.662] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:57:55.664] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.8769161105155945秒
[13:57:55.664] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:57:55.665] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:57:55.667] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:57:55.667] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:57:55.668] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:57:55.668] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:57:55.789] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:57:55.820] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:57:55.911] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:57:55.972] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:57:56.033] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:57:56.123] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:57:56.155] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:57:56.275] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:57:56.276] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:57:56.397] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:57:56.427] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:57:56.579] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:57:56.580] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:57:56.581] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:58:31.771] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:58:31.773] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:58:31.774] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/zaine.wav
[13:58:32.452] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:58:32.453] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.8769161105155945秒
[13:58:32.455] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:58:32.456] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:58:32.460] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:58:32.460] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:58:32.463] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:58:32.463] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:58:32.584] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:58:32.616] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:58:32.706] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:58:32.767] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:58:32.828] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:58:32.919] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:58:32.949] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:58:33.071] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:58:33.071] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:58:33.194] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:58:33.223] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:58:33.374] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:58:33.375] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:58:33.377] INFO     | audio_action_controller.py:348 - 音频播放完成
[13:59:08.864] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[13:59:08.865] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[13:59:08.865] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/shenmeshi.wav
[13:59:09.196] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[13:59:09.197] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.7745804786682129秒
[13:59:09.199] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:59:09.199] INFO     | audio_action_controller.py:327 - 开始播放音频
[13:59:09.200] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[13:59:09.200] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:59:09.202] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[13:59:09.202] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:59:09.323] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:59:09.353] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:59:09.445] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:59:09.505] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:59:09.566] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:59:09.657] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:59:09.688] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:59:09.808] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:59:09.810] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:59:09.960] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[13:59:10.008] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[13:59:10.009] INFO     | audio_action_controller.py:348 - 音频播放完成
