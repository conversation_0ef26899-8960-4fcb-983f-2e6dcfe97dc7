#/bin/bash 

pactl list short sinks      # 列出输出设备
pactl list short sources    # 列出输入设备
pactl set-sink-volume @DEFAULT_SINK@ 120%     # 设置音量
pactl set-default-sink alsa_output.usb-C-Media_Electronics_Inc._USB_Audio_Device-00.analog-stereo    # 新设备
pactl set-default-sink alsa_output.usb-Yealink_CP900_804005H020000466-00.analog-stereo               # CP900

# arecord -D plughw:0,0 -f cd -t wav -d 10 output.wav
# aplay output.wav

# pactl set-default-sink alsa_output.usb-C-Media_Electronics_Inc._USB_Audio_Device-00.multichannel-output
# pactl set-default-source alsa_input.usb-iflytek_XFM-DP-V0.0.18_dc001425a2924922450-01.multichannel-input

pactl set-default-sink alsa_output.usb-C-Media_Electronics_Inc._USB_Audio_Device-00.analog-stereo.monitor
pactl set-default-source alsa_input.usb-iflytek_XFM-DP-V0.0.18_dc001425a2924922450-01.multichannel-input


python chat.py --det_log 2 --chat_log 2




python chat_tencent.py --chat_log 2 --sound_th 4000
python chat_jd.py --chat_log 2 --sound_th 4000




pulseaudio -k && pulseaudio --start

.ifexists module-echo-cancel.so
load-module module-echo-cancel aec_method=webrtc source_name=echocancel_CP900 sink_name=echocancel_CP900
set-default-source alsa_input.usb-Yealink_CP900_804005H020000466-00.analog-stereo
set-default-sink alsa_output.usb-Yealink_CP900_804005H020000466-00.iec958-stereo module-alsa-card.c
.endif

## 物理+虚拟
aplay -L
arecord -L


### 物理的
arecord -l
aplay -l



export PATH=$(echo $PATH | tr ':' '\n' | grep -v "$HOME/.local/bin" | tr '\n' ':' | sed 's/:$//')



fuser -v /dev/snd/*
lsof /dev/snd/*

Error in sys.excepthook

~/.asoundrc

# 测试动作
python motion_test.py

# 拉取视频
python robot_agent/robot_camera.py --save video.mp4


# 运行脚本无动作
python chat_with_robot.py --chat_log 0 --sound_th 1000 --kws local_streaming


# 运行脚本有动作
python chat_with_robot.py --chat_log 0 --sound_th 1000 --kws local_streaming --use_action True

# 测试视频
python robot_agent/robot_camera.py --save video.mp4



#source /home/<USER>/miniconda3/bin/activate /home/<USER>/HardwareAIAgent_web/tatata
#pip freeze > requirements.txt

###### 1. 配置环境 ######
### 从零开始配置环境 ####
conda create -n tatata_dev python=3.8
conda activate tatata_dev


which pip
#错误的位置：/home/<USER>/.local/bin/pip

# 正确的 /home/<USER>/miniconda3/envs/tatata_deploy/bin/pip
export PATH=$(echo $PATH | tr ':' '\n' | grep -v "$HOME/.local/bin" | tr '\n' ':' | sed 's/:$//')

sudo apt-get update
sudo apt-get install python3-dev
sudo apt-get install portaudio19-dev
sudo apt-get install python3-pyaudio
sudo apt-get install libasound-dev
sudo apt-get install libportaudio2
sudo apt-get install libportaudiocpp0
sudo apt-get install ffmpeg
sudo apt-get install libpulse-dev
sudo apt-get install build-essential


pip install pyaudio -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install numpy -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install torch -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install torchaudio -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install pygame -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install openai -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install sherpa_onnx -i https://pypi.tuna.tsinghua.edu.cn/simple




# 运行脚本无动作
python chat_with_robot.py --chat_log 0 --sound_th 1000 --kws local_streaming


###### 2. 音频输出设置 ######

#让系统的默认输出设备，为你想要的输出设备


###### 3. 音频输入设置 ######
# 找到系统的输入设备号，然后设置为你的输入设备


##打印出所有的输入设备
python others/pyaudio_test.py

# 在asr/asr.py 下面，选择自动设备的名字
#  def _select_input_device_auto(self) -> int:
#         """持续检测直到找到目标设备"""
#         #target_device_name = "4-mic Microphone: USB Audio"
#         target_device_name = ["mic","CP900: USB Audio", "spdif","dsnooper","speaker"]




###### 4. 测试 ######
python chat_with_robot.py --chat_log 0 --sound_th 1000 --kws local_streaming --use_action dont --echo_cancel True
python chat_with_robot.py --chat_log 0 --sound_th 1000 --kws local_streaming --echo_cancel True

python chat_with_robot.py --chat_log 0 --sound_th 1000 --kws local_streaming --use_action rm

python chat_with_robot.py --chat_log 0 --sound_th 1000 --kws local_streaming --use_action engineai --echo_cancel True

python chat_with_robot.py --chat_log 0 --sound_th 1000 --kws local_streaming --use_action lite3 --echo_cancel True --with_agent True



python chat_with_robot.py --chat_log 0 --sound_th 1000 --kws local_streaming --use_action True



#####################conda 环境打包#####################

# 打包
pip install conda-pack -i https://pypi.tuna.tsinghua.edu.cn/simple

conda pack -n tatata_dev -o tatata_dev.tar.gz

## 解压
# mkdir tatata_dev
# tar -xzvf /home/<USER>/HardwareAIAgent_deploy/tatata_dev.tar.gz -C tatata_dev
# tar -xzvf /home/<USER>/HardwareAIAgent_deploy/tatata_dev.tar.gz
tar -xzvf /home/<USER>/HardwareAIAgent_deploy_v4/tatata_dev.tar.gz

## 激活
# source tatata_dev/bin/activate

source /home/<USER>/miniconda3/bin/activate /home/<USER>/HardwareAIAgent_deploy_v4/tatata_dev

# 占用
sudo ss -tulnp | grep 43897
sudo kill -9 XXXX



git rm -r --cached .  #清除缓存
git add . #重新trace file
git commit -m "update .gitignore" #提交和注释
git push origin master #可选，如果需要同步到remote上的话



du -h --max-depth=1



# 录音
touch recordings/record_control.txt  # 开始
rm recordings/record_control.txt     # 结束
bash others/wav_check.sh             # 检查wav文件
python3 others/merge_recordings_by_day.py    # 合并录音

# 移植·交互检测
pactl list short sinks
pactl set-default-sink alsa_output.usb-Jieli_Technology_MAXHUB_BM20_Speaker_8403620037331230-00.analog-stereo
pactl set-sink-volume @DEFAULT_SINK@ 100% 
aplay /home/<USER>/Possessed_AI/asserts/zaine.wav