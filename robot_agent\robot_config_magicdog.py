"""
@Author: <EMAIL> <EMAIL>
@Create Date: 2025.03.28
@Description: 

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
"""
import os

from .fsm_config import *
LOG_FILE_PATH = f"{os.path.dirname(os.path.dirname(os.path.abspath(__file__)))}/log/log.txt"

# 机器人配置信息
ROBOT_CONFIG = {
    "ctl_ip": "*************",        # 运动主机IP地址
    "mon_ip": "*************",        # 感知主机IP地址
    "ctl_port": 43893,                # 运动主机接收感知主机UDP通讯端口
    "mon_port": 43897,                # 感知主机接收运动主机UDP通讯端口
    "local_port": 20001,          # 感知主机UDP端口--本地
    "heartbeat_interval": 0.1     # 心跳间隔(秒)
}

ROBOT_COMMANDS = {
    # -----------------------------------------------------------
    # 基本状态 / 模式切换
    # -----------------------------------------------------------
    "心跳":              {"code": 0x21040001, "param1": 0,       "param2": 0},
    "回零":              {"code": 0x21010C05, "param1": 0,       "param2": 0},
    "起立/趴下":        {"code": FSM_LOW_LEVEL_CTRL, "param1": 0,       "param2": 0},
    "起立":        {"code": FSM_RECOVERY_STAND, "param1": 0,       "param2": 0},
    "原地模式":         {"code": 0x21010D05, "param1": 0,       "param2": 0},
    "移动模式":         {"code": 0x21010D06, "param1": 0,       "param2": 0},
    "手动模式":         {"code": 0x21010C02, "param1": 0,       "param2": 0},
    "导航模式":         {"code": 0x21010C03, "param1": 0,       "param2": 0},  # 又称"自主模式"
    "软急停":           {"code": 0x21010C0E, "param1": 0,       "param2": 0},
    "保存数据":         {"code": 0x21010C01, "param1": 0,       "param2": 0},
    "原地踏步(开启)":   {"code": 0x21010C06, "param1": -1,      "param2": 0},  # 持续运动(开启)
    "原地踏步(关闭)":   {"code": 0x21010C06, "param1": 2,       "param2": 0},  # 持续运动(关闭)

    # -----------------------------------------------------------
    # 步态
    # -----------------------------------------------------------
    "低速":            {"code": FSM_TROT, "param1": 0,        "param2": 0},  # 平地低速步态
    "中速":            {"code": FSM_TROT, "param1": 0,        "param2": 0},  # 平地中速步态
    "高速":            {"code": FSM_TROT, "param1": 0,        "param2": 0},  # 平地高速步态
    "正常/匍匐":       {"code": 0x21010406, "param1": 0,        "param2": 0},
    "抓地":            {"code": 0x21010402, "param1": 0,        "param2": 0},  # 抓地越障步态
    "越障":            {"code": 0x21010401, "param1": 0,        "param2": 0},  # 通用越障步态
    "高踏步":          {"code": 0x21010407, "param1": 0,        "param2": 0},  # 高踏步越障步态

    # -----------------------------------------------------------
    # 动作
    # -----------------------------------------------------------
    "扭身体":          {"code": FSM_SHAKE_BODY, "param1": 0,        "param2": 0},
    "翻身":            {"code": FSM_WIGGLE_HIP, "param1": 0,        "param2": 0},
    #"太空步":          {"code": 0x2101030C, "param1": 0,        "param2": 0},
    "后空翻":          {"code": FSM_BACK_FLIP, "param1": 0,        "param2": 0},
    "打招呼":          {"code": FSM_SHAKE_LEFT_HAND, "duration":6,"param1": 0,        "param2": 0},
    "向前跳":          {"code": FSM_JUMP_FRONT, "param1": 0,        "param2": 0},
    "扭身跳":          {"code": FSM_SPIN_JUMP_LEFT, "param1": 0,        "param2": 0},

    # -----------------------------------------------------------
    # 轴指令 (原地模式) —— 二十三档幅度示例
    #   以 doc 为准: code=0x210101xx, 取值范围约 [-32767, 32767]
    #   实际死区或有效范围可参考文档细节
    # -----------------------------------------------------------
    ############ 横滚角 ############
    # 向左的档位（从小到大）
    "调整横滚角(原地向左)-幅度1.0": {"code": 0x21010131, "param1": -12553, "param2": 0},  # 最小有效值
    "调整横滚角(原地向左)-幅度2.0": {"code": 0x21010131, "param1": -14000, "param2": 0},
    "调整横滚角(原地向左)-幅度3.0": {"code": 0x21010131, "param1": -16000, "param2": 0},
    "调整横滚角(原地向左)-幅度4.0": {"code": 0x21010131, "param1": -18000, "param2": 0},
    "调整横滚角(原地向左)-幅度5.0": {"code": 0x21010131, "param1": -20000, "param2": 0},
    "调整横滚角(原地向左)-幅度6.0": {"code": 0x21010131, "param1": -22000, "param2": 0},
    "调整横滚角(原地向左)-幅度7.0": {"code": 0x21010131, "param1": -24000, "param2": 0},
    "调整横滚角(原地向左)-幅度8.0": {"code": 0x21010131, "param1": -26000, "param2": 0},
    "调整横滚角(原地向左)-幅度9.0": {"code": 0x21010131, "param1": -28000, "param2": 0},
    "调整横滚角(原地向左)-幅度10.0": {"code": 0x21010131, "param1": -30000, "param2": 0},
    "调整横滚角(原地向左)-幅度11.0": {"code": 0x21010131, "param1": -32767, "param2": 0},  # 最大有效值

    # 中间位置
    "调整横滚角(原地)-幅度0.0": {"code": 0x21010131, "param1": 0, "param2": 0},

    # 向右的档位（从小到大）
    "调整横滚角(原地向右)-幅度1.0": {"code": 0x21010131, "param1": 12553, "param2": 0},  # 最小有效值
    "调整横滚角(原地向右)-幅度2.0": {"code": 0x21010131, "param1": 14000, "param2": 0},
    "调整横滚角(原地向右)-幅度3.0": {"code": 0x21010131, "param1": 16000, "param2": 0},
    "调整横滚角(原地向右)-幅度4.0": {"code": 0x21010131, "param1": 18000, "param2": 0},
    "调整横滚角(原地向右)-幅度5.0": {"code": 0x21010131, "param1": 20000, "param2": 0},
    "调整横滚角(原地向右)-幅度6.0": {"code": 0x21010131, "param1": 22000, "param2": 0},
    "调整横滚角(原地向右)-幅度7.0": {"code": 0x21010131, "param1": 24000, "param2": 0},
    "调整横滚角(原地向右)-幅度8.0": {"code": 0x21010131, "param1": 26000, "param2": 0},
    "调整横滚角(原地向右)-幅度9.0": {"code": 0x21010131, "param1": 28000, "param2": 0},
    "调整横滚角(原地向右)-幅度10.0": {"code": 0x21010131, "param1": 30000, "param2": 0},
    "调整横滚角(原地向右)-幅度11.0": {"code": 0x21010131, "param1": 32767, "param2": 0},  # 最大有效值

    ############ 俯仰角 ############
    # 抬头 == controller.23
    "调整俯仰角(原地)-高幅度": {
        "code":   0x21010130,
        "param1": -32767,
        "param2": 0
    },
    # 向上的档位（从小到大）
    "调整俯仰角(原地抬头)-幅度1.0": {"code": 0x21010130, "param1": -6553, "param2": 0},  # 最小有效值
    "调整俯仰角(原地抬头)-幅度2.0": {"code": 0x21010130, "param1": -9220, "param2": 0},
    "调整俯仰角(原地抬头)-幅度3.0": {"code": 0x21010130, "param1": -11840, "param2": 0},
    "调整俯仰角(原地抬头)-幅度4.0": {"code": 0x21010130, "param1": -14460, "param2": 0},
    "调整俯仰角(原地抬头)-幅度5.0": {"code": 0x21010130, "param1": -17080, "param2": 0},
    "调整俯仰角(原地抬头)-幅度6.0": {"code": 0x21010130, "param1": -19700, "param2": 0},
    "调整俯仰角(原地抬头)-幅度7.0": {"code": 0x21010130, "param1": -22320, "param2": 0},
    "调整俯仰角(原地抬头)-幅度8.0": {"code": 0x21010130, "param1": -24940, "param2": 0},
    "调整俯仰角(原地抬头)-幅度9.0": {"code": 0x21010130, "param1": -27560, "param2": 0},
    "调整俯仰角(原地抬头)-幅度10.0": {"code": 0x21010130, "param1": -30180, "param2": 0},
    "调整俯仰角(原地抬头)-幅度11.0": {"code": 0x21010130, "param1": -32767, "param2": 0},  # 最大有效值

    # 中间位置
    "调整俯仰角(原地)-幅度0.0": {"code": 0x21010130, "param1": 0, "param2": 0},

    # 向下的档位（从小到大）
    "调整俯仰角(原地低头)-幅度1.0": {"code": 0x21010130, "param1": 6553, "param2": 0},  # 最小有效值
    "调整俯仰角(原地低头)-幅度2.0": {"code": 0x21010130, "param1": 9220, "param2": 0},
    "调整俯仰角(原地低头)-幅度3.0": {"code": 0x21010130, "param1": 11840, "param2": 0},
    "调整俯仰角(原地低头)-幅度4.0": {"code": 0x21010130, "param1": 14460, "param2": 0},
    "调整俯仰角(原地低头)-幅度5.0": {"code": 0x21010130, "param1": 17080, "param2": 0},
    "调整俯仰角(原地低头)-幅度6.0": {"code": 0x21010130, "param1": 19700, "param2": 0},
    "调整俯仰角(原地低头)-幅度7.0": {"code": 0x21010130, "param1": 22320, "param2": 0},
    "调整俯仰角(原地低头)-幅度8.0": {"code": 0x21010130, "param1": 24940, "param2": 0},
    "调整俯仰角(原地低头)-幅度9.0": {"code": 0x21010130, "param1": 27560, "param2": 0},
    "调整俯仰角(原地低头)-幅度10.0": {"code": 0x21010130, "param1": 27560, "param2": 0},
    "调整俯仰角(原地低头)-幅度11.0": {"code": 0x21010130, "param1": 32767, "param2": 0},  # 最大有效值

    ############ 身体高度 ############
    # 向上的档位（从小到大）
    "调整身体高度(原地降低)-幅度1.0": {"code": 0x21010102, "param1": -20000, "param2": 0},  # 最小有效值
    "调整身体高度(原地降低)-幅度2.0": {"code": 0x21010102, "param1": -21250, "param2": 0},
    "调整身体高度(原地降低)-幅度3.0": {"code": 0x21010102, "param1": -22500, "param2": 0},
    "调整身体高度(原地降低)-幅度4.0": {"code": 0x21010102, "param1": -23750, "param2": 0},
    "调整身体高度(原地降低)-幅度5.0": {"code": 0x21010102, "param1": -25000, "param2": 0},
    "调整身体高度(原地降低)-幅度6.0": {"code": 0x21010102, "param1": -26250, "param2": 0},
    "调整身体高度(原地降低)-幅度7.0": {"code": 0x21010102, "param1": -27500, "param2": 0},
    "调整身体高度(原地降低)-幅度8.0": {"code": 0x21010102, "param1": -28750, "param2": 0},
    "调整身体高度(原地降低)-幅度9.0": {"code": 0x21010102, "param1": -30000, "param2": 0},
    "调整身体高度(原地降低)-幅度10.0": {"code": 0x21010102, "param1": -31250, "param2": 0},
    "调整身体高度(原地降低)-幅度11.0": {"code": 0x21010102, "param1": -32767, "param2": 0},  # 最大有效值

    # 中间位置
    "调整身体高度(原地)-幅度0.0": {"code": 0x21010102, "param1": 0, "param2": 0},

    # 向下的档位（从小到大）
    "调整身体高度(原地抬高)-幅度1.0": {"code": 0x21010102, "param1": 20000, "param2": 0},  # 最小有效值
    "调整身体高度(原地抬高)-幅度2.0": {"code": 0x21010102, "param1": 21250, "param2": 0},
    "调整身体高度(原地抬高)-幅度3.0": {"code": 0x21010102, "param1": 22500, "param2": 0},
    "调整身体高度(原地抬高)-幅度4.0": {"code": 0x21010102, "param1": 23750, "param2": 0},
    "调整身体高度(原地抬高)-幅度5.0": {"code": 0x21010102, "param1": 25000, "param2": 0},
    "调整身体高度(原地抬高)-幅度6.0": {"code": 0x21010102, "param1": 26250, "param2": 0},
    "调整身体高度(原地抬高)-幅度7.0": {"code": 0x21010102, "param1": 27500, "param2": 0},
    "调整身体高度(原地抬高)-幅度8.0": {"code": 0x21010102, "param1": 28750, "param2": 0},
    "调整身体高度(原地抬高)-幅度9.0": {"code": 0x21010102, "param1": 30000, "param2": 0},
    "调整身体高度(原地抬高)-幅度10.0": {"code": 0x21010102, "param1": 31250, "param2": 0},
    "调整身体高度(原地抬高)-幅度11.0": {"code": 0x21010102, "param1": 32767, "param2": 0},  # 最大有效值

    ############ 偏航角 ############
    # 向左的档位（从小到大）
    "调整偏航角(原地向左)-幅度1.0": {"code": 0x21010135, "param1": -9553, "param2": 0},  # 最小有效值
    "调整偏航角(原地向左)-幅度2.0": {"code": 0x21010135, "param1": -11870, "param2": 0},
    "调整偏航角(原地向左)-幅度3.0": {"code": 0x21010135, "param1": -14191, "param2": 0},
    "调整偏航角(原地向左)-幅度4.0": {"code": 0x21010135, "param1": -16510, "param2": 0},
    "调整偏航角(原地向左)-幅度5.0": {"code": 0x21010135, "param1": -18830, "param2": 0},
    "调整偏航角(原地向左)-幅度6.0": {"code": 0x21010135, "param1": -21150, "param2": 0},
    "调整偏航角(原地向左)-幅度7.0": {"code": 0x21010135, "param1": -23470, "param2": 0},
    "调整偏航角(原地向左)-幅度8.0": {"code": 0x21010135, "param1": -25790, "param2": 0},
    "调整偏航角(原地向左)-幅度9.0": {"code": 0x21010135, "param1": -28110, "param2": 0},
    "调整偏航角(原地向左)-幅度10.0": {"code": 0x21010135, "param1": -30430, "param2": 0},
    "调整偏航角(原地向左)-幅度11.0": {"code": 0x21010135, "param1": -32767, "param2": 0},  # 最大有效值

    # 中间位置
    "调整偏航角(原地)-幅度0.0": {"code": 0x21010135, "param1": 0, "param2": 0},

    # 向右的档位（从小到大）
    "调整偏航角(原地向右)-幅度1.0": {"code": 0x21010135, "param1": 9553, "param2": 0},  # 最小有效值
    "调整偏航角(原地向右)-幅度2.0": {"code": 0x21010135, "param1": 11870, "param2": 0},
    "调整偏航角(原地向右)-幅度3.0": {"code": 0x21010135, "param1": 14191, "param2": 0},
    "调整偏航角(原地向右)-幅度4.0": {"code": 0x21010135, "param1": 16510, "param2": 0},
    "调整偏航角(原地向右)-幅度5.0": {"code": 0x21010135, "param1": 18830, "param2": 0},
    "调整偏航角(原地向右)-幅度6.0": {"code": 0x21010135, "param1": 21150, "param2": 0},
    "调整偏航角(原地向右)-幅度7.0": {"code": 0x21010135, "param1": 23470, "param2": 0},
    "调整偏航角(原地向右)-幅度8.0": {"code": 0x21010135, "param1": 25790, "param2": 0},
    "调整偏航角(原地向右)-幅度9.0": {"code": 0x21010135, "param1": 28110, "param2": 0},
    "调整偏航角(原地向右)-幅度10.0": {"code": 0x21010135, "param1": 30430, "param2": 0},
    "调整偏航角(原地向右)-幅度11.0": {"code": 0x21010135, "param1": 32767, "param2": 0},  # 最大有效值

    # -----------------------------------------------------------
    # 轴指令 (移动模式) —— 三档速度示例
    #   code=0x210101xx, 取值范围约 [-32767, 32767]
    #   正值: 前进 / 右平移 / 右转   负值: 后退 / 左平移 / 左转
    # -----------------------------------------------------------
    "前进(低速度)":    {"code": 4, "duration":0.5,"param0": 0.05, "param1": 0,  "param2": 0},
    "前进(中速度)":    {"code": 4,"duration":0.5, "param0": 0.1, "param1": 0,  "param2": 0},
    "前进(高速度)":    {"code": 4,"duration":0.5, "param0": 0.2,  "param1": 0,  "param2": 0},

    "后退(低速度)":    {"code": 4,"duration":0.5, "param0": -0.05, "param1": 0,  "param2": 0},
    "后退(中速度)":    {"code": 4,"duration":0.5, "param0": -0.1, "param1": 0,  "param2": 0},
    "后退(高速度)":    {"code": 4,"duration":0.5, "param0": -0.2, "param1": 0,  "param2": 0},

    "左平移(低速度)":  {"code": 4, "duration":0.3,"param0": 0.0,"param1": 0.1, "param2": 0.0},
    "左平移(中速度)":  {"code": 4, "duration":0.3,"param0": 0.0,"param1": 0.2, "param2": 0.0},
    "左平移(高速度)":  {"code": 4, "duration":0.3,"param0": 0.0,"param1": 0.3, "param2": 0.0},

    "右平移(低速度)":  {"code": 4,"duration":0.3, "param0": 0.0,"param1": -0.1,   "param2": 0.0},
    "右平移(中速度)":  {"code": 4, "duration":0.3,"param0": 0.0,"param1": -0.2,  "param2": 0.0},
    "右平移(高速度)":  {"code": 4, "duration":0.3,"param0": 0.0,"param1": -0.3,  "param2": 0.0},

    "左转(低速度)":    {"code": 4, "duration":0.3,"param2": 0.1,  "param1": None},
    "左转(中速度)":    {"code": 4, "duration":0.3,"param2": 0.2, "param1": None},
    "左转(高速度)":    {"code": 4, "duration":0.3,"param2": 0.3, "param1": None},

    "右转(低速度)":    {"code": 4, "duration":0.3,"param2": -0.1,   "param1": None},
    "右转(中速度)":    {"code": 4,"duration":0.3, "param2": -0.3,  "param1": None},
    "右转(高速度)":    {"code": 4,"duration":0.3, "param2": -0.4,  "param1": None},

    # 停止
    "停止移动":       {"code": 4,"duration":0.2,"param0": 0, "param1": 0,       "param2": 0},
    "停止平移":       {"code": 4,"duration":0.2,"param0": 0, "param1": 0,       "param2": 0},
    "停止转向":       {"code": 4,"duration":0.2,"param0": 0, "param1": 0,       "param2": 0},
    "停止编排动作":   {"code": 4, "duration":0.2,"param0": 0, "param1": 0,       "param2": 0},

    # -----------------------------------------------------------
    # 扬声器指令 (不属于语音指令, 仅控制扬声器)
    # -----------------------------------------------------------
    "扬声器(关闭)":  {"code": 0x2101030D, "param1": 0, "param2": 0},
    "扬声器(打开)":  {"code": 0x2101030D, "param1": 1, "param2": 0},
    "扬声器(查询)": {"code": 0x2101030D, "param1": 2, "param2": 0},

    # -----------------------------------------------------------
    # 感知设置类指令
    # -----------------------------------------------------------
    "关闭所有AI选项": {"code": 0x21012109, "param1": 0x00, "param2": 0},
    "开启停障":       {"code": 0x21012109, "param1": 0x20, "param2": 0},
    "开启跟随":       {"code": 0x21012109, "param1": 0xC0, "param2": 0},
    "开启导航避障":   {"code": 0x21012109, "param1": 0x40, "param2": 0},

    # -----------------------------------------------------------
    # 速度指令 (复杂指令, param2=1; 需携带双精度值)
    # 以下仅示例三档 param1
    # -----------------------------------------------------------
    "速度指令-旋转(低)": {
        "code":   0x0141,
        "param1": "0.5",    # 范围 -1.5 ~ 1.5 (rad/s)
        "param2": 1
    },
    "速度指令-旋转(中)": {
        "code":   0x0141,
        "param1": "1.0",
        "param2": 1
    },
    "速度指令-旋转(高)": {
        "code":   0x0141,
        "param1": "1.5",
        "param2": 1
    },

    "速度指令-前后(低)": {
        "code":   0x0140,
        "param1": "0.3",    # 范围 -1.0 ~ 1.0 (m/s)
        "param2": 1
    },
    "速度指令-前后(中)": {
        "code":   0x0140,
        "param1": "0.6",
        "param2": 1
    },
    "速度指令-前后(高)": {
        "code":   0x0140,
        "param1": "1.0",
        "param2": 1
    },

    "速度指令-左右(低)": {
        "code":   0x0145,
        "param1": "0.2",    # 范围 -0.5 ~ 0.5 (m/s)
        "param2": 1
    },
    "速度指令-左右(中)": {
        "code":   0x0145,
        "param1": "0.4",
        "param2": 1
    },
    "速度指令-左右(高)": {
        "code":   0x0145,
        "param1": "0.5",
        "param2": 1
    },

    # -----------------------------------------------------------
    # 查询指令
    # -----------------------------------------------------------
    "状态查询": {"code": 0x0901, "param1": 0, "param2": 1}


}

# 机器人状态映射字典
ROBOT_STATES = {
    (1, 0, 0): "趴下状态",
    (1, 0, 11): "正在执行向前跳",
    (4, 0, 0): "准备起立状态",
    (5, 0, 0): "正在起立状态",
    (6, 0, 0): "力控状态（静止站立）且步态为平地低速步态",
    (6, 0, 1): "正在以平地低速步态踏步或正在根据轴指令扭动身体",
    (6, 0, 2): "正在执行扭身体",
    (6, 0, 4): "正在执行扭身跳",
    (6, 2, 0): "力控状态（静止站立）且步态为通用越障步态",
    (6, 2, 1): "正在以通用越障步态踏步",
    (6, 4, 0): "力控状态（静止站立）且步态为平地中速步态",
    (6, 4, 1): "正在以平地中速步态踏步",
    (6, 5, 0): "力控状态（静止站立）且步态为平地高速步态",
    (6, 5, 1): "正在以平地高速步态踏步",
    (6, 6, 0): "力控状态（静止站立）且步态为抓地越障步态",
    (6, 6, 1): "正在以抓地越障步态踏步",
    (6, 12, 1): "正在执行太空步",
    (6, 13, 0): "力控状态（静止站立）且步态为高踏步越障步态",
    (6, 13, 1): "正在以高踏步越障步态踏步",
    (7, 0, 0): "正在趴下状态",
    (8, 0, 0): "失控保护状态",
    (9, 0, 0): "姿态调整状态",
    (11, 0, 0): "正在执行翻身",
    (17, 0, 0): "回零状态",
    (18, 0, 0): "正在执行后空翻",
    (20, 0, 0): "正在执行打招呼"
}

def get_robot_state(basic_state: int, gait_state: int, motion_state: int) -> str:
    """
    获取机器狗当前状态描述
    """
    state_tuple = (basic_state, gait_state, motion_state)
    return ROBOT_STATES.get(state_tuple, "未知状态")