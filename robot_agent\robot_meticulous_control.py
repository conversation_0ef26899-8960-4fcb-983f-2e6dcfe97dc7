import time
import math
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from util.logger import logger

class RobotMeticulousController:
    def __init__(self, robot_controller):
        """
        精细控制器初始化
        
        参数:
        -----
        robot_controller : RobotController
            机器人控制器实例
        """
        self.robot_controller = robot_controller
        self.commander = robot_controller.commander
        
        # 各轴的最小有效阈值
        self.min_thresholds = {
            'roll': 12553,    # 横滚角最小有效值
            'pitch': 6553,    # 俯仰角最小有效值
            'height': 20000,  # 身体高度最小有效值
            'yaw': 9553       # 偏航角最小有效值
        }
        
        # 轴指令代码
        self.axis_codes = {
            'roll': 0x21010131,    # 横滚角
            'pitch': 0x21010130,   # 俯仰角
            'height': 0x21010102,  # 身体高度
            'yaw': 0x21010135      # 偏航角
        }

        # self.stop_flag = False

    @property
    def check_stop(self):
        """检查是否需要停止当前动作"""
        return self.robot_controller.stop_flag
    
    def send_axis_command(self, axis_type, amplitude, wait_time=0.05):
        """
        发送轴指令，带自定义幅度控制，确保幅度在有效范围内
        
        参数:
        -----
        axis_type : str
            轴类型: 'roll'(横滚角), 'pitch'(俯仰角), 'height'(身体高度), 'yaw'(偏航角)
        amplitude : int
            命令幅度 (-32767 到 32767)
        wait_time : float
            命令后等待时间(秒)
        """
        code = self.axis_codes.get(axis_type.lower())
        if code is None:
            logger.error(f"无效的轴类型: {axis_type}")
            return
            
        # 获取该轴的最小有效阈值
        threshold = self.min_thresholds.get(axis_type.lower(), 0)
        
        # 确保幅度在有效范围内 (处理死区)
        if abs(amplitude) < threshold:
            # 如果幅度小于最小有效值，且非零，则使用最小有效值
            if amplitude != 0:
                amplitude = threshold if amplitude > 0 else -threshold
    
        # 确保幅度在最大范围内
        amplitude = max(-32767, min(32767, int(amplitude)))
        
        # 发送指令给机器人 - 使用 send_command
        self.commander.send_command(code=code, param1=amplitude, param2=0)
        
        # 等待指定时间
        if wait_time > 0 and not self.check_stop:
            time.sleep(wait_time)

    def generate_smooth_transition(self, axis_type, start_amp, end_amp, steps, duration=1.0):
        """
        生成从起始幅度到结束幅度的平滑过渡
        
        参数:
        -----
        axis_type : str
            轴类型: 'roll', 'pitch', 'height', 或 'yaw'
        start_amp : int
            起始幅度
        end_amp : int
            结束幅度
        steps : int
            步数
        duration : float
            过渡总时长(秒)
        """
        if self.check_stop:
            return
            
        wait_per_step = duration / steps
        amp_step = (end_amp - start_amp) / (steps - 1) if steps > 1 else 0
        
        for i in range(steps):
            if self.check_stop:
                return
            amp = start_amp + (i * amp_step)
            self.send_axis_command(axis_type, amp, wait_per_step)

    def generate_sine_wave(self, axis_type, amplitude, duration, frequency=1.0, steps=None):
        """
        生成正弦波动作模式
        
        参数:
        -----
        axis_type : str
            轴类型: 'roll', 'pitch', 'height', 或 'yaw'
        amplitude : int
            最大幅度(正弦波峰值)
        duration : float
            总时长(秒)
        frequency : float
            频率(Hz，每秒周期数)
        steps : int
            生成的点数(默认: 20 * duration)
        """
        if self.check_stop:
            return
            
        if steps is None:
            steps = int(20 * duration)  # 默认每秒20个命令以确保平滑
            
        wait_per_step = duration / steps
        
        # 获取该轴的最小有效阈值
        threshold = self.min_thresholds.get(axis_type.lower(), 0)
        
        # 确保振幅至少是最小有效阈值
        if amplitude < threshold:
            amplitude = threshold
        
        for i in range(steps):
            if self.check_stop:
                return
            t = i * wait_per_step
            # 生成正弦值: amplitude * sin(2π * frequency * time)
            raw_value = amplitude * math.sin(2 * math.pi * frequency * t)
            
            # 处理死区问题：
            # 1. 如果原始值绝对值小于阈值但不为零，则使用阈值保持连续性
            # 2. 如果原始值为零或接近零，则保持为零以允许平滑过零点
            if 0 < abs(raw_value) < threshold:
                value = threshold if raw_value > 0 else -threshold
            else:
                value = raw_value
                
            self.send_axis_command(axis_type, value, wait_per_step)

    def generate_multiple_sine_waves(self, duration, **axis_configs):
        """
        同时执行多个轴的正弦波运动
        
        参数:
        -----
        duration : float
            总持续时间(秒)
        **axis_configs : dict
            每个轴的配置，格式为 axis_name=(amplitude, frequency)
            例如: roll=(32767, 0.5), pitch=(32767, 1.0)
        """
        steps = int(20 * duration)  # 默认每秒20个命令点
        wait_per_step = duration / steps
        
        # 获取各轴的阈值
        thresholds = {axis: self.min_thresholds.get(axis.lower(), 0) 
                    for axis in axis_configs.keys()}
        
        for i in range(steps):
            if self.check_stop:
                return
                
            t = i * wait_per_step
            
            # 为每个轴计算并发送命令
            for axis, (amplitude, frequency) in axis_configs.items():
                threshold = thresholds[axis]
                
                # 计算正弦值
                raw_value = amplitude * math.sin(2 * math.pi * frequency * t)
                
                # 处理死区
                if 0 < abs(raw_value) < threshold:
                    value = threshold if raw_value > 0 else -threshold
                else:
                    value = raw_value
                    
                # 发送命令
                self.send_axis_command(axis, value, 0.01)
            
            # 统一等待一个步长的时间
            time.sleep(max(0.01, wait_per_step - 0.01 * len(axis_configs)))

    ### 舞蹈编排
    def dance_default(self):
        """执行默认舞蹈序列"""
        # 横滚角摇摆(左右摆动) 
        self.generate_sine_wave('roll', 25000, 3.0, 0.5)
        if self.check_stop:
            return
        
        # 俯仰角摇摆(前后摆头)
        self.generate_sine_wave('pitch', 22000, 2.5, 0.6)
        if self.check_stop:
            return
        
        # 身体高度变化(上下起伏)
        self.generate_sine_wave('height', 25000, 3.0, 0.5)
        if self.check_stop:
            return
        
        # 偏航角旋转(先加速后减速)
        self.generate_smooth_transition('yaw', 10000, 30000, 20, 2.0)
        if self.check_stop:
            return
        self.generate_smooth_transition('yaw', 30000, -30000, 40, 4.0)
        if self.check_stop:
            return
        self.generate_smooth_transition('yaw', -30000, 0, 20, 2.0)

    def dance_spin(self):
        """执行旋转舞蹈"""
        # 渐进旋转到最大速度
        self.generate_smooth_transition('yaw', 10000, 30000, 20, 2.0)
        if self.check_stop:
            return
        
        # 在最大速度保持3秒
        for _ in range(30):
            if self.check_stop:
                return
            self.send_axis_command('yaw', 30000, 0.1)
        
        # 渐进减速
        self.generate_smooth_transition('yaw', 30000, 10000, 15, 1.5)
        if self.check_stop:
            return
        self.send_axis_command('yaw', 0, 0.5)  # 最后一步直接归零

    def dance_wave(self):
        """执行波浪舞蹈"""
        # 重复3次
        for _ in range(3):
            if self.check_stop:
                return
            # 横滚角波浪
            self.generate_sine_wave('roll', 25000, 2.0, 0.5)
            if self.check_stop:
                return
            # 俯仰角波浪
            self.generate_sine_wave('pitch', 20000, 2.0, 0.5)

    def dance_combined(self):
        """执行复合舞蹈动作 - 同时控制多个轴"""
        # 复合动作组合（同时控制多轴）
        total_time = 5.0  # 5秒
        steps = 50
        wait_time = total_time / steps
        
        # 各轴的最小有效阈值
        roll_threshold = self.min_thresholds['roll']
        pitch_threshold = self.min_thresholds['pitch']
        yaw_threshold = self.min_thresholds['yaw']
        
        for i in range(steps):
            if self.check_stop:
                return
                
            t = i * wait_time
            
            # 横滚角运动(左右) - 基础振幅确保高于死区
            roll_base = 20000
            roll_value = roll_base * math.sin(2 * math.pi * 0.4 * t)
            # 处理死区
            if -roll_threshold < roll_value < 0:
                roll_value = -roll_threshold
            elif 0 < roll_value < roll_threshold:
                roll_value = roll_threshold
            
            # 俯仰角运动(前后)
            pitch_base = 15000
            pitch_value = pitch_base * math.sin(2 * math.pi * 0.6 * t + math.pi/2)
            # 处理死区
            if -pitch_threshold < pitch_value < 0:
                pitch_value = -pitch_threshold
            elif 0 < pitch_value < pitch_threshold:
                pitch_value = pitch_threshold
            
            # 偏航角运动(旋转)
            yaw_base = 15000
            yaw_value = yaw_base * math.sin(2 * math.pi * 0.2 * t)
            # 处理死区
            if -yaw_threshold < yaw_value < 0:
                yaw_value = -yaw_threshold
            elif 0 < yaw_value < yaw_threshold:
                yaw_value = yaw_threshold
            
            # 发送命令
            self.send_axis_command('roll', roll_value, 0.01)
            self.send_axis_command('pitch', pitch_value, 0.01)
            self.send_axis_command('yaw', yaw_value, 0.01)
            
            # 短暂等待以确保命令发送
            if self.check_stop:
                return
            time.sleep(0.01)
    
    def dance_with_music(self):
        """"执行随音乐舞蹈"""
        # self.generate_sine_wave('roll', 32767, 2, 1)  # 测试
        # self.generate_sine_wave('height', 32767, 2, 1)  # 起伏
        self.generate_sine_wave('height', 32767, 1.5, 0.5)  # 狂犬病
        self.dance_combined()
        self.dance_combined()
        self.dance_combined()
        self.generate_sine_wave('pitch', 32767, 4, 0.5)
        self.generate_sine_wave('roll', 32767, 2, 0.5)
        self.generate_sine_wave('roll', 32767, 2, 1)
        self.generate_sine_wave('height', 32767, 0.5, 0.5)  # 狂犬疫苗
        time.sleep(0.5)
        self.generate_sine_wave('pitch', 32767, 3, 2)
        self.generate_multiple_sine_waves(4, 
                                          roll=(32767, 0.25),
                                          pitch=(32767, 0.5)
                                          )

    ### 执行
    def execute_dance(self, dance_style="default"):
        """
        执行指定风格的舞蹈
        
        参数:
        -----
        dance_style : str
            舞蹈风格: "default", "spin", "wave", "combined", "with_music"
        """
        # 确保我们在原地模式
        self.commander.run("原地模式")
        if self.check_stop:
            return
        time.sleep(0.1)  # 等待模式切换完成
        
        # 根据舞蹈风格选择不同的舞蹈动作
        if dance_style == "spin":
            self.dance_spin()
        elif dance_style == "wave":
            self.dance_wave()
        elif dance_style == "combined":
            self.dance_combined()
        elif dance_style == "with_music":
            self.dance_with_music()
        else:
            # 默认舞蹈
            self.dance_default()
        
        # 结束舞蹈回到正常姿态
        self.commander.run("调整横滚角(原地)-幅度0.0")
        self.commander.run("调整俯仰角(原地)-幅度0.0")
        self.commander.run("调整偏航角(原地)-幅度0.0")
        self.commander.run("调整身体高度(原地)-幅度0.0")
        
        time.sleep(0.5)