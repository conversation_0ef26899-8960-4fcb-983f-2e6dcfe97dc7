[14:15:12.287] INFO     | audio_action_controller.py:136 - Modbus 功能已禁用（pymodbus 未安装）
[14:15:14.545] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[14:15:14.545] INFO     | audio_action_controller.py:70 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[14:15:14.546] INFO     | audio_action_controller.py:292 - 正在加载音频: ./asserts/ding.wav
[14:15:14.546] INFO     | audio_action_controller.py:42 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[14:15:14.546] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 0.9675963521003723秒
[14:15:14.546] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:15:14.546] INFO     | audio_action_controller.py:327 - 开始播放音频
[14:15:14.548] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[14:15:14.548] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:14.548] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[14:15:14.548] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:14.669] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:14.699] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:14.789] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:14.850] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:14.910] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:15.000] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:15.031] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:15.151] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:15.152] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:15.273] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:15.302] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:15.394] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:15.453] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:15.554] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:15:15.554] INFO     | audio_action_controller.py:348 - 音频播放完成
[14:15:15.604] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:15.605] INFO     | audio_action_controller.py:97 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[14:15:15.605] INFO     | audio_action_controller.py:67 - 检测到MP3文件，已应用针对压缩音频的优化设置
[14:15:15.605] INFO     | audio_action_controller.py:292 - 正在加载音频: asserts/tts/dog_ok.mp3
[14:15:15.610] INFO     | audio_action_controller.py:42 - 音频音量设置为: 85.0% (针对WAV无损格式优化)
[14:15:15.610] INFO     | audio_action_controller.py:305 - 音频长度(从文件获取): 4.439773082733154秒
[14:15:15.611] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:15:15.611] INFO     | audio_action_controller.py:327 - 开始播放音频
[14:15:15.612] INFO     | audio_action_controller.py:332 - 嘴部动作控制线程已启动
[14:15:15.612] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:15.612] INFO     | audio_action_controller.py:335 - 脖子动作控制线程已启动
[14:15:15.612] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:15.733] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:15.764] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:15.854] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:15.914] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:15.975] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:16.065] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:16.096] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:16.216] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:16.217] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:16.337] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:16.367] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:16.458] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:16.518] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:16.579] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:16.669] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:16.699] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:16.820] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:16.820] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:16.941] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:16.971] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:17.062] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:17.122] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:17.183] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:17.273] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:17.304] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:17.423] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:17.425] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:17.545] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:17.574] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:17.666] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:17.725] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:17.787] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:17.876] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:17.908] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:18.027] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:18.029] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:18.150] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:18.177] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:18.271] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:18.328] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:18.391] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:18.479] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:18.512] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:18.629] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:18.633] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:18.754] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:18.780] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:18.874] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:18.931] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:18.995] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:19.082] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:19.116] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:19.233] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:19.237] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:19.358] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:19.384] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:19.479] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:19.535] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:19.600] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:19.686] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:19.720] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:19.837] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:19.842] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:19.988] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:20.139] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:20.140] DEBUG    | audio_action_controller.py:174 - Modbus 客户端未连接，跳过线圈写入
[14:15:20.140] INFO     | audio_action_controller.py:348 - 音频播放完成
[14:15:20.289] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:20.440] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:20.591] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:20.742] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
[14:15:20.892] DEBUG    | audio_action_controller.py:155 - Modbus 客户端未连接，跳过寄存器写入
