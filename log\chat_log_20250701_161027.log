2025-07-01 16:10:28.282 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-07-01 16:10:28.282 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-01 16:10:28.299 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751357428300&accessNonce=47f5ee25-2aa6-478c-bca6-beeca6c42d7f&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=d963ee2a-5652-11f0-9508-dc4546c07870&requestId=93269490-f6b2-4fe5-a845-b8592d7eaa06_joyinside&accessSign=ee9a08dbb94d6f498b0705a1fdde0639, request_id: 93269490-f6b2-4fe5-a845-b8592d7eaa06_joyinside
2025-07-01 16:10:28.301 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-01 16:10:28.301 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-01 16:10:28.756 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-01 16:10:28.861 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-07-01 16:10:30.255 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-01 16:10:30.256 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-07-01 16:10:30.256 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-07-01 16:10:30.256 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-07-01 16:10:30.310 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-01 16:10:30.310 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-07-01 16:10:32.207 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-07-01 16:10:32.207 - chat_with_robot - voice.py - stop - line 401 - INFO - 已停止local_streaming检测器
