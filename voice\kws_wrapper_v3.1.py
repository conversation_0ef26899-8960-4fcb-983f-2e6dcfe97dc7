import os
import threading
import time
import numpy as np
from util.logger import logger
# import jdwakeup  # 引入流式处理的库
import sherpa_onnx  # 引入sherpa_onnx替代jdwakeup

# class KWSDetector:
#     """C++关键词唤醒系统的Python包装器"""
    
#     def __init__(self, 
#                  kws_path="kws/cpp_demo", 
#                  model_path="models",
#                  frame_size=40, 
#                  chunk_size=50, 
#                  wav_chunk=10):
#         """
#         初始化KWS检测器
        
#         参数:
#             kws_path: KWS C++可执行文件所在的路径
#             model_path: 模型文件所在的路径
#             frame_size: 特征帧大小
#             chunk_size: 解码块大小
#             wav_chunk: 音频块大小
#         """
#         self.base_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), kws_path)
#         self.model_path = os.path.join(self.base_path, model_path)
#         self.exec_path = os.path.join(self.base_path, "kws_tiny_tranducer_simulate_stream_main")
#         self.frame_size = frame_size
#         self.chunk_size = chunk_size
#         self.wav_chunk = wav_chunk
        
#         # print(f"KWS路径: {self.base_path}")
#         # print(f"模型路径: {self.model_path}")
#         # print(f"可执行文件路径: {self.exec_path}")
        
#         # 确保可执行文件存在且有执行权限
#         if not os.path.exists(self.exec_path):
#             raise FileNotFoundError(f"KWS可执行文件不存在: {self.exec_path}")
        
#         # 设置执行权限
#         os.chmod(self.exec_path, 0o755)
        
#         # 音频处理参数
#         self.CHANNELS = 1
#         self.RATE = 16000
#         self.FORMAT = pyaudio.paInt16
#         self.CHUNK = 2048  # 与原来的asr.py保持一致
        
#         # 检测结果
#         self.detected = False
#         self.detection_callback = None
        
#         # 运行状态
#         self.running = False
#         self.thread = None
        
#         # 音频缓冲区
#         self.audio_buffer = deque(maxlen=20)  # 保存约maxlen * 0.128秒的音频
        
#         self.last_wakeup_time = 0  # 添加上次唤醒时间记录
#         self.wakeup_cooldown = 1.0  # 唤醒冷却时间（秒）
    
#     def add_audio(self, audio_data):
#         """添加音频数据到缓冲区"""
#         self.audio_buffer.append(audio_data)
    
#     def save_buffer_to_wav(self, filename):
#         """将当前缓冲区的音频保存为WAV文件"""
#         if not self.audio_buffer:
#             return False
            
#         wf = wave.open(filename, 'wb')
#         wf.setnchannels(self.CHANNELS)
#         wf.setsampwidth(2)  # 16位音频
#         wf.setframerate(self.RATE)
#         wf.writeframes(b''.join(self.audio_buffer))
#         wf.close()
        
#         # print(f"已保存音频到 {filename}, 大小: {os.path.getsize(filename)} 字节")
#         return True
    
#     def set_detection_callback(self, callback):
#         """设置检测到唤醒词时的回调函数"""
#         self.detection_callback = callback
    
#     def detect_from_file(self, audio_file):
#         """从音频文件检测唤醒词"""
#         try:
#             # 检查冷却时间
#             current_time = time.time()
#             if current_time - self.last_wakeup_time < self.wakeup_cooldown:
#                 return False

#             # 获取文件的绝对路径
#             abs_file_path = os.path.abspath(audio_file)
            
#             # 构建命令
#             cmd = [
#                 self.exec_path,
#                 str(self.frame_size),
#                 str(self.chunk_size),
#                 str(self.wav_chunk),
#                 self.model_path,
#                 abs_file_path
#             ]
            
#             # 设置环境变量
#             env = os.environ.copy()
#             env["LD_LIBRARY_PATH"] = f"{self.base_path}:{env.get('LD_LIBRARY_PATH', '')}"
            
#             # 执行命令
#             result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
#             # 输出完整的结果，便于调试
#             # print(f"命令退出码: {result.returncode}")
#             # print(f"标准输出:\n{result.stdout}")
#             # print(f"标准错误:\n{result.stderr}")
            
#             # 检查输出中是否包含result: 3
#             if "result: 3" in result.stdout:
#                 self.last_wakeup_time = current_time  # 更新最后唤醒时间
#                 return True
                
#             return False
            
#         except Exception as e:
#             print(f"检测文件时出错: {e}")
#             return False
    
#     def _detection_thread(self):
#         """检测线程"""
#         temp_wav = os.path.join(self.base_path, "temp_buffer.wav")
        
#         while self.running:
#             # 每0.03秒检查一次
#             if len(self.audio_buffer) > 1:  # 确保有足够的音频数据
#                 if self.save_buffer_to_wav(temp_wav):
#                     result = self.detect_from_file(temp_wav)
#                     if result:
#                         self.detected = True
#                         if self.detection_callback:
#                             self.detection_callback()
#                         # 清空缓冲区
#                         self.audio_buffer.clear()
#             time.sleep(0.03)
    
#     def start(self):
#         """启动检测线程"""
#         if self.running:
#             return
            
#         self.running = True
#         self.thread = threading.Thread(target=self._detection_thread)
#         self.thread.daemon = True
#         self.thread.start()
#         print("KWS检测线程已启动")
    
#     def stop(self):
#         """停止检测线程"""
#         self.running = False
#         if self.thread:
#             self.thread.join(timeout=1.0)
#             self.thread = None
#         print("KWS检测线程已停止") 

# class KWSStreamDetector:
#     """流式关键词唤醒系统Python包装器"""
    
#     def __init__(self, 
#                  model_path="models"):  # sample_rate=16000, wav_chunk=10
#         """
#         初始化流式KWS检测器
        
#         参数:
#             model_path: 模型文件所在的路径
#         """
#         self.model_path = model_path
#         # self.sample_rate = sample_rate
#         # self.wav_chunk = wav_chunk
        
#         # 初始化JD唤醒库
#         try:
#             self.detector = jdwakeup.JdwakeupWrapper(self.model_path)
#             logging.info("流式KWS检测器初始化成功")
#         except Exception as e:
#             logging.error(f"流式KWS检测器初始化失败: {e}")
#             raise
        
#         # 检测结果
#         self.detected = False
#         self.detection_callback = None
        
#         # 运行状态
#         self.running = False
#         self.processing_thread = None
        
#         self.last_wakeup_time = 0
#         self.wakeup_cooldown = 1.0  # 唤醒冷却时间（秒）
    
#     def accept_audio(self, audio_data):
#         """接收音频数据并进行处理（由主程序音频回调提供）"""
#         if not self.running:
#             return
            
#         try:
#             # 将音频数据转换为int16列表格式
#             int16_data = list(memoryview(audio_data).cast('h'))
#             # 送入检测器
#             self.detector.accept_waveform(int16_data)
#         except Exception as e:
#             logging.error(f"处理音频数据出错: {e}")
    
#     def set_detection_callback(self, callback):
#         """设置检测到唤醒词时的回调函数"""
#         self.detection_callback = callback
    
#     def _detection_thread(self):
#         """检测线程"""
#         self.detector.reset()
        
#         while self.running:
#             try:
#                 # 前向处理
#                 self.detector.step_forward()
#                 current_result = self.detector.get_result()
                
#                 # 结果判断
#                 if current_result > 1:  # 检测到唤醒词
#                     current_time = time.time()
#                     if current_time - self.last_wakeup_time >= self.wakeup_cooldown:
#                         logging.info(f'检测到唤醒词: {current_result}')
#                         self.detected = True
#                         self.last_wakeup_time = current_time
                        
#                         # 触发回调
#                         if self.detection_callback:
#                             self.detection_callback()
                        
#                         # 重置检测器
#                         self.detector.reset()
                
#                 # 短暂休眠，降低CPU使用率
#                 time.sleep(0.01)
#             except Exception as e:
#                 logging.error(f"检测线程出错: {e}")
#                 time.sleep(0.1)  # 错误后稍长休眠
    
#     def start(self):
#         """启动检测线程"""
#         if self.running:
#             return
            
#         self.running = True
#         self.processing_thread = threading.Thread(target=self._detection_thread)
#         self.processing_thread.daemon = True
#         self.processing_thread.start()
#         logging.info("流式KWS检测线程已启动")
    
#     def stop(self):
#         """停止检测线程"""
#         self.running = False
#         if self.processing_thread:
#             self.processing_thread.join(timeout=1.0)
#             self.processing_thread = None
#         logging.info("流式KWS检测线程已停止")

class KWSStreamDetector:
    """流式关键词唤醒系统Python包装器（基于sherpa_onnx）"""
    
    def __init__(self, 
                 model_path="models"):
        """
        初始化流式KWS检测器
        
        参数:
            model_path: 模型文件所在的路径
        """
        self.model_path = model_path
        
        # 从kws_v3_demo确定模型文件路径
        kws_v3_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                  "kws/kws_v3.1_demo")
        
        # 初始化sherpa_onnx关键词检测器
        try:
            self.detector = sherpa_onnx.KeywordSpotter(
                tokens=os.path.join(kws_v3_path, "tokens.txt"),
                encoder=os.path.join(kws_v3_path, "encoder-epoch-5-avg-1-chunk-16-left-64.int8.onnx"),
                decoder=os.path.join(kws_v3_path, "decoder-epoch-5-avg-1-chunk-16-left-64.int8.onnx"),
                joiner=os.path.join(kws_v3_path, "joiner-epoch-5-avg-1-chunk-16-left-64.int8.onnx"),
                num_threads=2,
                max_active_paths=4,
                keywords_file=os.path.join(kws_v3_path, "keywords.txt"),
                # keywords_score=0.01,
                # keywords_threshold=0.7,
                num_trailing_blanks=0,
                provider="cpu",
            )
            self.stream = self.detector.create_stream()
            logger.info("sherpa_onnx流式KWS检测器初始化成功")
        except Exception as e:
            logger.error(f"sherpa_onnx流式KWS检测器初始化失败: {e}")
            raise
        
        # 检测结果
        self.detected = False
        self.detection_callback = None
        
        # 运行状态
        self.running = False
        self.processing_thread = None
        
        self.last_wakeup_time = 0
        self.wakeup_cooldown = 0.5  # 唤醒冷却时间（秒）
        
        # 采样率
        self.sample_rate = 16000
    
    def accept_audio(self, audio_data):
        """接收音频数据并进行处理（由主程序音频回调提供）"""
        if not self.running:
            return
            
        try:
            # 将音频数据转换为float32格式
            samples = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
            samples = samples.reshape(-1)
            # 送入检测器
            self.stream.accept_waveform(self.sample_rate, samples)
        except Exception as e:
            logger.error(f"处理音频数据出错: {e}")
    
    def set_detection_callback(self, callback):
        """设置检测到唤醒词时的回调函数"""
        self.detection_callback = callback
    
    def _detection_thread(self):
        """检测线程"""
        while self.running:
            try:
                # 检查是否有数据可处理
                if self.detector.is_ready(self.stream):
                    # 解码流
                    self.detector.decode_stream(self.stream)
                    # 获取结果
                    result = self.detector.get_result(self.stream)
                    #print(f"result: {result}")
                    # 如果检测到唤醒词
                    if result:
                        if time.time() - self.last_wakeup_time >= self.wakeup_cooldown:
                            self.detection_callback()
                            self.last_wakeup_time = time.time()
                        self.detector.reset_stream(self.stream)
                
                # 短暂休眠，降低CPU使用率
                #time.sleep(0.01)
            except Exception as e:
                logger.error(f"检测线程出错: {e}")
                time.sleep(0.1)  # 错误后稍长休眠
    
    def start(self):
        """启动检测线程"""
        if self.running:
            return
            
        self.running = True
        self.processing_thread = threading.Thread(target=self._detection_thread)
        self.processing_thread.daemon = True
        self.processing_thread.start()
        logger.info("sherpa_onnx流式KWS检测线程已启动")
    
    def stop(self):
        """停止检测线程"""
        self.running = False
        if self.processing_thread:
            self.processing_thread.join(timeout=1.0)
            self.processing_thread = None
            #self.processing_thread.terminate()
        logger.info("sherpa_onnx流式KWS检测线程已停止")