#!/usr/bin/env python
# -*- coding: utf-8 -*-

import serial
import json
import time

"""
| 分类   | 同步头 | 用户ID | 消息类型 | 消息长度   | 消息ID    | 消息数据   | 校验码     |
|--------|--------|--------|----------|------------|-----------|------------|------------|
| 字节   | 0      | 1      | 2        | 3~4        | 5~6       | 7  ~ m     | m+1        |
| 握手请求 | 0xA5   | 0x01   | 0x01     | ...        | ...       | ...        | ...        |
| 设备消息 | 0xA5   | 0x01   | 0x04     | ...        | ...       | JSON字符串 | ...        |
| 主控消息 | 0xA5   | 0x01   | 0x05     | ...        | ...       | ...        | ...        |
| 确认消息 | 0xA5   | 0x01   | 0xff     | ...        | ...       | ...        | ...        |
"""

class WheeltecMic:
    def __init__(self, port='/dev/ttyACM0'):
        self.usart_port_name = port
        self.awake_words = "ni3 hao3 xiao3 dong1"
        self.receive_data = bytearray()
        try:
            self.serial_port = serial.Serial(
                port=self.usart_port_name,
                baudrate=115200,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=0
            )
            print(">>>>>成功打开麦克风设备")
            print(f">>>>>唤醒词为: \"{self.awake_words}\"")
        except Exception as e:
            print(f"无法打开麦克风设备: {str(e)}")
            print(">>>>>无法打开麦克风设备，尝试重新连接进行测试")
            self.serial_port = None

    @staticmethod
    def calc_checksum(frame):
        """计算校验和（补码）"""
        return (~(sum(frame[:-1]) & 0xFF) + 1) & 0xFF

    def parse_frame(self):
        """解析缓冲区中的完整帧"""
        while True:
            if len(self.receive_data) < 8:
                break
            if self.receive_data[0] != 0xA5 or self.receive_data[1] != 0x01:
                self.receive_data = self.receive_data[1:]
                continue
            msg_type = self.receive_data[2]
            msg_len = self.receive_data[3] | (self.receive_data[4] << 8)
            total_len = 7 + msg_len + 1
            if len(self.receive_data) < total_len:
                break
            frame = self.receive_data[:total_len]
            if frame[-1] != self.calc_checksum(frame):
                print("校验失败，丢弃本帧")
                self.receive_data = self.receive_data[1:]
                continue
            if msg_type == 0x04:
                self.handle_device_msg(frame[7:-1])
            self.receive_data = self.receive_data[total_len:]

    def handle_device_msg(self, data_bytes):
        """处理设备消息（JSON）"""
        try:
            json_start = data_bytes.index(ord('{'))
            json_str = data_bytes[json_start:].decode('utf-8', errors='ignore')
            json_data = json.loads(json_str)
            result = json_data.get('content', {}).get('result', '')
            info_str = json_data.get('content', {}).get('info', '')
            angle = None
            if info_str:
                try:
                    info_json = json.loads(info_str)
                    angle = info_json.get('ivw', {}).get('angle', None)
                except Exception:
                    pass
            print(f"检测到唤醒词: {result}")
            if angle is not None:
                print(f"唤醒角度: {angle}")
        except Exception as e:
            print("消息体解析失败", e)

    def run(self):
        if not self.serial_port:
            return
        try:
            while True:
                n = self.serial_port.in_waiting
                if n:
                    print(f"接收到{n}字节数据")
                    data = self.serial_port.read(n)
                    if data:
                        self.receive_data.extend(data)
                        # 防止缓冲区过大
                        if len(self.receive_data) > 4096:
                            self.receive_data = self.receive_data[-4096:]
                        self.parse_frame()
                time.sleep(0.005)
        except KeyboardInterrupt:
            print("\n程序已停止")
        finally:
            self.serial_port.close()

if __name__ == '__main__':
    mic = WheeltecMic()
    mic.run()