2025-07-11 16:08:39.236 - chat_with_robot - chat_with_robot.py - <module> - line 640 - INFO - use_action: dont
2025-07-11 16:08:39.236 - chat_with_robot - chat_with_robot.py - <module> - line 641 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-11 16:08:39.236 - chat_with_robot - chat_with_robot.py - init_websocket - line 319 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1752221319237&accessNonce=10cbdeea-f4aa-41be-b6e0-c992c87ce142&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=4083c12f-5e2e-11f0-b99c-dc4546c07870&requestId=65012570-1fbe-49ba-a5a3-1e3a773e5026_joyinside&accessSign=74e377dcb1f57dad45aa1059556fc90c, request_id: 65012570-1fbe-49ba-a5a3-1e3a773e5026_joyinside
2025-07-11 16:08:39.237 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-11 16:08:39.237 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-11 16:08:39.724 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-11 16:08:39.885 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-07-11 16:08:41.329 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-11 16:08:41.330 - chat_with_robot - voice.py - _setup_audio_stream - line 324 - INFO - 使用音频设备: 1
2025-07-11 16:08:41.330 - chat_with_robot - voice.py - _setup_audio_stream - line 325 - INFO - channels: 4 <class 'int'>
2025-07-11 16:08:41.330 - chat_with_robot - voice.py - _setup_audio_stream - line 326 - INFO - rate: 44100.0 <class 'float'>
2025-07-11 16:08:41.384 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-11 16:08:41.385 - chat_with_robot - voice.py - init_wakeup - line 311 - INFO - 本地流式KWS检测器启动成功
2025-07-11 16:08:42.385 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-11 16:08:42.385 - chat_with_robot - chat_with_robot.py - play_audio - line 525 - INFO - 使用 audio_action_controller 播放: ./asserts/ding.wav
2025-07-11 16:08:42.387 - chat_with_robot - chat_with_robot.py - play_audio - line 527 - INFO - audio_action_controller 播放完成: ./asserts/ding.wav
2025-07-11 16:08:42.387 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 84 - INFO - Windows 系统，使用 audio_action_controller 播放: asserts/tts/dog_ok.mp3
2025-07-11 16:08:49.264 - chat_with_robot - voice.py - detect_callback - line 426 - INFO - [wakeup] 检测到唤醒词
2025-07-11 16:08:49.264 - chat_with_robot - voice.py - end_streaming - line 225 - INFO - [end recording]...
2025-07-11 16:08:49.330 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-11 16:08:49.330 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-11 16:08:49.331 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/zaine.wav
2025-07-11 16:08:49.332 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用 audio_action_controller 播放唤醒音频: asserts/zaine.wav
2025-07-11 16:08:49.332 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/zaine.wav
2025-07-11 16:08:49.332 - chat_with_robot - voice.py - start_streaming - line 221 - INFO - [start recording]...
2025-07-11 16:08:49.343 - chat_with_robot - voice.py - run - line 484 - INFO - [run] 持续监听状态...
2025-07-11 16:08:53.756 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想知道回锅肉怎么做, 时间戳: 2025-07-11 16:08:53.779000
2025-07-11 16:08:55.445 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-11 16:08:55.451000
2025-07-11 16:08:55.445 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1672
2025-07-11 16:08:55.459 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:08:55.466 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:08:55.473 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:08:55.473 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:08:55.474 - chat_with_robot - audio_player.py - _play_single_audio - line 178 - INFO - 使用 audio_action_controller 播放大模型语音: temp_tts_audio.mp3
2025-07-11 16:08:55.474 - chat_with_robot - audio_player.py - _play_single_audio - line 182 - INFO - 大模型语音播放完成
2025-07-11 16:08:55.475 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:08:55.935 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:08:55.946 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:08:55.948 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:08:55.948 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:08:55.949 - chat_with_robot - audio_player.py - _play_single_audio - line 178 - INFO - 使用 audio_action_controller 播放大模型语音: temp_tts_audio.mp3
2025-07-11 16:08:55.949 - chat_with_robot - audio_player.py - _play_single_audio - line 182 - INFO - 大模型语音播放完成
2025-07-11 16:08:55.951 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:08:56.799 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:08:56.807 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:08:56.810 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:08:56.810 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:08:56.810 - chat_with_robot - audio_player.py - _play_single_audio - line 178 - INFO - 使用 audio_action_controller 播放大模型语音: temp_tts_audio.mp3
2025-07-11 16:08:56.812 - chat_with_robot - audio_player.py - _play_single_audio - line 182 - INFO - 大模型语音播放完成
2025-07-11 16:08:56.813 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:08:57.101 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:08:57.102 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-11 16:08:57.109 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:08:57.119 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:08:57.119 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:08:57.119 - chat_with_robot - audio_player.py - _play_single_audio - line 178 - INFO - 使用 audio_action_controller 播放大模型语音: temp_tts_audio.mp3
2025-07-11 16:08:57.120 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: 4083c12f-5e2e-11f0-b99c-dc4546c07870; requestId: 65012570-1fbe-49ba-a5a3-1e3a773e5026_joyinside; asr: 我想知道回锅肉怎么做; 响应时间: 0; JD机器人回复: 哇～东东也喜欢做饭吗？回锅肉超好吃的！我记得要用五花肉先煮一下，然后切片炒到金黄，再加豆瓣酱和蒜苗～不过具体步骤我有点记不清啦...要不我们下次一起查菜谱试试？
2025-07-11 16:08:57.121 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-11 16:08:57.121 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-11 16:08:57.121 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-11 16:08:57.121 - chat_with_robot - audio_player.py - _play_single_audio - line 182 - INFO - 大模型语音播放完成
2025-07-11 16:08:57.122 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:08:57.664 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:09:04.695 - chat_with_robot - voice.py - detect_callback - line 426 - INFO - [wakeup] 检测到唤醒词
2025-07-11 16:09:04.695 - chat_with_robot - voice.py - end_streaming - line 225 - INFO - [end recording]...
2025-07-11 16:09:04.760 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-11 16:09:04.760 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-11 16:09:04.762 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-07-11 16:09:04.762 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用 audio_action_controller 播放唤醒音频: asserts/shenmeshi.wav
2025-07-11 16:09:04.763 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-07-11 16:09:04.763 - chat_with_robot - voice.py - start_streaming - line 221 - INFO - [start recording]...
2025-07-11 16:09:04.831 - chat_with_robot - voice.py - run - line 484 - INFO - [run] 持续监听状态...
2025-07-11 16:09:05.438 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好东东, 时间戳: 2025-07-11 16:09:05.466000
2025-07-11 16:09:07.042 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-11 16:09:07.040000
2025-07-11 16:09:07.042 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1574
2025-07-11 16:09:07.049 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:09:07.049 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-11 16:09:07.051 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:09:07.061 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:09:07.061 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:09:07.061 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: 4083c12f-5e2e-11f0-b99c-dc4546c07870; requestId: 65012570-1fbe-49ba-a5a3-1e3a773e5026_joyinside; asr: ，你好东东; 响应时间: 0; JD机器人回复: 你好呀！我是东东～今天有什么好玩的事要和我分享吗？
2025-07-11 16:09:07.061 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-11 16:09:07.062 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-11 16:09:07.062 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-11 16:09:07.062 - chat_with_robot - audio_player.py - _play_single_audio - line 178 - INFO - 使用 audio_action_controller 播放大模型语音: temp_tts_audio.mp3
2025-07-11 16:09:07.064 - chat_with_robot - audio_player.py - _play_single_audio - line 182 - INFO - 大模型语音播放完成
2025-07-11 16:09:07.064 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:09:07.088 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:11:03.022 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:11:03.022 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:11:26.291 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:11:26.292 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:11:42.941 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-07-11 16:11:42.941 - chat_with_robot - voice.py - stop - line 421 - INFO - 已停止local_streaming检测器
