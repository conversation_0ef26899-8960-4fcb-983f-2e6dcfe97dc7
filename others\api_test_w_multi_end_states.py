"""
@Author: weixuelong1 <EMAIL>, he<PERSON><PERSON> <EMAIL>
@Create Date: 2025.04.09
@Description: 

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
"""

import requests
import json
import time

TIMEOUT = 5  # seconds
META_ACTIONS = {
    "前进": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states": ["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 1秒, 移动距离: 0.3米"
    },
    "后退": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 1秒, 移动距离: 0.3米"
    },
    "左平移": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 1秒, 移动距离: 0.15米"
    },
    "右平移": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 1秒, 移动距离: 0.15米"
    },
    "左转": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 1秒, 移动距离: 0米"
    },
    "右转": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 1秒, 移动距离: 0米"
    },
    "起立": {
        "start_states": ["趴下状态", "低速静止"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 1.5秒, 移动距离: 0米"
    },
    "趴下": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states": ["趴下状态"],
        "description": "耗时: 1.5秒, 移动距离: 0米"
    },
    "打招呼": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 7秒, 移动距离: 0米"
    },
    "扭身体": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 20秒, 移动距离: 0米"
    },
    "向前跳": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 2.5秒, 移动距离: 0.84米"
    },
    "摇尾巴撒娇": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 2.75秒, 移动距离: 0米"
    },
    "抬头": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 0.95秒, 移动距离: 0米"
    },
    "悠闲": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 6.75秒, 移动距离: 0米"
    },
    "兴奋": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 1.33秒, 移动距离: 0米"
    },
    "停止运动": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 0.75秒, 移动距离: 0米"
    },
    "沮丧": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 8.2秒, 移动距离: 0米"
    },
    "倾听": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 3.2秒, 移动距离: 0米"
    },
    "关闭遇到障碍停止模式": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 0.95秒, 移动距离: 0米"
    },
    "开启跟随模式": {
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 0.95秒, 移动距离: 0米"
    },
    "开启遇到障碍停止模式":{
        "start_states": ["低速静止", "中速静止", "高速静止", " 趴下状态"],
        "end_states":["低速静止", "中速静止", "高速静止"],
        "description": "耗时: 0.95秒, 移动距离: 0米"
    }
}
EXAMPLES = [
    [
        "你今天心情怎么样",
        ["打招呼"]
    ],
    [
        "你会扭秧歌吗",
        ["左转", "右转", "扭身体"]
    ],
    [
        "你能给我跳支舞吗",
        ["扭身体"]
    ],
    [
        "小犀好可爱啊",
        ["摇尾巴撒娇"]
    ],
    [
        "小犀跑两步",
        ["前进", "前进"]
    ],
    [
        "向左转个圈",
        ["左转", "左转", "左转", "左转", "左转", "左转", "左转", "左转"]
    ],
    [
        "小犀撒个娇",
        ["摇尾巴撒娇"]
    ],
    [
        "小犀求抱抱",
        ["向前跳"]
    ],
    [
        "快停下",
        ["停止运动"]
    ],
    [
        "向右转90度",
        ["右转", "右转", "右转"]
    ],
    [
        "走路狂野点",
        ["关闭遇到障碍停止模式"]
    ],
    [
        "走路小心点",
        ["开启遇到障碍停止模式"]
    ],
    [
        "小犀你真好看",
        ["兴奋", "摇尾巴撒娇"]
    ],
    [
        "快点啦",
        ["抬头"]
    ],
    [
        "你都会做什么呀",
        ["悠闲"]
    ],
    [
        "好喜欢你",
        ["兴奋"]
    ],
    [
        "我来讲个故事",
        ["倾听"]
    ],
    [
        "讨厌你",
        ["沮丧"]
    ]
    ,
    [
        ["你能跑两步吗"],
        ["高速, 前进, 前进"]
    ],
    [
        ["你能快走两步吗"],
        ["中速, 前进, 前进"]
    ],
    [
        ["别走那么快"],
        ["抬头, 低速"]
    ],
    [
        ["跟着我"],
        ["开启跟随模式"]
    ],
    [
        ["不用跟着我啦"],
        ["关闭跟随模式"]
    ],
    [
        ["来个厉害的招式"],
        ["扭身跳"]
    ]
]

def trigger_query(content, current_state, examples=EXAMPLES, meta_actions=META_ACTIONS):
    start_time = time.time()
    data = {
        "content": content,
        "current_state": current_state,
        "examples": examples,
        "meta_actions": meta_actions,
    }
    timeout = TIMEOUT
    try:
        response = requests.post("http://proxy-7861-heqingrong7-notebook-01.kuplus.jd.com/arrange_action", json=data, timeout=timeout)
        # response = requests.post("http://127.0.0.1:30000/arrange_action", json=data, timeout=timeout)
        response.raise_for_status()  # Raise an error for bad responses
        data = response.json()
        json_str = json.dumps(data, ensure_ascii=False)
    except requests.exceptions.Timeout:
        print(f"Request timed out after {timeout} seconds")
        data = {
            "actions": [
                "请求超时"
            ],
            "is_valid": False
        }
        json_str = json.dumps(data, ensure_ascii=False)
    print(json_str)
    print(f"Time taken: {time.time() - start_time:.2f} seconds")
    return json_str
    
if __name__ == '__main__':
    content = "左转90度"
    current_state = "低速静止"
    trigger_query(content, current_state)