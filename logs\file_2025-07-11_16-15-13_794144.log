[16:15:13.799] INFO     | audio_action_controller.py:144 - Modbus 功能已禁用（pymodbus 未安装）
[16:15:17.437] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: ./asserts/ding.wav
[16:15:17.437] INFO     | audio_action_controller.py:193 - 音频队列处理线程已启动
[16:15:17.438] INFO     | audio_action_controller.py:230 - 音频已添加到队列: ./asserts/ding.wav
[16:15:17.438] INFO     | audio_action_controller.py:176 - 队列播放音频: ./asserts/ding.wav
[16:15:17.438] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: asserts/tts/dog_ok.mp3
[16:15:17.439] INFO     | audio_action_controller.py:230 - 音频已添加到队列: asserts/tts/dog_ok.mp3
[16:15:17.493] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:15:17.494] INFO     | audio_action_controller.py:78 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[16:15:17.495] INFO     | audio_action_controller.py:245 - 正在加载音频: ./asserts/ding.wav
[16:15:17.495] INFO     | audio_action_controller.py:50 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[16:15:17.496] INFO     | audio_action_controller.py:259 - 音频长度(从文件获取): 0.9675963521003723秒
[16:15:17.496] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:15:17.496] INFO     | audio_action_controller.py:277 - 开始播放音频
[16:15:17.497] INFO     | audio_action_controller.py:282 - 嘴部动作控制线程已启动
[16:15:17.497] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:17.498] INFO     | audio_action_controller.py:285 - 脖子动作控制线程已启动
[16:15:17.498] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:17.619] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:17.649] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:17.740] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:17.800] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:17.861] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:17.951] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:17.982] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:18.102] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:18.103] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:18.224] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:18.253] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:18.344] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:18.404] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:18.507] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:15:18.507] INFO     | audio_action_controller.py:302 - 音频播放完成
[16:15:18.507] INFO     | audio_action_controller.py:176 - 队列播放音频: asserts/tts/dog_ok.mp3
[16:15:18.555] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:18.598] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:15:18.601] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:15:18.602] INFO     | audio_action_controller.py:245 - 正在加载音频: asserts/tts/dog_ok.mp3
[16:15:18.610] INFO     | audio_action_controller.py:50 - 音频音量设置为: 85.0% (针对WAV无损格式优化)
[16:15:18.610] INFO     | audio_action_controller.py:259 - 音频长度(从文件获取): 4.439773082733154秒
[16:15:18.612] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:15:18.612] INFO     | audio_action_controller.py:277 - 开始播放音频
[16:15:18.613] INFO     | audio_action_controller.py:282 - 嘴部动作控制线程已启动
[16:15:18.613] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:18.614] INFO     | audio_action_controller.py:285 - 脖子动作控制线程已启动
[16:15:18.614] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:18.734] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:18.765] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:18.855] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:18.916] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:18.976] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:19.067] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:19.097] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:19.218] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:19.218] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:19.339] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:19.368] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:19.460] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:19.520] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:19.581] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:19.582] INFO     | audio_action_controller.py:207 - 收到音频打断信号
[16:15:19.582] INFO     | audio_action_controller.py:213 - 已停止当前音频播放
[16:15:19.582] INFO     | audio_action_controller.py:221 - 已清空音频播放队列
[16:15:19.583] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: asserts/shenmeshi.wav
[16:15:19.583] INFO     | audio_action_controller.py:230 - 音频已添加到队列: asserts/shenmeshi.wav
[16:15:19.619] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:15:19.620] INFO     | audio_action_controller.py:304 - 音频播放被打断结束
[16:15:19.620] INFO     | audio_action_controller.py:176 - 队列播放音频: asserts/shenmeshi.wav
[16:15:19.671] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:19.703] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:15:19.703] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:19.704] INFO     | audio_action_controller.py:78 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[16:15:19.704] INFO     | audio_action_controller.py:245 - 正在加载音频: asserts/shenmeshi.wav
[16:15:19.822] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:19.825] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:19.870] INFO     | audio_action_controller.py:50 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[16:15:19.871] INFO     | audio_action_controller.py:259 - 音频长度(从文件获取): 0.7745804786682129秒
[16:15:19.872] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:15:19.872] INFO     | audio_action_controller.py:277 - 开始播放音频
[16:15:19.873] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:19.873] INFO     | audio_action_controller.py:282 - 嘴部动作控制线程已启动
[16:15:19.874] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:19.874] INFO     | audio_action_controller.py:285 - 脖子动作控制线程已启动
[16:15:19.946] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:19.973] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:19.994] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.024] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.067] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.115] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.124] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.176] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.189] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.236] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.274] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.310] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.326] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.357] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.425] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.430] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.477] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.478] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.551] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.576] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.628] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.672] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.679] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:15:20.679] INFO     | audio_action_controller.py:302 - 音频播放完成
[16:15:20.727] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.793] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.878] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:20.914] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:21.029] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:21.035] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:21.156] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:21.180] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:21.277] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:21.331] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:21.398] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:21.482] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:21.518] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:21.633] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:21.640] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:21.761] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:21.784] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:21.881] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:21.934] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:22.003] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:22.085] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:22.124] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:22.236] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:22.244] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:22.365] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:22.387] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:22.486] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:22.538] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:22.607] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:22.689] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:22.727] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:22.841] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:22.849] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:22.992] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:23.143] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:23.294] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:23.444] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:23.596] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:23.746] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:23.897] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:15:24.877] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\temp_tts_audio_f38d3cb8.mp3
[16:15:24.877] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\temp_tts_audio_f38d3cb8.mp3
[16:15:24.877] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\temp_tts_audio_f38d3cb8.mp3
[16:15:24.944] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:15:24.944] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:15:24.944] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\temp_tts_audio_f38d3cb8.mp3
[16:15:24.946] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\temp_tts_audio_f38d3cb8.mp3'.
[16:15:25.277] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\temp_tts_audio_63565bd8.mp3
[16:15:25.277] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\temp_tts_audio_63565bd8.mp3
[16:15:25.277] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\temp_tts_audio_63565bd8.mp3
[16:15:25.352] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:15:25.353] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:15:25.354] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\temp_tts_audio_63565bd8.mp3
[16:15:25.354] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\temp_tts_audio_63565bd8.mp3'.
[16:15:25.593] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\temp_tts_audio_583b7bc8.mp3
[16:15:25.594] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\temp_tts_audio_583b7bc8.mp3
[16:15:25.594] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\temp_tts_audio_583b7bc8.mp3
[16:15:25.663] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:15:25.663] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:15:25.664] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\temp_tts_audio_583b7bc8.mp3
[16:15:25.665] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\temp_tts_audio_583b7bc8.mp3'.
[16:15:25.924] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\temp_tts_audio_b8a317e8.mp3
[16:15:25.925] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\temp_tts_audio_b8a317e8.mp3
[16:15:25.925] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\temp_tts_audio_b8a317e8.mp3
[16:15:26.024] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:15:26.025] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:15:26.026] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\temp_tts_audio_b8a317e8.mp3
[16:15:26.027] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\temp_tts_audio_b8a317e8.mp3'.
[16:15:26.201] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\temp_tts_audio_12791451.mp3
[16:15:26.201] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\temp_tts_audio_12791451.mp3
[16:15:26.201] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\temp_tts_audio_12791451.mp3
[16:15:26.291] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:15:26.292] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:15:26.292] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\temp_tts_audio_12791451.mp3
[16:15:26.294] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\temp_tts_audio_12791451.mp3'.
[16:22:23.862] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\temp_tts_audio_b79f2dc8.mp3
[16:22:23.864] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\temp_tts_audio_b79f2dc8.mp3
[16:22:23.864] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\temp_tts_audio_b79f2dc8.mp3
[16:22:24.005] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:22:24.006] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:22:24.007] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\temp_tts_audio_b79f2dc8.mp3
[16:22:24.008] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\temp_tts_audio_b79f2dc8.mp3'.
[16:22:24.165] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\temp_tts_audio_c072d68b.mp3
[16:22:24.166] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\temp_tts_audio_c072d68b.mp3
[16:22:24.166] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\temp_tts_audio_c072d68b.mp3
[16:22:24.249] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:22:24.251] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:22:24.251] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\temp_tts_audio_c072d68b.mp3
[16:22:24.252] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\temp_tts_audio_c072d68b.mp3'.
[16:22:24.546] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\temp_tts_audio_17d5c1a7.mp3
[16:22:24.546] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\temp_tts_audio_17d5c1a7.mp3
[16:22:24.546] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\temp_tts_audio_17d5c1a7.mp3
[16:22:24.652] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:22:24.654] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:22:24.655] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\temp_tts_audio_17d5c1a7.mp3
[16:22:24.656] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\temp_tts_audio_17d5c1a7.mp3'.
[16:22:24.813] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\temp_tts_audio_94d89725.mp3
[16:22:24.814] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\temp_tts_audio_94d89725.mp3
[16:22:24.814] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\temp_tts_audio_94d89725.mp3
[16:22:24.902] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:22:24.903] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:22:24.904] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\temp_tts_audio_94d89725.mp3
[16:22:24.905] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\temp_tts_audio_94d89725.mp3'.
[16:23:01.137] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\temp_tts_audio_1aa1cbd4.mp3
[16:23:01.139] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\temp_tts_audio_1aa1cbd4.mp3
[16:23:01.139] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\temp_tts_audio_1aa1cbd4.mp3
[16:23:01.233] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:23:01.235] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:23:01.235] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\temp_tts_audio_1aa1cbd4.mp3
[16:23:01.237] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\temp_tts_audio_1aa1cbd4.mp3'.
[16:23:01.410] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\temp_tts_audio_987c4889.mp3
[16:23:01.412] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\temp_tts_audio_987c4889.mp3
[16:23:01.413] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\temp_tts_audio_987c4889.mp3
[16:23:01.529] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:23:01.529] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:23:01.532] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\temp_tts_audio_987c4889.mp3
[16:23:01.540] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\temp_tts_audio_987c4889.mp3'.
[16:23:01.754] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\temp_tts_audio_ca635faf.mp3
[16:23:01.756] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\temp_tts_audio_ca635faf.mp3
[16:23:01.757] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\temp_tts_audio_ca635faf.mp3
[16:23:01.873] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:23:01.873] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:23:01.874] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\temp_tts_audio_ca635faf.mp3
[16:23:01.875] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\temp_tts_audio_ca635faf.mp3'.
[16:23:02.064] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\temp_tts_audio_15d49fe2.mp3
[16:23:02.067] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\temp_tts_audio_15d49fe2.mp3
[16:23:02.067] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\temp_tts_audio_15d49fe2.mp3
[16:23:02.179] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:23:02.181] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:23:02.181] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\temp_tts_audio_15d49fe2.mp3
[16:23:02.183] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\temp_tts_audio_15d49fe2.mp3'.
[16:23:02.364] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: D:\prooject_code\Possessed_AI\temp_tts_audio_bc5ef9f3.mp3
[16:23:02.364] INFO     | audio_action_controller.py:230 - 音频已添加到队列: D:\prooject_code\Possessed_AI\temp_tts_audio_bc5ef9f3.mp3
[16:23:02.364] INFO     | audio_action_controller.py:176 - 队列播放音频: D:\prooject_code\Possessed_AI\temp_tts_audio_bc5ef9f3.mp3
[16:23:02.479] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:23:02.480] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:23:02.482] INFO     | audio_action_controller.py:245 - 正在加载音频: D:\prooject_code\Possessed_AI\temp_tts_audio_bc5ef9f3.mp3
[16:23:02.482] ERROR    | audio_action_controller.py:309 - 播放音频失败: No such file or directory: 'D:\prooject_code\Possessed_AI\temp_tts_audio_bc5ef9f3.mp3'.
