2025-06-30 18:13:22.796 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-06-30 18:13:22.797 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-06-30 18:13:22.812 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751278402813&accessNonce=a4b6d574-783a-41a4-852d-e2d1802a5d30&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=da87982f-559a-11f0-8e25-dc4546c07870&requestId=f0b8048a-bba0-43de-b1d0-170c08685884_joyinside&accessSign=15920dcee15af3e476a12cd80359264d, request_id: f0b8048a-bba0-43de-b1d0-170c08685884_joyinside
2025-06-30 18:13:22.814 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-06-30 18:13:22.815 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-06-30 18:13:23.142 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-06-30 18:13:23.358 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-06-30 18:13:24.795 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-06-30 18:13:24.796 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-06-30 18:13:24.796 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-06-30 18:13:24.796 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-06-30 18:13:24.854 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-06-30 18:13:24.855 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-06-30 18:13:34.656 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 18:13:34.656 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 18:13:34.719 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 18:13:34.719 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 18:13:35.738 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 18:13:35.750 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 18:13:38.324 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退一下, 时间戳: 2025-06-30 18:13:44.039000
2025-06-30 18:13:38.985 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 18:13:44.700000
2025-06-30 18:13:38.985 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 661
2025-06-30 18:13:39.086 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 18:13:39.086 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 18:13:39.097 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 18:13:39.108 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: da87982f-559a-11f0-8e25-dc4546c07870; requestId: f0b8048a-bba0-43de-b1d0-170c08685884_joyinside; asr: 退一下; 响应时间: 0; JD机器人回复: 车辆信息获取失败了，请重新再试试吧
2025-06-30 18:13:39.108 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 18:13:39.108 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 18:13:39.108 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 18:13:39.108 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 18:13:40.478 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 18:13:42.732 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 18:13:45.880 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退一下, 时间戳: 2025-06-30 18:13:51.598000
2025-06-30 18:13:46.428 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 18:13:52.144000
2025-06-30 18:13:46.428 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 546
2025-06-30 18:13:46.517 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 18:13:46.517 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 18:13:46.520 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 18:13:46.529 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 18:13:46.531 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: da87982f-559a-11f0-8e25-dc4546c07870; requestId: f0b8048a-bba0-43de-b1d0-170c08685884_joyinside; asr: 退一下; 响应时间: 0; JD机器人回复: 车辆信息获取失败了，请重新再试试吧
2025-06-30 18:13:46.531 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 18:13:46.531 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 18:13:46.531 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 18:13:50.253 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 18:13:52.725 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退一下, 时间戳: 2025-06-30 18:13:58.442000
2025-06-30 18:13:53.326 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 18:13:59.042000
2025-06-30 18:13:53.326 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 600
2025-06-30 18:13:53.395 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 18:13:53.395 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 18:13:53.405 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 18:13:53.414 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 18:13:53.416 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: da87982f-559a-11f0-8e25-dc4546c07870; requestId: f0b8048a-bba0-43de-b1d0-170c08685884_joyinside; asr: 退一下; 响应时间: 0; JD机器人回复: 车辆信息获取失败了，请重新再试试吧
2025-06-30 18:13:53.416 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 18:13:53.416 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 18:13:53.416 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 18:13:56.433 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 18:13:57.039 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 18:13:58.833 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，退出, 时间戳: 2025-06-30 18:14:04.549000
2025-06-30 18:13:59.395 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 18:14:05.106000
2025-06-30 18:13:59.395 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 557
2025-06-30 18:13:59.464 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 18:13:59.464 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 18:27:05.989 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 18:27:05.989 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 18:27:06.053 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 18:27:06.053 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 18:27:07.446 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 18:27:07.457 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
