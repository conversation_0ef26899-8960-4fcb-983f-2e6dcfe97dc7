# Ubuntu 24.04 Possessed_AI 项目部署指南

## 目录
1. [系统准备](#系统准备)
2. [Miniconda安装](#miniconda安装)
3. [系统依赖安装](#系统依赖安装)
4. [Python环境配置](#python环境配置)
5. [项目配置](#项目配置)
6. [音频设备配置](#音频设备配置)
7. [启动测试](#启动测试)

---

## 系统准备

### 更新系统
```bash
sudo apt-get update
sudo apt-get upgrade -y
```

### 安装基础工具
```bash
sudo apt-get install -y wget curl git vim
```

---

## Miniconda安装

### 方法1：官方安装脚本（推荐）
```bash
# 下载Miniconda安装脚本
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh

# 给脚本执行权限
chmod +x Miniconda3-latest-Linux-x86_64.sh

# 运行安装脚本
bash Miniconda3-latest-Linux-x86_64.sh

# 按照提示操作：
# 1. 按Enter查看许可协议
# 2. 输入 yes 同意协议
# 3. 按Enter使用默认安装路径 (/home/<USER>/miniconda3)
# 4. 输入 yes 初始化conda

# 重新加载shell配置
source ~/.bashrc
```

### 方法2：使用清华镜像（国内用户推荐）
```bash
# 使用清华镜像下载
wget https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/Miniconda3-latest-Linux-x86_64.sh

# 安装步骤同上
chmod +x Miniconda3-latest-Linux-x86_64.sh
bash Miniconda3-latest-Linux-x86_64.sh
source ~/.bashrc

# 配置清华镜像源
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
conda config --set show_channel_urls yes
```

### 验证安装
```bash
# 检查conda版本
conda --version

# 检查Python版本
python --version

# 更新conda
conda update conda
```

---

## 系统依赖安装

### 音频相关依赖
```bash
sudo apt-get install -y python3-dev
sudo apt-get install -y portaudio19-dev
sudo apt-get install -y python3-pyaudio
sudo apt-get install -y libasound-dev
sudo apt-get install -y libportaudio2
sudo apt-get install -y libportaudiocpp0
sudo apt-get install -y ffmpeg
sudo apt-get install -y libpulse-dev
sudo apt-get install -y build-essential
```

### 其他必要依赖
```bash
sudo apt-get install -y pkg-config
sudo apt-get install -y cmake
sudo apt-get install -y libssl-dev
sudo apt-get install -y libffi-dev
```

---

## Python环境配置

### 创建conda环境
```bash
# 创建Python 3.8环境
conda create -n tatata_dev python=3.8 -y
conda activate tatata_dev

# 确保使用正确的pip路径
export PATH=$(echo $PATH | tr ':' '\n' | grep -v "$HOME/.local/bin" | tr '\n' ':' | sed 's/:$//')
```

### 安装Python包
```bash
# 使用清华镜像安装Python包
pip install pyaudio -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install numpy -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install torch -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install torchaudio -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install pygame -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install openai -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install sherpa_onnx -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install websocket-client -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install requests -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install sounddevice -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install scipy -i https://pypi.tuna.tsinghua.edu.cn/simple
pip install pydub -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### 或使用requirements.txt（如果存在）
```bash
# 如果项目有requirements.txt文件
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

---

## 项目配置

### 下载/克隆项目
```bash
# 如果是git仓库
git clone <项目地址>
cd Possessed_AI

# 或者解压项目文件
# unzip Possessed_AI.zip
# cd Possessed_AI
```

### 配置API密钥
编辑 `chat_with_robot.py` 文件，修改以下行：

```python
# 第206-210行
self.access_key = "您的access_key"        # 替换xxxxxxxx
self.access_secret = "您的access_secret"  # 替换xxxxxxxx  
self.access_version = "V2"
self.bot_id = "您的bot_id"               # 替换xxxxxxxx
```

---

## 音频设备配置

### 查看可用音频设备
```bash
# 激活环境
conda activate tatata_dev
cd /path/to/Possessed_AI

# 查看音频设备
python util/print_audio_devices.py
```

### 配置目标音频设备
编辑 `voice/voice.py` 文件第342行，添加您的设备名称：

```python
# 在目标设备列表中添加您的设备
target_device_name = ["您的设备名称", "CP900: USB Audio", "Wireless Microphone RX", "pulse"]
```

### ALSA音频配置（可选）
```bash
# 运行ALSA配置工具
python util/asoundrc.py
```

---

## 启动测试

### 纯语音对话模式启动
```bash
# 激活环境
conda activate tatata_dev
cd /path/to/Possessed_AI

# 启动纯语音对话（不连接机器人）
python chat_with_robot.py --use_action dont --kws local_streaming --echo_cancel True --sound_th 1000
```

### 启动参数说明
- `--use_action dont`：禁用机器人控制功能
- `--kws local_streaming`：使用本地流式唤醒词检测
- `--echo_cancel True`：启用回声消除
- `--sound_th 1000`：声音检测阈值

### 验证启动成功
- 程序启动后会播放提示音
- 控制台显示"[启动HardwareAIAgent交互程序]"
- 音频设备检测成功并显示设备信息

---

## 常见问题解决

### 音频设备问题
```bash
# 检查音频设备
aplay -l
arecord -l

# 重启音频服务
pulseaudio -k && pulseaudio --start
```

### Python包安装问题
```bash
# 如果pyaudio安装失败
sudo apt-get install python3-pyaudio
pip install pyaudio --force-reinstall

# 如果torch安装失败
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
```

### 权限问题
```bash
# 添加用户到audio组
sudo usermod -a -G audio $USER
# 重新登录生效
```

---

## 项目结构说明

```
Possessed_AI/
├── chat_with_robot.py          # 主程序入口
├── voice/                      # 语音处理模块
│   ├── voice.py               # 音频设备配置
│   └── kws_wrapper.py         # 唤醒词检测
├── api/                       # API接口模块
├── util/                      # 工具模块
│   ├── print_audio_devices.py # 音频设备查看工具
│   └── asoundrc.py           # ALSA配置工具
├── robot_agent/               # 机器人控制模块
└── requirements.txt           # Python依赖列表
```

---

## 下一步

1. 完成环境配置后，获取并填写API密钥
2. 配置音频设备
3. 测试启动纯语音对话功能
4. 根据需要配置机器人控制功能

如有问题，请检查日志输出或参考项目文档。
