2025-07-02 11:06:31.315 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-07-02 11:06:31.315 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-02 11:06:31.329 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751425591329&accessNonce=78a5f066-f2b6-46b2-9f77-009b404dca0a&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=8db8ce26-56f1-11f0-aa60-dc4546c07870&requestId=0847568e-a7f7-45ce-9e95-e55b9efa69ae_joyinside&accessSign=403d5a2812d1a5b0c9cedc346024aafd, request_id: 0847568e-a7f7-45ce-9e95-e55b9efa69ae_joyinside
2025-07-02 11:06:31.330 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-02 11:06:31.331 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-02 11:06:31.785 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-02 11:06:32.005 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-07-02 11:06:33.351 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-02 11:06:33.352 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-07-02 11:06:33.352 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-07-02 11:06:33.352 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-07-02 11:06:33.411 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-02 11:06:33.411 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-07-02 11:06:38.111 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-02 11:06:38.111 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 11:06:38.176 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 11:06:38.177 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 11:06:39.469 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 11:06:39.474 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-02 11:06:42.440 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我要克隆我的声音, 时间戳: 2025-07-02 11:06:48.809000
2025-07-02 11:06:43.358 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:06:49.725000
2025-07-02 11:06:43.358 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 916
2025-07-02 11:06:43.429 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:06:43.430 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:06:43.441 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:06:43.755 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:06:43.765 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:06:44.018 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:06:44.018 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:06:44.025 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:06:44.036 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 8db8ce26-56f1-11f0-aa60-dc4546c07870; requestId: 0847568e-a7f7-45ce-9e95-e55b9efa69ae_joyinside; asr: 我要克隆我的声音; 响应时间: 0; JD机器人回复: 太好了！那我们先来确认一下，你是想让我克隆你自己的声音吗？确认一下哦！
2025-07-02 11:06:44.036 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:06:44.036 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:06:44.693 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，太好了, 时间戳: 2025-07-02 11:06:51.062000
2025-07-02 11:06:45.352 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:06:51.722000
2025-07-02 11:06:45.354 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 660
2025-07-02 11:06:45.360 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 11:06:45.360 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 11:06:45.360 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:06:45.431 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:06:45.439 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:06:45.456 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:06:45.461 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-07-02 11:06:45.733 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:06:45.743 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:06:45.995 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:06:45.995 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:06:46.006 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:06:46.017 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 8db8ce26-56f1-11f0-aa60-dc4546c07870; requestId: 0847568e-a7f7-45ce-9e95-e55b9efa69ae_joyinside; asr: ，太好了; 响应时间: 0; JD机器人回复: 那我们就开始吧！请跟我读这句话："今天天气真好我们一起出去玩吧"。记得要一字不漏哦！
2025-07-02 11:06:46.017 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:06:46.017 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:06:48.975 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:06:52.404 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:06:54.317 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:06:54.317 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:06:59.761 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，今天天气真好，我们一起出去玩吧, 时间戳: 2025-07-02 11:07:06.131000
2025-07-02 11:07:00.642 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:07:07.008000
2025-07-02 11:07:00.642 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 877
2025-07-02 11:07:00.756 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:07:00.767 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:07:00.768 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:07:01.032 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:07:01.033 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:07:01.033 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:07:01.043 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 8db8ce26-56f1-11f0-aa60-dc4546c07870; requestId: 0847568e-a7f7-45ce-9e95-e55b9efa69ae_joyinside; asr: ，今天天气真好，我们一起出去玩吧; 响应时间: 0; JD机器人回复: 小萝卜没学会你的声音，录音时间不够哦！再试一次，还是退出呢？稍等一下哦！
2025-07-02 11:07:01.043 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:07:01.043 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:07:07.310 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:07:08.197 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 11:07:08.621 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:07:08.622 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:07:10.745 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 再试一次, 时间戳: 2025-07-02 11:07:17.114000
2025-07-02 11:07:11.339 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:07:17.708000
2025-07-02 11:07:11.339 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 594
2025-07-02 11:07:11.406 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:07:11.418 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:07:11.427 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:07:11.806 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:07:11.816 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:07:12.129 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:07:12.129 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:07:12.140 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:07:12.151 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 8db8ce26-56f1-11f0-aa60-dc4546c07870; requestId: 0847568e-a7f7-45ce-9e95-e55b9efa69ae_joyinside; asr: 再试一次; 响应时间: 0; JD机器人回复: 没问题！我们再来一次吧。请跟我读这句话："今天天气真好我们一起出去玩吧"。加油，这次一定可以的！
2025-07-02 11:07:12.151 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:07:12.151 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:07:14.149 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:07:18.982 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:07:21.399 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:07:21.399 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:07:27.491 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，今天天气真好，我们一起出去玩吧, 时间戳: 2025-07-02 11:07:33.860000
2025-07-02 11:07:28.082 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:07:34.443000
2025-07-02 11:07:28.082 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 583
2025-07-02 11:07:28.188 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:07:28.188 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:07:28.190 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:07:28.198 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:07:28.201 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 8db8ce26-56f1-11f0-aa60-dc4546c07870; requestId: 0847568e-a7f7-45ce-9e95-e55b9efa69ae_joyinside; asr: ，今天天气真好，我们一起出去玩吧; 响应时间: 0; JD机器人回复: 录音时间太短了，还不到3秒哦。你想再试一次，还是退出呢？
2025-07-02 11:07:28.201 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:07:28.201 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:07:28.201 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:07:34.446 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:07:39.599 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，那你可以说一句长一点的话吗？, 时间戳: 2025-07-02 11:07:45.968000
2025-07-02 11:07:40.730 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:07:47.099000
2025-07-02 11:07:40.731 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1131
2025-07-02 11:07:40.796 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:07:40.797 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:07:40.806 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:07:41.163 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:07:41.171 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:07:41.659 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:07:41.666 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:07:42.032 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:07:42.038 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:07:42.346 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:07:42.346 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:07:42.735 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:07:42.736 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:07:42.746 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:07:42.756 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 8db8ce26-56f1-11f0-aa60-dc4546c07870; requestId: 0847568e-a7f7-45ce-9e95-e55b9efa69ae_joyinside; asr: ，那你可以说一句长一点的话吗？; 响应时间: 0; JD机器人回复: 当然可以。作为一名AI助手，我能够生成各种长度和复杂度的句子，以满足用户的需求。无论是简短的回答还是长篇大论的详细解释，我都可以根据用户的要求来提供相应的回复。如果您有任何问题或需要进一步的帮助，请随时告诉我，我会尽力为您提供满意的答案。
2025-07-02 11:07:42.756 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:07:42.756 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:07:43.728 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:07:47.356 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:07:53.503 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:07:57.733 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:08:01.458 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:08:05.988 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:08:05.988 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:08:10.059 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想你要东东, 时间戳: 2025-07-02 11:08:16.429000
2025-07-02 11:08:11.287 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:08:17.652000
2025-07-02 11:08:11.287 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1223
2025-07-02 11:08:11.351 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:08:11.354 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:08:11.362 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:08:11.838 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:08:11.845 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:08:12.155 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 11:08:12.156 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 11:08:12.156 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 11:08:12.167 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:08:12.168 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:08:12.177 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 8db8ce26-56f1-11f0-aa60-dc4546c07870; requestId: 0847568e-a7f7-45ce-9e95-e55b9efa69ae_joyinside; asr: 我想你要东东; 响应时间: 0; JD机器人回复: 好的，用户让我说一句长一点的话，但他们的后续回复“我想你要东东”有点让人困惑。
2025-07-02 11:08:12.177 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:08:12.177 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:08:12.177 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:08:12.257 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 11:08:12.597 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-02 11:08:12.597 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 11:08:12.662 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 11:08:12.662 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 11:08:13.680 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 11:08:13.742 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-02 11:08:13.846 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:08:20.213000
2025-07-02 11:08:13.847 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 3784
2025-07-02 11:08:13.919 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:08:13.930 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:08:13.931 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:08:14.247 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:08:14.255 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:08:14.742 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:08:14.753 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:08:14.805 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 11:08:14.807 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 11:08:14.807 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 11:08:14.818 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:08:14.829 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 8db8ce26-56f1-11f0-aa60-dc4546c07870; requestId: 0847568e-a7f7-45ce-9e95-e55b9efa69ae_joyinside; asr: 我想你要东东; 响应时间: 0; JD机器人回复: 当然可以。作为一名AI助手，我能够生成各种长度和复杂度的句子，以满足用户的需求。无论是简短的回答还是长篇大论的详细解释，
2025-07-02 11:08:14.829 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:08:14.829 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:08:14.829 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:08:14.839 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:08:14.909 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 11:08:15.790 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 11:08:16.884 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:08:23.252000
2025-07-02 11:08:16.885 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 6823
2025-07-02 11:08:16.955 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:08:16.957 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:08:16.967 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:08:17.204 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 11:08:17.214 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:08:17.214 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 11:08:17.215 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 8db8ce26-56f1-11f0-aa60-dc4546c07870; requestId: 0847568e-a7f7-45ce-9e95-e55b9efa69ae_joyinside; asr: 我想你要东东; 响应时间: 0; JD机器人回复: 当然可以。作为一名AI助手，
2025-07-02 11:08:17.215 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 11:08:17.215 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:08:17.216 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:08:17.216 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:08:17.271 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:08:17.316 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 11:08:17.527 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-02 11:08:17.528 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 11:08:17.593 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 11:08:17.593 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 11:08:18.863 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 11:08:18.924 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-02 11:08:21.293 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东，我想克隆我的声音, 时间戳: 2025-07-02 11:08:27.662000
2025-07-02 11:08:22.228 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:08:28.596000
2025-07-02 11:08:22.228 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 934
2025-07-02 11:08:22.297 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:08:22.301 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:08:22.307 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:08:22.599 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:08:22.600 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:08:22.932 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:08:22.939 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:08:23.300 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:08:23.301 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:08:23.310 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:08:23.320 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 8db8ce26-56f1-11f0-aa60-dc4546c07870; requestId: 0847568e-a7f7-45ce-9e95-e55b9efa69ae_joyinside; asr: 你好，东东，我想克隆我的声音; 响应时间: 0; JD机器人回复: 太好了，你想克隆你的声音！我们可以开始了。首先，我需要确认一下，你是想让我克隆你的声音吗？如果是的话，我们就可以继续下一步了。
2025-07-02 11:08:23.320 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:08:23.320 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:08:24.102 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 太好了, 时间戳: 2025-07-02 11:08:30.471000
2025-07-02 11:08:24.714 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:08:31.082000
2025-07-02 11:08:24.714 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 611
2025-07-02 11:08:24.723 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 11:08:24.723 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 11:08:24.723 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:08:24.726 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:08:24.775 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:08:24.778 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:08:24.779 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:08:24.824 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-07-02 11:08:25.096 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:08:25.104 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:08:25.393 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:08:25.394 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:08:25.867 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:08:25.867 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:08:25.867 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:08:25.879 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 8db8ce26-56f1-11f0-aa60-dc4546c07870; requestId: 0847568e-a7f7-45ce-9e95-e55b9efa69ae_joyinside; asr: 太好了; 响应时间: 0; JD机器人回复: 太好了！既然你决定了，我们就开始吧。请跟我读这句话：“今天天气真好，我们一起去公园玩吧。” 记得要一字不漏哦！
2025-07-02 11:08:25.879 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:08:25.879 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:08:27.300 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:08:30.623 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:08:34.652 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:08:36.868 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:08:36.868 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:08:43.609 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，今天天气真好，我们一起去公园玩吧, 时间戳: 2025-07-02 11:08:49.978000
2025-07-02 11:08:44.288 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:08:50.653000
2025-07-02 11:08:44.288 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 675
2025-07-02 11:08:44.389 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:08:44.398 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:08:44.408 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:08:44.706 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:08:44.712 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:08:45.096 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:08:45.096 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:08:45.453 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:08:45.454 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:08:45.465 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:08:45.475 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 8db8ce26-56f1-11f0-aa60-dc4546c07870; requestId: 0847568e-a7f7-45ce-9e95-e55b9efa69ae_joyinside; asr: ，今天天气真好，我们一起去公园玩吧; 响应时间: 0; JD机器人回复: 你读得真棒！现在我们来检查一下。嗯，几乎完美！接下来，这次我没学会你的声音，要不要再给我一次机会？不想继续也可以退出哦。嗯，声音没到3秒呢！再试一次，还是退出呀？
2025-07-02 11:08:45.475 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:08:45.475 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:08:47.938 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:08:50.658 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:08:57.611 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:09:02.444 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:09:02.444 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:09:05.173 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我应该有3秒吧, 时间戳: 2025-07-02 11:09:11.542000
2025-07-02 11:09:05.781 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:09:12.148000
2025-07-02 11:09:05.781 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 606
2025-07-02 11:09:05.857 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:09:05.862 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:09:05.872 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:09:06.132 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:09:06.143 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:09:06.457 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:09:06.457 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:09:06.843 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:09:06.845 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:09:07.201 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:09:07.201 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:09:07.206 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:09:07.217 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 8db8ce26-56f1-11f0-aa60-dc4546c07870; requestId: 0847568e-a7f7-45ce-9e95-e55b9efa69ae_joyinside; asr: ，我应该有3秒吧; 响应时间: 0; JD机器人回复: 你读得非常好，几乎完美！现在我们来检查一下。嗯，几乎完美！接下来，哎呀，声音时间太短啦！要不要再给我一次机会，还是退出休息下？这次声音没有变好，要不要再试试？退出也可以哦。
2025-07-02 11:09:07.217 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:09:07.217 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:09:08.592 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:09:10.809 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:09:13.530 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
