2025-07-10 13:02:10.494 - chat_with_robot - chat_with_robot.py - <module> - line 632 - INFO - use_action: dont
2025-07-10 13:02:10.494 - chat_with_robot - chat_with_robot.py - <module> - line 633 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-10 13:02:10.511 - chat_with_robot - chat_with_robot.py - init_websocket - line 311 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1752123730511&accessNonce=89a054e0-486d-4e62-a4af-e8762e44179e&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=0919f171-5d4b-11f0-8bd2-dc4546c07870&requestId=ec615496-6b7d-45b8-8d57-3db3a9730de2_joyinside&accessSign=1c7a61762a12946be3e626cb4a8fb32f, request_id: ec615496-6b7d-45b8-8d57-3db3a9730de2_joyinside
2025-07-10 13:02:10.512 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-10 13:02:10.512 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-10 13:02:11.103 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-10 13:02:11.212 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-07-10 13:02:12.763 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-10 13:02:12.764 - chat_with_robot - voice.py - _setup_audio_stream - line 311 - INFO - 使用音频设备: 1
2025-07-10 13:02:12.764 - chat_with_robot - voice.py - _setup_audio_stream - line 312 - INFO - channels: 4 <class 'int'>
2025-07-10 13:02:12.764 - chat_with_robot - voice.py - _setup_audio_stream - line 313 - INFO - rate: 44100.0 <class 'float'>
2025-07-10 13:02:12.821 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-10 13:02:12.821 - chat_with_robot - voice.py - init_wakeup - line 298 - INFO - 本地流式KWS检测器启动成功
2025-07-10 13:02:13.822 - chat_with_robot - chat_with_robot.py - play_audio - line 509 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-10 13:02:13.822 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 使用 audio_action_controller 播放: ./asserts/ding.wav
2025-07-10 13:02:14.891 - chat_with_robot - chat_with_robot.py - play_audio - line 519 - INFO - audio_action_controller 播放完成: ./asserts/ding.wav
2025-07-10 13:02:16.861 - chat_with_robot - voice.py - detect_callback - line 413 - INFO - [wakeup] 检测到唤醒词
2025-07-10 13:02:16.861 - chat_with_robot - voice.py - end_streaming - line 212 - INFO - [end recording]...
2025-07-10 13:02:16.925 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-10 13:02:16.926 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-10 13:02:18.181 - chat_with_robot - voice.py - start_streaming - line 208 - INFO - [start recording]...
2025-07-10 13:02:18.182 - chat_with_robot - voice.py - run - line 469 - INFO - [run] 持续监听状态...
2025-07-10 13:02:21.174 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想知道回锅肉怎么做, 时间戳: 2025-07-10 13:02:20.908000
2025-07-10 13:02:22.585 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-10 13:02:22.318000
2025-07-10 13:02:22.585 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1410
2025-07-10 13:02:22.658 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-10 13:02:22.658 - chat_with_robot - chat_with_robot.py - _task_worker - line 421 - INFO - 存入音频
2025-07-10 13:02:22.669 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-10 13:02:23.036 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-10 13:02:23.048 - chat_with_robot - chat_with_robot.py - _task_worker - line 421 - INFO - 存入音频
2025-07-10 13:02:23.328 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-10 13:02:23.338 - chat_with_robot - chat_with_robot.py - _task_worker - line 421 - INFO - 存入音频
2025-07-10 13:02:23.761 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-10 13:02:23.769 - chat_with_robot - chat_with_robot.py - _task_worker - line 421 - INFO - 存入音频
2025-07-10 13:02:24.329 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-10 13:02:24.337 - chat_with_robot - chat_with_robot.py - _task_worker - line 421 - INFO - 存入音频
2025-07-10 13:02:24.672 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-10 13:02:24.684 - chat_with_robot - chat_with_robot.py - _task_worker - line 421 - INFO - 存入音频
2025-07-10 13:02:24.953 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-10 13:02:24.953 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-10 13:02:24.964 - chat_with_robot - chat_with_robot.py - _task_worker - line 421 - INFO - 存入音频
2025-07-10 13:02:24.975 - chat_with_robot - chat_with_robot.py - _task_worker - line 375 - INFO - session_id: 0919f171-5d4b-11f0-8bd2-dc4546c07870; requestId: ec615496-6b7d-45b8-8d57-3db3a9730de2_joyinside; asr: 我想知道回锅肉怎么做; 响应时间: 0; JD机器人回复: 回锅肉超好吃！首先选五花肉，冷水下锅加姜片、料酒煮20分钟，捞出切片。锅里放油，爆香蒜片、姜丝，加豆瓣酱炒出红油。放肉片翻炒到微焦，加青椒、红椒、洋葱，加点糖、生抽调味，最后撒点葱花出锅！香喷喷的回锅肉就做好啦！
2025-07-10 13:02:24.975 - chat_with_robot - chat_with_robot.py - _task_worker - line 377 - INFO - 等待控制完成
2025-07-10 13:02:24.975 - chat_with_robot - chat_with_robot.py - _task_worker - line 382 - INFO - 等待音频播放完成
2025-07-10 13:02:25.794 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-10 13:02:29.335 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-10 13:02:31.753 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-10 13:02:35.786 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-10 13:02:40.216 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-10 13:02:44.243 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-10 13:02:46.867 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-10 13:02:46.867 - chat_with_robot - chat_with_robot.py - _task_worker - line 392 - INFO - 任务完成，继续
2025-07-10 13:03:07.323 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
