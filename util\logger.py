"""
@Author: <EMAIL> <EMAIL>
@Create Date: 2025.03.28
@Description: 

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
"""
import logging
import os
from datetime import datetime
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from robot_agent.robot_config import LOG_FILE_PATH

def setup_logger():
    # 创建日志目录
    log_file = LOG_FILE_PATH
    log_dir = os.path.dirname(log_file) 
    os.makedirs(log_dir, exist_ok=True)
    # 生成带时间戳的日志文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"chat_log_{timestamp}.log")
    # 配置根logger
    logger = logging.getLogger("chat_with_robot")

    # 重要：防止日志重复
    if logger.handlers:  # 如果已经有handler，先清除
        logger.handlers.clear()
    logger.setLevel(logging.INFO)  # 设置日志级别
    # 添加文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8', mode='a')
    file_handler.setLevel(logging.INFO)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    # 创建格式化器
    #formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')


    formatter = logging.Formatter(
        '%(asctime)s.%(msecs)03d - %(name)s - %(filename)s - %(funcName)s - line %(lineno)d - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'#[:-3]  # 精确到毫秒
    )
    
   
    file_handler.setFormatter(formatter)

     # 控制台处理器使用简洁格式
    # console_handler = logging.StreamHandler()
    # console_handler.setLevel(logging.INFO)
    # console_formatter = logging.Formatter(
    #     '%(asctime)s - %(levelname)s - %(message)s',
    #     datefmt='%Y-%m-%d %H:%M:%S'
    # )
    # console_handler.setFormatter(console_formatter)

    # 设置其他库的日志级别
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("onnxruntime").setLevel(logging.ERROR)
    # 添加处理器到logger
    logger.addHandler(file_handler)
    #logger.addHandler(console_handler)
    
    return logger

logger = setup_logger()