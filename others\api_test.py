"""
@Author: weixuelong1 <EMAIL>, he<PERSON><PERSON> <EMAIL>
@Create Date: 2025.04.09
@Description: 

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
"""

import requests
import json
import time

TIMEOUT = 5  # seconds
META_ACTIONS = {
    "前进": {
        "start_states": ["站着"],
        "end_state": "站着",
    },
    "后退": {
        "start_states": ["站着"],
        "end_state": "站着",
    },
    "左平移": {
        "start_states": ["站着"],
        "end_state": "站着",
    },
    "右平移": {
        "start_states": ["站着"],
        "end_state": "站着",
    },
    "左转": {
        "start_states": ["站着"],
        "end_state": "站着",
    },
    "右转": {
        "start_states": ["站着"],
        "end_state": "站着",
    },
    "起立": {
        "start_states": ["趴着"],
        "end_state": "站着",
    },
    "趴下": {
        "start_states": ["站着"],
        "end_state": "趴着",
    },
    # "打招呼": {
    #     "start_states": ["站着", "趴着"],
    #     "end_state": "站着",
    # },
    # "跳舞": {
    #     "start_states": ["站着"],
    #     "end_state": "站着",
    # },
    # "扭身体": {
    #     "start_states": ["站着"],
    #     "end_state": "站着",
    # },
    # "向前跳": {
    #     "start_states": ["站着"],
    #     "end_state": "站着",
    # },
}

EXAMPLES = [
    # [
    #     ["用户", "你今天心情怎么样"],
    #     ["小犀", "[打招呼]"]
    # ],
    # [
    #     ["用户", "你会扭秧歌吗"],
    #     ["小犀", "[左转, 右转, 扭身体]"]
    # ],
    # [
    #     ["用户", "你能给我跳支舞吗"],
    #     ["小犀", "[跳舞]"]
    # ],
    # [
    #     ["用户", "小犀好可爱啊"],
    #     ["小犀", "[扭身体]"]
    # ],
    [
        ["用户", "小犀跑两步"],
        ["小犀", "[前进, 前进]"]
    ],
    [
        ["用户", "转个圈"],
        ["小犀", "[左转, 左转, 左转]"]
    ],
    # [
    #     ["用户", "小犀撒个娇"],
    #     ["小犀", "[趴下, 起立, 扭身体]"]
    # ],
    # [
    #     ["用户", "小犀求抱抱"],
    #     ["小犀", "[向前跳]"]
    # ],
    [
        ["用户", "别动"],
        ["小犀", "[无动作]"]
    ]
]

def trigger_query():
    start_time = time.time()
    data = {
        "content": "往前跑两步", #修改为调用的文本输入，如：input_string
        "current_state": "站着", #修改为当前状态，需要是当前调用的状态
        "examples": EXAMPLES,
        "meta_actions": META_ACTIONS,
    }
    timeout = TIMEOUT
    try:
        response = requests.post("http://proxy-7861-heqingrong7-notebook-01.kuplus.jd.com/arrange_action", json=data, timeout=timeout)

        #response = requests.post("http://outer-voicechat.jd.com/soulmate/arrange_action", json=data, timeout=timeout)
        response.raise_for_status()  # Raise an error for bad responses
        data = response.json()
        json_str = json.dumps(data, ensure_ascii=False)
    except requests.exceptions.Timeout:
        print(f"Request timed out after {timeout} seconds")
        data = {
            "actions": [
                "请求超时"
            ],
            "is_valid": False
        }
        json_str = json.dumps(data, ensure_ascii=False)
    print(json_str)
    print(f"Time taken: {time.time() - start_time:.2f} seconds")
    return json_str
    
if __name__ == '__main__':
    trigger_query()
    
