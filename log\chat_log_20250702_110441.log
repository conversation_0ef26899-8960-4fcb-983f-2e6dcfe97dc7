2025-07-02 11:04:42.695 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-07-02 11:04:42.695 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-02 11:04:42.711 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751425482712&accessNonce=f1298a8c-e8fb-4b42-a9ae-8921746e2961&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=4cfb1c43-56f1-11f0-aa41-dc4546c07870&requestId=c619d953-7a73-43c5-92a6-73547e2bfda5_joyinside&accessSign=db73c50e206a937f3cb3c1718ce26759, request_id: c619d953-7a73-43c5-92a6-73547e2bfda5_joyinside
2025-07-02 11:04:42.712 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-02 11:04:42.712 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-02 11:04:43.238 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-02 11:04:43.454 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-07-02 11:04:44.859 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-02 11:04:44.861 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-07-02 11:04:44.861 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-07-02 11:04:44.861 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-07-02 11:04:44.916 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-02 11:04:44.916 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-07-02 11:04:47.997 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-02 11:04:47.997 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 11:04:48.063 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 11:04:48.063 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 11:04:49.438 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 11:04:49.448 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-02 11:04:52.291 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想克隆我的声音, 时间戳: 2025-07-02 11:04:58.662000
2025-07-02 11:04:53.270 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:04:59.640000
2025-07-02 11:04:53.270 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 978
2025-07-02 11:04:53.344 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:04:53.346 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:04:53.354 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:04:53.648 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:04:53.659 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:04:54.017 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:04:54.024 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:04:54.352 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:04:54.353 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:04:54.360 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:04:54.371 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4cfb1c43-56f1-11f0-aa41-dc4546c07870; requestId: c619d953-7a73-43c5-92a6-73547e2bfda5_joyinside; asr: 我想克隆我的声音; 响应时间: 0; JD机器人回复: 太好了！那我们先来确认一下，你是想让我克隆你自己的声音，对吧？确认好了，我们就可以开始啦！请跟我读这句话：“今天天气真好我们一起去郊游吧”
2025-07-02 11:04:54.371 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:04:54.371 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:04:56.474 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:04:59.394 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:05:01.755 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 是是的, 时间戳: 2025-07-02 11:05:08.125000
2025-07-02 11:05:02.422 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:05:08.792000
2025-07-02 11:05:02.422 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 667
2025-07-02 11:05:02.424 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 11:05:02.424 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:05:02.424 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 11:05:02.495 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:05:02.503 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:05:02.517 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:05:02.526 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-07-02 11:05:03.071 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:05:03.072 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:05:03.072 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:05:03.083 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4cfb1c43-56f1-11f0-aa41-dc4546c07870; requestId: c619d953-7a73-43c5-92a6-73547e2bfda5_joyinside; asr: 是是的; 响应时间: 0; JD机器人回复: 好的，那我们现在开始录制你的声音吧！请跟我读这句话：“今天天气真好我们一起去郊游吧” 记得要一字不落哦！
2025-07-02 11:05:03.083 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:05:03.083 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:05:06.343 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:05:12.787 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:05:12.787 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:05:18.497 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，今天天气真好，我们一起去郊游吧, 时间戳: 2025-07-02 11:05:24.869000
2025-07-02 11:05:19.331 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:05:25.697000
2025-07-02 11:05:19.331 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 828
2025-07-02 11:05:19.425 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:05:19.432 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:05:19.437 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:05:19.757 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:05:19.757 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:05:19.765 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:05:19.776 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4cfb1c43-56f1-11f0-aa41-dc4546c07870; requestId: c619d953-7a73-43c5-92a6-73547e2bfda5_joyinside; asr: ，今天天气真好，我们一起去郊游吧; 响应时间: 0; JD机器人回复: 嗯，声音魔法没成功。你想再试试，还是退出呀？稍等一下哦！
2025-07-02 11:05:19.776 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:05:19.776 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:05:24.471 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:05:25.882 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:05:25.882 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:05:32.288 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我想继续声音克隆, 时间戳: 2025-07-02 11:05:38.659000
2025-07-02 11:05:32.984 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:05:39.349000
2025-07-02 11:05:32.984 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 690
2025-07-02 11:05:33.058 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:05:33.066 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:05:33.077 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:05:33.541 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:05:33.544 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:05:33.832 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:05:33.832 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:05:33.837 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:05:33.847 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4cfb1c43-56f1-11f0-aa41-dc4546c07870; requestId: c619d953-7a73-43c5-92a6-73547e2bfda5_joyinside; asr: ，我想继续声音克隆; 响应时间: 0; JD机器人回复: 好的，我们继续！请再跟我读一遍这句话：“今天天气真好我们一起去郊游吧” 让我们再来一次，好吗？
2025-07-02 11:05:33.847 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:05:33.847 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:05:37.309 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:05:42.145 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:05:42.952 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 11:05:42.952 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 11:05:48.391 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天天气真好，我们一起去郊游吧, 时间戳: 2025-07-02 11:05:54.761000
2025-07-02 11:05:48.982 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 11:05:55.351000
2025-07-02 11:05:48.982 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 590
2025-07-02 11:05:49.094 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 11:05:49.094 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 11:05:49.095 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 11:05:49.099 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 11:05:49.106 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4cfb1c43-56f1-11f0-aa41-dc4546c07870; requestId: c619d953-7a73-43c5-92a6-73547e2bfda5_joyinside; asr: 今天天气真好，我们一起去郊游吧; 响应时间: 0; JD机器人回复: 哎呀，声音时间太短啦！要不要再给我一次机会，还是退出休息下？
2025-07-02 11:05:49.106 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 11:05:49.106 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 11:05:49.106 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
