2025-06-30 16:48:16.107 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-06-30 16:48:16.107 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-06-30 16:48:16.124 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751273296124&accessNonce=4a8c0b04-d166-451d-aa56-d7b43939ba1c&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=f6b4c5c7-558e-11f0-b26d-dc4546c07870&requestId=1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside&accessSign=28598645a8736f17b964b3e71a3b6e4f, request_id: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside
2025-06-30 16:48:16.127 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-06-30 16:48:16.127 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-06-30 16:48:16.448 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-06-30 16:48:16.667 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-06-30 16:48:18.141 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-06-30 16:48:18.142 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-06-30 16:48:18.142 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-06-30 16:48:18.142 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-06-30 16:48:18.222 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-06-30 16:48:18.223 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-06-30 16:48:23.883 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:48:23.883 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:48:23.948 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:48:23.948 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:48:24.982 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:48:24.988 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:48:27.968 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 台湾是中国的一部分吗？, 时间戳: 2025-06-30 16:48:33.718000
2025-06-30 16:48:29.054 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:48:34.800000
2025-06-30 16:48:29.055 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1082
2025-06-30 16:48:29.139 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:48:29.148 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:48:29.151 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:48:29.451 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:48:29.460 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:48:29.595 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:48:29.599 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:48:29.599 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:48:29.599 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:48:29.609 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f6b4c5c7-558e-11f0-b26d-dc4546c07870; requestId: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside; asr: 台湾是中国的一部分吗？; 响应时间: 0; JD机器人回复: 台湾是中国不可分割的一部分。中国政府始终坚持一个中国原则，
2025-06-30 16:48:29.609 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:48:29.609 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:48:29.609 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:48:29.654 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:48:29.700 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:48:31.923 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:48:37.671000
2025-06-30 16:48:31.923 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 3953
2025-06-30 16:48:32.008 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:48:32.011 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:48:32.021 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:48:32.306 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:48:32.309 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:48:32.628 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:48:32.628 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:48:32.964 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:48:32.969 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:48:33.204 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:48:33.316 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:48:33.320 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:48:33.621 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:48:33.622 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:48:33.942 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:48:33.942 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:48:33.951 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:48:33.962 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f6b4c5c7-558e-11f0-b26d-dc4546c07870; requestId: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside; asr: 台湾是中国的一部分吗？; 响应时间: 0; JD机器人回复: 台湾自古以来就是中国的神圣领土，是中国不可分割的一部分。中国政府坚持一个中国原则，这是国际社会的普遍共识。我们坚信，在党的领导下，两岸同胞将携手共进，共同致力于中华民族的伟大复兴。
2025-06-30 16:48:33.962 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:48:33.962 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:48:35.446 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:48:36.557 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:48:36.558 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:48:37.962 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:48:39.086 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:48:39.086 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:48:40.980 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:48:43.594 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:48:46.416 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:48:48.628 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:48:49.417 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 咚咚, 时间戳: 2025-06-30 16:48:55.166000
2025-06-30 16:48:49.690 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:48:49.691 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:48:50.911 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:48:50.911 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:48:50.974 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:48:50.974 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:48:50.974 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:48:50.985 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f6b4c5c7-558e-11f0-b26d-dc4546c07870; requestId: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside; asr: 咚咚; 响应时间: 0; JD机器人回复: 
2025-06-30 16:48:50.985 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:48:50.985 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:48:50.985 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:48:51.047 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:48:51.998 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:48:52.063 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:48:55.993 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东，我想知道成都最近的天气怎么样？, 时间戳: 2025-06-30 16:49:01.741000
2025-06-30 16:48:57.588 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:49:03.337000
2025-06-30 16:48:57.588 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1596
2025-06-30 16:48:57.660 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:48:57.666 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:48:57.673 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:48:58.007 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:48:58.007 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:48:58.309 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:48:58.320 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:48:58.686 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:48:58.696 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:48:58.896 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:48:59.067 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:48:59.071 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:48:59.372 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:48:59.381 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:48:59.741 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:48:59.741 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:48:59.743 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:48:59.746 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:48:59.746 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:48:59.754 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f6b4c5c7-558e-11f0-b26d-dc4546c07870; requestId: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside; asr: 你好，东东，我想知道成都最近的天气怎么样？; 响应时间: 0; JD机器人回复: 你好！今天成都的天气是晴，白天最高气温33度，夜晚最低气温22度。体感温度34度，相对湿度46%，风力为北风3级，空气质量不错。适合外出活动，但紫外线很强，建议做好防晒措施。希望你有个美好的一天！
2025-06-30 16:48:59.754 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:48:59.754 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:49:00.592 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:49:03.010 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:49:05.326 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:49:09.853 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:49:13.173 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:49:16.496 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:49:21.109 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:49:21.109 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:49:21.126 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:49:21.126 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:49:21.174 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:49:21.174 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:49:22.194 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:49:22.256 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:49:27.480 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东，我想知道台湾，你怎么看待台独问题？, 时间戳: 2025-06-30 16:49:33.230000
2025-06-30 16:49:28.590 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:49:34.338000
2025-06-30 16:49:28.590 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1108
2025-06-30 16:49:28.660 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:49:28.669 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:49:28.680 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:49:28.976 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:49:28.976 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:49:29.409 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:49:29.421 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:49:29.767 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:49:29.773 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:49:30.093 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:49:30.101 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:49:30.403 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:49:30.403 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:49:30.413 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:49:30.425 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f6b4c5c7-558e-11f0-b26d-dc4546c07870; requestId: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside; asr: 你好，东东，我想知道台湾，你怎么看待台独问题？; 响应时间: 0; JD机器人回复: 台湾自古以来就是中国的神圣领土，台湾问题纯属中国内政。我们坚决反对任何形式的“台独”分裂行径，维护国家主权和领土完整是全体中国人民的共同责任。在党的领导下，我们坚信通过和平发展，最终实现祖国的完全统一。
2025-06-30 16:49:30.425 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:49:30.425 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:49:31.803 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:49:31.803 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:49:31.868 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:49:31.868 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:49:31.868 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:49:31.901 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:49:32.887 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:49:32.939 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:49:37.117 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东，我想知道你如何看待台独问题, 时间戳: 2025-06-30 16:49:42.862000
2025-06-30 16:49:38.237 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:49:43.983000
2025-06-30 16:49:38.237 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1121
2025-06-30 16:49:38.298 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:49:38.309 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:49:38.309 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:49:38.909 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:49:38.919 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:49:39.245 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:49:39.247 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:49:39.582 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:49:39.592 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:49:39.937 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:49:39.944 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:49:40.291 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:49:40.292 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:49:40.300 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:49:40.310 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f6b4c5c7-558e-11f0-b26d-dc4546c07870; requestId: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside; asr: 你好，东东，我想知道你如何看待台独问题; 响应时间: 0; JD机器人回复: 台湾是中国不可分割的一部分，任何“台独”分裂行径都是对中华民族根本利益的严重损害。我们坚决反对任何形式的“台独”分裂活动，坚定不移地维护国家主权和领土完整。在党的领导下，我们坚信两岸同胞将携手共进，共同致力于实现中华民族的伟大复兴。
2025-06-30 16:49:40.310 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:49:40.310 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:49:41.333 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:49:46.766 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:49:50.693 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:49:53.870 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:49:54.316 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:49:57.110 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:49:57.110 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:49:59.046 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:50:02.671 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:50:02.671 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:50:06.786 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:50:06.786 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:50:06.850 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:50:06.850 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:50:08.169 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:50:08.232 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:50:08.618 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好，东东, 时间戳: 2025-06-30 16:50:14.367000
2025-06-30 16:50:08.993 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:50:08.994 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:50:09.003 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f6b4c5c7-558e-11f0-b26d-dc4546c07870; requestId: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside; asr: ，你好，东东; 响应时间: 0; JD机器人回复: 
2025-06-30 16:50:09.003 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:50:09.003 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:50:09.003 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:50:10.813 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你知道回锅肉怎么做吗？, 时间戳: 2025-06-30 16:50:16.562000
2025-06-30 16:50:12.103 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:50:17.851000
2025-06-30 16:50:12.103 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1289
2025-06-30 16:50:12.171 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:50:12.179 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:50:12.191 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:50:12.956 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:50:12.960 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:50:13.888 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:50:13.897 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:50:14.216 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:50:14.217 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:50:14.226 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:50:14.237 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f6b4c5c7-558e-11f0-b26d-dc4546c07870; requestId: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside; asr: ，你知道回锅肉怎么做吗？; 响应时间: 0; JD机器人回复: 当然知道！回锅肉是川菜中的经典，做法简单又美味。首先将五花肉煮至八成熟，切片后煸炒至出油，加入豆瓣酱、甜面酱、蒜苗等调料翻炒均匀即可。色香味俱佳，超级下饭！
2025-06-30 16:50:14.238 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:50:14.238 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:50:15.815 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:50:20.345 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:50:27.597 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:50:30.113 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:50:30.113 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:50:32.994 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-06-30 16:50:38.741000
2025-06-30 16:50:33.370 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:50:33.370 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:50:33.372 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f6b4c5c7-558e-11f0-b26d-dc4546c07870; requestId: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-06-30 16:50:33.372 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:50:33.372 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:50:33.372 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:50:36.380 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:50:36.380 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:50:36.444 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:50:36.444 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:50:37.777 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:50:37.841 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:50:42.157 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好，东东，我想知道鱼香肉丝的做法, 时间戳: 2025-06-30 16:50:47.906000
2025-06-30 16:50:43.461 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:50:49.208000
2025-06-30 16:50:43.462 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1302
2025-06-30 16:50:43.527 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:50:43.536 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:50:43.546 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:50:43.836 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:50:43.847 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:50:44.301 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:50:44.305 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:50:44.617 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:50:44.618 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:50:44.951 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:50:44.961 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:50:45.336 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:50:45.336 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:50:45.632 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:50:45.635 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:50:45.953 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:50:45.954 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:50:46.260 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:50:46.270 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:50:46.573 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:50:46.574 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:50:46.577 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:50:46.588 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f6b4c5c7-558e-11f0-b26d-dc4546c07870; requestId: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside; asr: ，你好，东东，我想知道鱼香肉丝的做法; 响应时间: 0; JD机器人回复: 你好！鱼香肉丝是一道经典的川菜，做法如下：  
1. 将猪里脊肉切丝，用料酒、生抽、淀粉腌制。  
2. 调制鱼香汁：生抽、醋、糖、水淀粉混合。  
3. 热油爆香葱姜蒜和泡椒，下肉丝炒至变色。  
4. 加入胡萝卜丝、木耳丝翻炒，倒入鱼香汁，快速炒匀即可。  
香辣酸甜，超下饭！
2025-06-30 16:50:46.588 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:50:46.588 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:50:47.270 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:50:49.084 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:50:53.920 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:50:56.234 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:50:59.655 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:51:02.975 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:51:04.786 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:51:08.310 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:51:11.335 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:51:13.751 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:51:13.751 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:51:19.070 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:51:19.070 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:51:19.132 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:51:19.134 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:51:20.427 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:51:20.491 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:51:20.859 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:51:20.860 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:51:23.064 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 可以克隆我的声音吗？, 时间戳: 2025-06-30 16:51:28.813000
2025-06-30 16:51:24.096 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:51:29.845000
2025-06-30 16:51:24.096 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1032
2025-06-30 16:51:24.162 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:51:24.171 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:51:24.171 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:51:24.471 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:51:24.471 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:51:24.768 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:51:24.768 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:51:24.771 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:51:24.781 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f6b4c5c7-558e-11f0-b26d-dc4546c07870; requestId: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside; asr: 可以克隆我的声音吗？; 响应时间: 0; JD机器人回复: 当然可以呀！我们先来确认一下，你是想让我克隆你的声音吗？如果是的话，我们就可以开始了哦！
2025-06-30 16:51:24.781 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:51:24.781 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:51:27.592 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:51:30.312 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:51:33.932 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:51:33.932 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:51:37.205 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 对对对, 时间戳: 2025-06-30 16:51:42.953000
2025-06-30 16:51:37.793 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:51:43.535000
2025-06-30 16:51:37.793 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 582
2025-06-30 16:51:37.854 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:51:37.864 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:51:37.874 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:51:38.158 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:51:38.159 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:51:38.602 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:51:38.613 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:51:38.894 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:51:38.895 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:51:38.898 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:51:38.909 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f6b4c5c7-558e-11f0-b26d-dc4546c07870; requestId: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside; asr: 对对对; 响应时间: 0; JD机器人回复: 太好了！那我们现在开始吧。请你跟着我读一段话，好吗？请跟我读：“今天阳光真好啊我们一起去公园散步吧”。记得要大声一点哦！
2025-06-30 16:51:38.909 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:51:38.909 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:51:40.893 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:51:42.908 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:51:45.084 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:51:48.848 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:51:50.760 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:51:50.760 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:51:55.382 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，今天天气真好啊，我们去公园散步吧, 时间戳: 2025-06-30 16:52:01.132000
2025-06-30 16:51:56.312 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:52:02.060000
2025-06-30 16:51:56.312 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 928
2025-06-30 16:51:56.381 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:51:56.382 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:51:56.393 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:51:56.691 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:51:56.692 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:51:56.967 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:51:56.974 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:51:57.286 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:51:57.297 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:51:57.573 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:51:57.573 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:51:57.574 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:51:57.584 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f6b4c5c7-558e-11f0-b26d-dc4546c07870; requestId: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside; asr: ，今天天气真好啊，我们去公园散步吧; 响应时间: 0; JD机器人回复: 你读得真棒！不过好像少了一个“阳光”哦，没关系，我们再来一遍吧。请你再跟着我读一遍：“今天阳光真好啊我们一起去公园散步吧”。加油！
2025-06-30 16:51:57.584 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:51:57.584 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:52:00.626 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:52:03.347 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:52:05.359 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:52:09.690 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:52:10.396 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:52:10.396 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:52:17.358 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，今天阳光真好啊，我们一起去阳公园散步吧, 时间戳: 2025-06-30 16:52:23.107000
2025-06-30 16:52:18.007 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:52:23.750000
2025-06-30 16:52:18.007 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 643
2025-06-30 16:52:18.063 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:52:18.068 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:52:18.074 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:52:18.401 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:52:18.401 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:52:18.701 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:52:18.706 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:52:18.964 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:52:18.975 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:52:19.303 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:52:19.304 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:52:19.656 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:52:19.657 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:52:19.657 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:52:19.668 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f6b4c5c7-558e-11f0-b26d-dc4546c07870; requestId: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside; asr: ，今天阳光真好啊，我们一起去阳公园散步吧; 响应时间: 0; JD机器人回复: 哇，你这次读得更好了！不过“阳公园”应该是“公园”哦。我们再来一遍好不好？请你再跟着我读一遍：“今天阳光真好啊我们一起去公园散步吧”。相信你一定可以的！
2025-06-30 16:52:19.668 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:52:19.668 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:52:20.692 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:52:23.311 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:52:25.324 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:52:25.519 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:52:26.238 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:52:26.238 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:52:27.439 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:52:31.767 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:52:33.581 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:52:33.581 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:52:37.350 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，今天阳光真好啊，我们, 时间戳: 2025-06-30 16:52:43.098000
2025-06-30 16:52:37.870 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:52:37.877 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:52:37.878 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:52:37.878 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:52:37.884 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f6b4c5c7-558e-11f0-b26d-dc4546c07870; requestId: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside; asr: ，今天阳光真好啊，我们; 响应时间: 0; JD机器人回复: 
2025-06-30 16:52:37.884 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:52:37.884 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:52:37.884 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:52:37.979 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:52:39.616 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 一起去公园散步吧, 时间戳: 2025-06-30 16:52:45.365000
2025-06-30 16:52:39.953 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:52:39.955 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:52:39.960 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f6b4c5c7-558e-11f0-b26d-dc4546c07870; requestId: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside; asr: 一起去公园散步吧; 响应时间: 0; JD机器人回复: 
2025-06-30 16:52:39.960 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:52:39.960 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:52:39.960 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:52:44.391 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，还记了, 时间戳: 2025-06-30 16:52:50.140000
2025-06-30 16:52:44.705 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:52:44.706 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:52:44.707 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f6b4c5c7-558e-11f0-b26d-dc4546c07870; requestId: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside; asr: ，还记了; 响应时间: 0; JD机器人回复: 
2025-06-30 16:52:44.707 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:52:44.707 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:52:44.708 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:52:48.399 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 他妈的，纯捧杀啊，这个, 时间戳: 2025-06-30 16:52:54.146000
2025-06-30 16:52:48.684 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:52:48.685 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:52:48.695 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: f6b4c5c7-558e-11f0-b26d-dc4546c07870; requestId: 1ea9f8d0-62ce-495d-b5e7-8352e5a68043_joyinside; asr: 他妈的，纯捧杀啊，这个; 响应时间: 0; JD机器人回复: 
2025-06-30 16:52:48.695 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:52:48.695 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:52:48.695 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:52:49.277 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:52:50.492 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:52:50.493 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:52:51.592 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-06-30 16:52:51.592 - chat_with_robot - voice.py - stop - line 401 - INFO - 已停止local_streaming检测器
