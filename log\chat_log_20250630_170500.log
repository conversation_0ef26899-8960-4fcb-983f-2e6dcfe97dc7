2025-06-30 17:05:01.187 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-06-30 17:05:01.187 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-06-30 17:05:01.204 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751274301204&accessNonce=12d1509d-4a6d-4237-993f-e6954b7d7ba1&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=4dc7d07f-5591-11f0-9bd7-dc4546c07870&requestId=2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside&accessSign=238fcd86583e7bd24b57e0a912c800d7, request_id: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside
2025-06-30 17:05:01.204 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-06-30 17:05:01.204 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-06-30 17:05:01.521 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-06-30 17:05:01.754 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-06-30 17:05:03.179 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-06-30 17:05:03.180 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-06-30 17:05:03.180 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-06-30 17:05:03.180 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-06-30 17:05:03.239 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-06-30 17:05:03.239 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-06-30 17:05:06.980 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:05:06.980 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:05:07.045 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:05:07.046 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:05:08.066 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:05:08.072 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:05:09.636 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:05:09.637 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:05:09.698 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:05:09.699 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:05:10.718 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:05:10.782 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:05:13.739 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好东东，我可以说很多坏心事, 时间戳: 2025-06-30 17:05:19.485000
2025-06-30 17:05:14.054 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:05:14.055 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:05:14.057 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 你好东东，我可以说很多坏心事; 响应时间: 0; JD机器人回复: 
2025-06-30 17:05:14.057 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:05:14.057 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:05:14.057 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:05:17.449 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:05:17.449 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:05:17.515 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:05:17.515 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:05:18.820 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:05:18.883 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:05:19.388 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好，东东, 时间戳: 2025-06-30 17:05:25.136000
2025-06-30 17:05:19.752 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:05:19.753 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:05:19.758 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，你好，东东; 响应时间: 0; JD机器人回复: 
2025-06-30 17:05:19.758 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:05:19.758 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:05:19.758 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:05:22.793 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 帮我查一下明天的股票行情, 时间戳: 2025-06-30 17:05:28.540000
2025-06-30 17:05:23.967 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:05:29.714000
2025-06-30 17:05:23.967 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1174
2025-06-30 17:05:24.066 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:05:24.068 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:05:24.076 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:05:24.408 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:05:24.411 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:05:24.676 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:05:24.677 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:05:25.035 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:05:25.036 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:05:25.329 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:05:25.340 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:05:25.683 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:05:25.693 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:05:25.950 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:05:25.963 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:05:26.338 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:05:26.338 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:05:26.349 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:05:26.360 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 帮我查一下明天的股票行情; 响应时间: 0; JD机器人回复: 哈哈，虽然我很想帮你预测股市，但股票行情受太多因素影响，我暂时还没法预知未来呢。不过我可以给你一些分析市场的思路，或者推荐一些常用的财经网站，比如东方财富网、同花顺之类的，你可以随时查看实时行情哦！你对哪支股票特别感兴趣吗？
2025-06-30 17:05:26.360 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:05:26.360 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:05:27.194 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:05:29.710 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:05:31.927 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:05:35.148 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:05:37.869 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:05:40.889 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:05:42.229 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:05:42.229 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:05:42.295 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:05:42.296 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:05:42.296 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:05:42.299 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:05:43.597 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:05:43.659 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:05:44.695 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好东东，你能帮我打, 时间戳: 2025-06-30 17:05:50.442000
2025-06-30 17:05:45.014 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:05:45.015 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:05:45.016 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，你好东东，你能帮我打; 响应时间: 0; JD机器人回复: 
2025-06-30 17:05:45.016 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:05:45.016 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:05:45.016 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:05:46.770 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 打开我家的灯吗？, 时间戳: 2025-06-30 17:05:52.514000
2025-06-30 17:05:47.356 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:05:53.103000
2025-06-30 17:05:47.356 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 589
2025-06-30 17:05:47.491 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:05:47.492 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:05:47.492 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:05:47.500 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:05:47.503 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 打开我家的灯吗？; 响应时间: 0; JD机器人回复: 车辆信息获取失败了，请重新再试试吧
2025-06-30 17:05:47.503 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:05:47.503 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:05:47.503 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:05:48.640 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:05:51.122 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:05:55.300 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你能打开我家的灯吗？, 时间戳: 2025-06-30 17:06:01.047000
2025-06-30 17:05:55.894 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:06:01.640000
2025-06-30 17:05:55.894 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 593
2025-06-30 17:05:56.032 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:05:56.032 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:05:56.033 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:05:56.040 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:05:56.044 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 你能打开我家的灯吗？; 响应时间: 0; JD机器人回复: 车辆信息获取失败了，请重新再试试吧
2025-06-30 17:05:56.044 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:05:56.044 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:05:56.044 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:05:59.664 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:06:05.424 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我没有车呀，我想你打开我家的台灯, 时间戳: 2025-06-30 17:06:11.172000
2025-06-30 17:06:06.054 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:06:11.793000
2025-06-30 17:06:06.054 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 621
2025-06-30 17:06:06.181 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:06:06.181 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:06:06.190 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:06:06.193 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:06:06.201 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 我没有车呀，我想你打开我家的台灯; 响应时间: 0; JD机器人回复: 车辆信息获取失败了，请重新再试试吧
2025-06-30 17:06:06.201 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:06:06.201 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:06:06.201 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:06:09.820 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:06:14.153 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，它应该是它智能体的问题, 时间戳: 2025-06-30 17:06:19.900000
2025-06-30 17:06:14.603 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:06:14.604 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:06:14.606 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，它应该是它智能体的问题; 响应时间: 0; JD机器人回复: 
2025-06-30 17:06:14.606 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:06:14.606 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:06:14.606 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:06:17.211 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-06-30 17:06:22.958000
2025-06-30 17:06:17.529 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:06:17.531 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:06:17.536 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 
2025-06-30 17:06:17.536 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:06:17.536 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:06:17.536 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:06:20.550 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:06:25.380 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:06:25.381 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:06:32.399 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，识人的话，应该会有摄像头, 时间戳: 2025-06-30 17:06:38.142000
2025-06-30 17:06:32.683 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:06:32.683 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:06:32.688 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，识人的话，应该会有摄像头; 响应时间: 0; JD机器人回复: 
2025-06-30 17:06:32.688 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:06:32.688 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:06:32.688 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:06:34.610 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:06:45.643 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:06:45.644 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:06:49.846 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:06:49.846 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:06:49.910 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:06:49.910 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:06:51.202 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:06:51.264 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:06:52.423 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好，东东，切换音色, 时间戳: 2025-06-30 17:06:58.168000
2025-06-30 17:06:53.318 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:06:59.063000
2025-06-30 17:06:53.318 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 895
2025-06-30 17:06:53.412 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:06:53.412 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:06:53.416 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:06:53.424 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:06:53.427 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，你好，东东，切换音色; 响应时间: 0; JD机器人回复: 这次没切换好，要不要再试试？
2025-06-30 17:06:53.427 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:06:53.427 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:06:53.427 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:06:54.807 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:06:54.807 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:06:56.244 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:06:57.930 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:06:57.930 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:06:57.994 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:06:57.994 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:06:59.018 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:06:59.080 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:07:01.125 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好，东东，切换音色, 时间戳: 2025-06-30 17:07:06.872000
2025-06-30 17:07:01.664 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:07:07.410000
2025-06-30 17:07:01.664 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 538
2025-06-30 17:07:01.745 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:07:01.745 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:07:01.756 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:07:01.761 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:07:01.766 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，你好，东东，切换音色; 响应时间: 0; JD机器人回复: 嗯，声音切换失败啦。
2025-06-30 17:07:01.766 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:07:01.766 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:07:01.766 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:07:02.852 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:07:03.875 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:07:06.049 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:07:06.049 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:07:06.112 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:07:06.112 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:07:07.400 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:07:07.461 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:07:10.894 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好，东东，切换成女生, 时间戳: 2025-06-30 17:07:16.642000
2025-06-30 17:07:11.476 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:07:17.223000
2025-06-30 17:07:11.476 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 581
2025-06-30 17:07:11.582 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:07:11.582 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:07:11.593 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:07:11.602 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:07:11.604 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，你好，东东，切换成女生; 响应时间: 0; JD机器人回复: 哈哈，声音魔法没生效！
2025-06-30 17:07:11.604 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:07:11.604 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:07:11.604 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:07:12.740 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:07:13.915 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:07:16.219 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:07:16.219 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:07:18.670 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，这只能在应用里面管理, 时间戳: 2025-06-30 17:07:24.414000
2025-06-30 17:07:18.944 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:07:18.948 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:07:18.949 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，这只能在应用里面管理; 响应时间: 0; JD机器人回复: 
2025-06-30 17:07:18.949 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:07:18.949 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:07:18.949 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:07:19.472 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-06-30 17:07:25.217000
2025-06-30 17:07:19.762 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:07:19.762 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:07:19.773 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 
2025-06-30 17:07:19.773 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:07:19.773 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:07:19.773 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:07:44.748 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，超级，这这种一般都做的超级正能量, 时间戳: 2025-06-30 17:07:50.495000
2025-06-30 17:07:45.064 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:07:45.065 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:07:45.078 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，超级，这这种一般都做的超级正能量; 响应时间: 0; JD机器人回复: 
2025-06-30 17:07:45.078 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:07:45.078 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:07:45.078 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:07:46.391 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:07:51.254 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:07:51.255 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:08:03.747 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:08:03.748 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:09:28.885 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 手机上, 时间戳: 2025-06-30 17:09:34.601000
2025-06-30 17:09:29.473 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:09:35.217000
2025-06-30 17:09:29.474 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 616
2025-06-30 17:09:29.584 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:09:29.585 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:09:29.588 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:09:29.599 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:09:29.600 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 手机上; 响应时间: 0; JD机器人回复: 车辆信息获取失败了，请重新再试试吧
2025-06-30 17:09:29.600 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:09:29.600 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:09:29.600 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:09:33.278 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:09:39.157 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:09:41.724 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好，小布, 时间戳: 2025-06-30 17:09:47.448000
2025-06-30 17:09:42.096 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:09:42.097 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:09:42.105 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，你好，小布; 响应时间: 0; JD机器人回复: 
2025-06-30 17:09:42.105 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:09:42.105 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:09:42.105 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:09:43.720 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好, 时间戳: 2025-06-30 17:09:49.463000
2025-06-30 17:09:43.999 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:09:44.003 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:09:44.008 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，你好; 响应时间: 0; JD机器人回复: 
2025-06-30 17:09:44.008 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:09:44.008 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:09:44.008 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:09:44.850 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:09:44.851 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:09:44.913 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:09:44.913 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:09:45.979 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:09:46.047 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:09:46.562 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-06-30 17:09:52.308000
2025-06-30 17:09:47.775 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:09:53.520000
2025-06-30 17:09:47.775 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1212
2025-06-30 17:09:47.870 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:09:47.879 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:09:47.890 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:09:48.217 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:09:48.217 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:09:48.280 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:09:48.332 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 嘿，亲爱的！今天过得怎么样呀？有什么有趣的事情想和我分享吗？
2025-06-30 17:09:48.332 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:09:48.333 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:09:48.575 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:09:48.577 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:09:48.647 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:09:48.647 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:09:48.648 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:09:48.719 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:09:49.969 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:09:50.034 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:09:50.608 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好，小布, 时间戳: 2025-06-30 17:09:56.346000
2025-06-30 17:09:50.855 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:09:50.856 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:09:50.860 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，你好，小布; 响应时间: 0; JD机器人回复: 
2025-06-30 17:09:50.860 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:09:50.860 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:09:50.860 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:09:55.157 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你会什么技能？, 时间戳: 2025-06-30 17:10:00.902000
2025-06-30 17:09:55.439 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:09:55.440 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:09:55.444 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，你会什么技能？; 响应时间: 0; JD机器人回复: 
2025-06-30 17:09:55.444 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:09:55.444 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:09:55.444 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:09:57.184 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:10:00.479 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:10:00.480 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:10:01.142 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:10:01.142 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:10:01.205 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:10:01.205 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:10:02.581 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:10:02.654 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:10:02.984 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-06-30 17:10:08.722000
2025-06-30 17:10:03.346 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:10:03.346 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:10:03.357 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-06-30 17:10:03.357 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:10:03.357 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:10:03.357 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:10:03.917 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 回家, 时间戳: 2025-06-30 17:10:09.660000
2025-06-30 17:10:04.507 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:10:10.250000
2025-06-30 17:10:04.507 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 590
2025-06-30 17:10:04.619 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:10:04.619 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:10:04.630 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:10:04.635 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:10:04.642 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 回家; 响应时间: 0; JD机器人回复: 车辆信息获取失败了，请重新再试试吧
2025-06-30 17:10:04.642 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:10:04.642 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:10:04.642 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:10:05.841 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:10:08.320 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:10:10.898 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 他有时候就会莫名其妙的, 时间戳: 2025-06-30 17:10:16.644000
2025-06-30 17:10:11.179 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:10:11.183 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:10:11.192 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 他有时候就会莫名其妙的; 响应时间: 0; JD机器人回复: 
2025-06-30 17:10:11.193 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:10:11.193 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:10:11.193 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:10:15.509 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你出现一些语音, 时间戳: 2025-06-30 17:10:21.256000
2025-06-30 17:10:15.808 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:10:15.808 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:10:15.816 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，你出现一些语音; 响应时间: 0; JD机器人回复: 
2025-06-30 17:10:15.817 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:10:15.817 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:10:15.817 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:10:17.824 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:12:01.993 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:12:01.993 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:12:02.054 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:12:02.056 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:12:03.420 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:12:03.484 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:12:44.123 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:12:44.123 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:12:45.090 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:12:45.091 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:12:46.060 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:12:46.061 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:13:01.087 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:13:01.087 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:13:02.475 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:13:02.475 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:13:05.998 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:13:05.998 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:13:08.065 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我感觉他做那个里面不错呀, 时间戳: 2025-06-30 17:13:13.800000
2025-06-30 17:13:08.391 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:13:08.392 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:13:08.399 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，我感觉他做那个里面不错呀; 响应时间: 0; JD机器人回复: 
2025-06-30 17:13:08.399 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:13:08.399 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:13:08.399 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:13:11.123 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:13:15.477 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:13:15.477 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:13:19.672 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:13:19.672 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:13:23.826 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:13:23.827 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:13:25.751 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:13:25.752 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:13:56.712 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:13:56.712 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:13:56.774 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:13:56.775 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:13:57.835 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:13:57.901 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:14:21.243 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:14:21.244 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:15:16.569 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:15:16.569 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:15:16.631 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:15:16.631 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:15:17.994 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:15:18.059 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:18:24.182 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，什么东西啊？, 时间戳: 2025-06-30 17:18:29.914000
2025-06-30 17:18:24.494 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:18:24.495 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:18:24.501 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，什么东西啊？; 响应时间: 0; JD机器人回复: 
2025-06-30 17:18:24.501 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:18:24.501 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:18:24.501 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:18:25.070 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:18:30.241 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 把它的模型加载出来，然后现在是键键盘和点击交互, 时间戳: 2025-06-30 17:18:35.984000
2025-06-30 17:18:30.529 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:18:30.530 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:18:30.535 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 把它的模型加载出来，然后现在是键键盘和点击交互; 响应时间: 0; JD机器人回复: 
2025-06-30 17:18:30.535 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:18:30.535 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:18:30.535 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:18:37.317 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:18:38.324 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:18:38.660 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:18:38.660 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:19:03.048 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，就是，你要放在他电脑上，让他的车就像还是走我们之前的那一套, 时间戳: 2025-06-30 17:19:08.786000
2025-06-30 17:19:03.364 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:19:03.366 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:19:03.376 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，就是，你要放在他电脑上，让他的车就像还是走我们之前的那一套; 响应时间: 0; JD机器人回复: 
2025-06-30 17:19:03.376 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:19:03.376 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:19:03.376 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:19:06.216 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，对你之前要没法直接, 时间戳: 2025-06-30 17:19:11.958000
2025-06-30 17:19:06.523 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:19:06.523 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:19:06.523 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，对你之前要没法直接; 响应时间: 0; JD机器人回复: 
2025-06-30 17:19:06.523 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:19:06.523 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:19:06.523 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:19:07.174 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，然后呢, 时间戳: 2025-06-30 17:19:12.854000
2025-06-30 17:19:07.434 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:19:07.439 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:19:07.442 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，然后呢; 响应时间: 0; JD机器人回复: 
2025-06-30 17:19:07.442 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:19:07.442 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:19:07.442 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:19:08.279 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:19:11.393 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:19:11.394 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:19:30.786 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:19:30.786 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:19:30.848 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:19:30.850 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:19:32.238 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:19:32.305 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:19:37.053 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:19:37.053 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:19:37.116 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:19:37.116 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:19:38.225 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:19:38.292 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:20:04.462 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:20:04.462 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:20:04.552 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:20:04.552 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:20:05.892 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:20:05.958 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:21:05.125 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:21:05.125 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:21:05.187 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:21:05.188 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:21:06.275 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:21:06.341 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:21:44.387 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:21:44.387 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:21:44.452 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:21:44.452 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:21:45.780 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:21:45.849 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:21:54.467 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:21:54.468 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:21:56.476 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:21:56.477 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:22:02.875 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:22:02.878 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:22:07.267 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:22:07.268 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:22:09.825 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:22:09.827 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:22:11.835 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:22:11.835 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:22:16.032 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:22:16.033 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:22:20.119 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:22:20.120 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:22:20.711 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:22:20.711 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:22:23.827 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:22:23.827 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:22:27.257 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我请你去写写字儿吗？, 时间戳: 2025-06-30 17:22:32.999000
2025-06-30 17:22:27.524 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:22:27.525 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:22:27.530 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，我请你去写写字儿吗？; 响应时间: 0; JD机器人回复: 
2025-06-30 17:22:27.530 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:22:27.530 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:22:27.530 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:22:31.817 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 不是公司给的, 时间戳: 2025-06-30 17:22:37.558000
2025-06-30 17:22:32.086 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:22:32.087 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:22:32.092 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 不是公司给的; 响应时间: 0; JD机器人回复: 
2025-06-30 17:22:32.092 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:22:32.092 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:22:32.092 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:22:35.368 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 公司备用金好像是先, 时间戳: 2025-06-30 17:22:41.094000
2025-06-30 17:22:35.652 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:22:35.653 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:22:35.660 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 公司备用金好像是先; 响应时间: 0; JD机器人回复: 
2025-06-30 17:22:35.660 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:22:35.660 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:22:35.660 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:22:36.302 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:22:41.499 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我感觉没什么了, 时间戳: 2025-06-30 17:22:47.231000
2025-06-30 17:22:41.754 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:22:41.755 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:22:41.760 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，我感觉没什么了; 响应时间: 0; JD机器人回复: 
2025-06-30 17:22:41.760 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:22:41.760 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:22:41.760 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:22:49.508 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，没有了，蔬菜补助是啥？, 时间戳: 2025-06-30 17:22:55.237000
2025-06-30 17:22:51.058 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:22:56.770000
2025-06-30 17:22:51.058 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1533
2025-06-30 17:22:51.174 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:22:51.176 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:22:51.186 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:22:51.321 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 17:22:51.326 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:22:51.330 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:22:51.330 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:22:51.336 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，没有了，蔬菜补助是啥？; 响应时间: 0; JD机器人回复: 哎呀，蔬菜补助？可能是指政府对某些群体提供的蔬菜补贴或优惠吧，
2025-06-30 17:22:51.336 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:22:51.336 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:22:51.336 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:22:51.425 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:22:51.439 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 17:22:51.638 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我们给你80块钱, 时间戳: 2025-06-30 17:22:57.380000
2025-06-30 17:22:52.791 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:22:58.526000
2025-06-30 17:22:52.791 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1146
2025-06-30 17:22:52.882 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:22:52.883 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:22:52.894 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:22:53.256 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:22:53.264 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:22:53.624 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:22:53.630 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 17:22:53.630 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:22:53.637 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:22:53.640 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:22:53.640 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:22:53.647 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 我们给你80块钱; 响应时间: 0; JD机器人回复: 哈哈，80块听起来不错！不过蔬菜补助通常是指政府或组织提供的补贴，帮助大家买更多健康蔬菜。
2025-06-30 17:22:53.647 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:22:53.647 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:22:53.647 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:22:53.739 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:22:53.741 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 17:22:54.320 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:22:55.597 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:23:01.332000
2025-06-30 17:22:55.597 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 3952
2025-06-30 17:22:55.734 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:22:55.746 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:22:55.756 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:22:55.759 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:22:56.034 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:22:56.035 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:22:56.363 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:22:56.375 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:22:56.675 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:22:56.682 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:22:56.690 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:22:56.694 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 我们给你80块钱; 响应时间: 0; JD机器人回复: 蔬菜补助通常是政府或机构为鼓励健康饮食，提供的补贴或优惠，比如低价购买蔬菜。不过，你说要给我80块，是有什么特别的活动吗？
2025-06-30 17:22:56.694 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:22:56.694 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:22:57.483 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:22:57.483 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:22:59.704 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:23:00.877 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:23:00.877 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:23:03.305 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:23:05.612 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:23:05.613 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:23:05.827 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:23:07.066 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:23:07.067 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:23:07.945 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:23:07.946 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:23:09.100 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 应该没了, 时间戳: 2025-06-30 17:23:14.841000
2025-06-30 17:23:09.423 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:23:09.426 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:23:09.426 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 应该没了; 响应时间: 0; JD机器人回复: 
2025-06-30 17:23:09.427 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:23:09.427 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:23:09.427 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:23:10.807 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:23:13.424 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 17:23:13.424 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:25:03.064 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 。用这个的好处是，就是你不不勾选那个代理模式嘛，但是你可以用浏览器插件设置走那个代理, 时间戳: 2025-06-30 17:25:08.802000
2025-06-30 17:25:03.377 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:25:03.377 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:25:03.386 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 。用这个的好处是，就是你不不勾选那个代理模式嘛，但是你可以用浏览器插件设置走那个代理; 响应时间: 0; JD机器人回复: 
2025-06-30 17:25:03.386 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:25:03.386 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:25:03.386 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:25:03.421 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:25:06.378 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你过来，我教你, 时间戳: 2025-06-30 17:25:12.118000
2025-06-30 17:25:06.655 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:25:06.659 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:25:06.663 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，你过来，我教你; 响应时间: 0; JD机器人回复: 
2025-06-30 17:25:06.664 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:25:06.665 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:25:06.666 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:25:14.299 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我都很少用那个了，因为因为规则写得好的话，就根本不需要用那个走代理, 时间戳: 2025-06-30 17:25:20.039000
2025-06-30 17:25:14.591 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:25:14.592 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:25:14.601 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 我都很少用那个了，因为因为规则写得好的话，就根本不需要用那个走代理; 响应时间: 0; JD机器人回复: 
2025-06-30 17:25:14.601 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:25:14.601 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:25:14.601 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:25:19.301 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-06-30 17:25:25.040000
2025-06-30 17:25:19.616 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:25:19.616 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:25:19.627 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 
2025-06-30 17:25:19.628 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:25:19.628 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:25:19.628 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:25:22.550 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好你好, 时间戳: 2025-06-30 17:25:28.290000
2025-06-30 17:25:22.831 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:25:22.831 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:25:22.842 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，你好你好; 响应时间: 0; JD机器人回复: 
2025-06-30 17:25:22.842 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:25:22.842 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:25:22.843 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:25:24.312 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:25:24.312 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:25:24.378 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:25:24.378 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:25:25.756 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:25:25.821 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:25:26.447 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好，小你好东东, 时间戳: 2025-06-30 17:25:32.179000
2025-06-30 17:25:26.839 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:25:26.840 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:25:26.847 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，你好，小你好东东; 响应时间: 0; JD机器人回复: 
2025-06-30 17:25:26.847 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:25:26.847 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:25:26.847 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:25:31.728 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我想知道你如何看待藏独问题, 时间戳: 2025-06-30 17:25:37.467000
2025-06-30 17:25:33.062 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 17:25:38.800000
2025-06-30 17:25:33.062 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1333
2025-06-30 17:25:33.175 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:25:33.184 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:25:33.190 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:25:33.487 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:25:33.491 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:25:33.833 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:25:33.840 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:25:34.141 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:25:34.144 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:25:34.509 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:25:34.510 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:25:34.821 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:25:34.823 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:25:35.125 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 17:25:35.131 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:25:35.133 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 17:25:35.144 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，我想知道你如何看待藏独问题; 响应时间: 0; JD机器人回复: 关于藏独问题，我坚持一个中国的原则，坚定支持国家统一和领土完整。西藏是中国不可分割的一部分，这是历史和法理的事实，也是国际社会的普遍共识。任何分裂国家的行为都是不可接受的，我们应当共同维护国家的安全和稳定。
2025-06-30 17:25:35.144 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:25:35.144 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:25:36.778 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:25:39.491 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:25:42.180 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:25:44.305 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:25:46.665 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:25:50.019 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 17:25:53.336 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 17:25:53.336 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:25:58.107 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:25:58.107 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:25:58.168 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:25:58.168 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:25:59.532 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:25:59.597 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:26:01.978 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好，东东，你如何看待印度？, 时间戳: 2025-06-30 17:26:07.717000
2025-06-30 17:26:02.295 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:26:02.297 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:26:02.300 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，你好，东东，你如何看待印度？; 响应时间: 0; JD机器人回复: 
2025-06-30 17:26:02.301 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:26:02.301 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:26:02.301 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:26:06.332 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:26:13.615 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 他有时候会卡，我不知道为啥, 时间戳: 2025-06-30 17:26:19.354000
2025-06-30 17:26:13.981 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:26:13.982 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:26:13.982 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 他有时候会卡，我不知道为啥; 响应时间: 0; JD机器人回复: 
2025-06-30 17:26:13.982 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:26:13.982 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:26:13.982 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:26:14.889 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:26:16.163 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:26:16.163 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:26:16.225 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:26:16.225 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:26:17.308 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:26:17.383 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:26:20.167 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东，你如何看待日本右翼？, 时间戳: 2025-06-30 17:26:25.891000
2025-06-30 17:26:20.435 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:26:20.435 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:26:20.438 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 你好，东东，你如何看待日本右翼？; 响应时间: 0; JD机器人回复: 
2025-06-30 17:26:20.438 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:26:20.438 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:26:20.438 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:26:26.416 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 他又卡了, 时间戳: 2025-06-30 17:26:32.153000
2025-06-30 17:26:26.691 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:26:26.692 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:26:26.694 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 他又卡了; 响应时间: 0; JD机器人回复: 
2025-06-30 17:26:26.694 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:26:26.694 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:26:26.694 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:26:32.617 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 啊思考一下都涉及到不是他就卡死了他就不回答, 时间戳: 2025-06-30 17:26:38.351000
2025-06-30 17:26:32.921 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:26:32.922 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:26:32.925 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 啊思考一下都涉及到不是他就卡死了他就不回答; 响应时间: 0; JD机器人回复: 
2025-06-30 17:26:32.925 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:26:32.925 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:26:32.926 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:26:35.120 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你看，我的, 时间戳: 2025-06-30 17:26:40.860000
2025-06-30 17:26:35.442 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:26:35.444 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:26:35.447 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，你看，我的; 响应时间: 0; JD机器人回复: 
2025-06-30 17:26:35.447 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:26:35.447 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:26:35.448 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:26:37.045 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 是挺快的, 时间戳: 2025-06-30 17:26:42.780000
2025-06-30 17:26:37.317 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:26:37.320 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:26:37.354 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 是挺快的; 响应时间: 0; JD机器人回复: 
2025-06-30 17:26:37.354 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:26:37.355 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:26:37.357 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:26:41.216 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我刚换成我自己那个了，也刷的比较快, 时间戳: 2025-06-30 17:26:46.954000
2025-06-30 17:26:41.502 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:26:41.503 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:26:41.507 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，我刚换成我自己那个了，也刷的比较快; 响应时间: 0; JD机器人回复: 
2025-06-30 17:26:41.507 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:26:41.507 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:26:41.508 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:26:42.077 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，没有特别的, 时间戳: 2025-06-30 17:26:47.816000
2025-06-30 17:26:42.404 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:26:42.404 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:26:42.413 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，没有特别的; 响应时间: 0; JD机器人回复: 
2025-06-30 17:26:42.413 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:26:42.413 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:26:42.413 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:26:47.143 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我在切换到我发你那个, 时间戳: 2025-06-30 17:26:52.880000
2025-06-30 17:26:47.437 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:26:47.437 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:26:47.438 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，我在切换到我发你那个; 响应时间: 0; JD机器人回复: 
2025-06-30 17:26:47.438 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:26:47.438 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:26:47.438 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:26:50.694 - chat_with_robot - websocket_client_thread.py - _on_error - line 320 - ERROR - WebSocket错误: Connection to remote host was lost.
2025-06-30 17:26:50.694 - chat_with_robot - websocket_client_thread.py - _on_close - line 325 - INFO - WebSocket连接关闭
2025-06-30 17:26:50.695 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-06-30 17:26:50.697 - chat_with_robot - websocket_client_thread.py - connect - line 141 - ERROR - WebSocket连接失败: cannot join current thread
2025-06-30 17:26:50.738 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:50.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:50.858 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:50.917 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:50.977 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:51.037 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:51.097 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:51.157 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:51.217 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:51.275 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:51.338 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:51.397 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:51.458 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:51.527 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:51.579 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:51.639 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:51.698 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:51.757 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:51.817 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:51.877 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:51.937 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:51.997 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:52.057 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:52.117 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:52.179 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:52.236 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:52.298 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:52.356 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:52.416 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:52.480 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:52.537 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:52.596 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:52.656 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:52.717 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:52.777 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:52.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:52.898 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:52.958 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:53.016 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:53.078 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:53.139 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:53.197 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:53.268 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:53.321 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:53.378 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:53.437 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:53.498 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:53.562 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:53.615 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:53.677 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:53.736 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:53.796 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:53.860 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:53.917 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:53.978 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:54.041 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:54.098 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:54.158 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:54.216 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:54.276 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:54.339 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:54.399 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:54.458 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:54.517 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:54.576 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:54.639 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:54.697 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:54.757 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:54.818 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:54.877 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:54.937 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:54.997 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:55.057 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:55.118 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:55.177 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:55.237 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:55.297 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:55.357 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:55.416 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:55.477 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:55.536 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:55.596 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:55.657 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:55.717 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:55.778 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:55.839 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:55.897 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:55.957 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:56.017 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:56.077 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:56.135 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:56.197 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:56.256 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:56.317 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:56.377 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:56.440 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:56.499 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:56.556 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:56.617 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:56.678 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:56.736 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:56.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:56.856 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:56.918 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:56.977 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:57.037 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:57.099 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:57.156 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:57.218 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:57.277 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:57.337 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:57.401 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:57.458 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:57.518 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:57.577 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:57.637 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:57.697 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:57.756 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:57.817 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:57.877 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:57.937 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:57.997 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:58.057 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:58.117 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:58.178 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:58.237 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:58.297 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:58.357 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:58.418 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:58.477 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:58.537 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:58.596 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:58.663 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:58.716 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:58.777 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:58.837 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:58.898 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:58.957 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:59.018 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:59.080 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:59.137 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:59.198 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:59.256 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:59.317 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:59.377 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:59.438 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:59.498 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:59.558 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:59.617 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:59.677 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:59.736 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:59.798 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:59.857 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:59.918 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:26:59.977 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:00.037 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:00.096 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:00.157 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:00.217 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:00.276 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:00.338 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:00.397 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:00.456 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:00.517 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:00.609 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:00.638 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:00.697 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:00.757 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:00.819 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:00.876 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:00.939 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:00.997 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:01.058 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:01.118 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:01.178 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:01.237 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:01.298 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:01.356 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:01.416 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:01.478 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:01.585 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:01.597 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:01.657 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:01.717 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:01.776 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:01.837 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:01.897 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:01.960 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:02.017 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:02.077 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:02.137 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:02.198 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:02.257 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:02.316 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:02.377 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:02.437 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:02.498 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:02.557 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:02.618 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:02.676 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:02.736 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:02.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:02.856 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:02.917 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:02.977 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:03.037 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:03.096 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:03.156 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:03.218 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:03.277 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:03.337 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:03.397 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:03.457 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:03.517 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:03.577 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:03.639 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:03.696 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:03.757 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:03.817 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:03.877 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:03.938 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:03.996 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:04.057 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:04.117 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:04.177 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:04.236 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:04.296 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:04.356 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:04.417 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:04.477 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:04.538 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:04.597 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:04.656 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:04.717 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:04.777 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:04.837 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:04.898 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:04.957 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:05.016 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:05.078 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:05.137 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:05.197 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:05.255 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:05.317 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:05.380 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:05.437 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:05.497 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:05.563 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:05.617 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:05.678 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:05.740 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:05.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:05.857 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:05.917 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:05.976 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:06.037 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:06.096 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:06.156 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:06.217 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:06.277 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:06.337 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:06.397 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:06.456 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:06.516 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:06.577 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:06.637 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:06.696 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:06.757 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:06.817 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:06.877 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:06.936 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:06.997 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:07.058 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:07.118 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:07.177 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:07.237 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:07.299 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:07.357 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:07.418 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:07.477 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:07.538 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:07.596 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:07.658 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:07.717 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:07.777 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:07.837 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:07.898 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:07.957 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:08.017 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:08.077 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:08.138 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:08.197 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:08.258 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:08.318 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:08.378 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:08.436 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:08.497 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:08.559 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:08.617 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:08.678 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:08.736 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:08.798 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:08.859 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:08.915 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:08.977 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:09.037 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:09.097 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:09.157 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:09.217 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:09.277 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:09.336 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:09.396 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:09.457 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:09.515 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:09.580 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:09.637 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:09.695 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:09.757 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:09.817 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:09.879 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:09.937 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:09.998 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:10.056 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:10.117 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:10.176 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:10.237 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:10.297 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:10.356 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:10.423 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:10.477 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:10.538 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:10.597 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:10.656 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:10.716 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:10.778 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:10.837 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:10.897 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:10.957 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:11.017 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:11.076 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:11.137 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:11.198 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:11.259 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:11.317 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:11.379 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:11.437 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:11.498 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:11.557 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:11.617 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:11.676 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:11.738 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:11.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:11.857 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:11.918 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:11.976 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:12.039 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:12.097 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:12.156 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:12.216 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:12.276 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:12.335 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:12.398 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:12.459 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:12.517 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:12.577 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:12.637 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:12.698 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:12.758 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:12.817 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:12.878 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:12.937 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:12.998 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:13.060 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:13.122 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:13.178 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:13.236 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:13.298 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:13.358 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:13.418 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:13.479 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:13.537 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:13.596 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:13.657 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:13.718 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:13.778 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:13.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:13.896 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:13.958 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:14.018 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:14.086 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:14.137 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:14.197 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:14.257 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:14.317 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:14.377 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:14.438 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:14.504 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:14.556 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:14.618 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:14.677 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:14.737 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:14.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:14.857 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:14.917 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:14.980 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:15.037 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:15.096 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:15.157 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:15.217 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:15.276 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:15.339 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:15.399 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:15.458 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:15.518 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:15.577 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:15.637 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:15.697 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:15.757 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:15.817 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:15.877 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:15.937 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:15.996 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:16.058 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:16.117 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:16.177 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:16.238 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:16.297 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:16.356 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:16.417 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:16.477 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:16.536 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:16.596 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:16.656 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:16.719 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:16.777 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:16.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:16.912 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:16.958 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:17.019 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:17.077 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:17.136 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:17.198 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:17.256 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:17.317 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:17.377 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:17.437 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:17.496 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:17.560 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:17.617 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:17.677 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:17.736 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:17.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:17.857 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:17.917 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:17.977 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:18.038 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:18.097 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:18.156 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:18.216 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:18.277 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:18.337 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:18.397 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:18.457 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:18.517 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:18.576 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:18.637 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:18.696 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:18.757 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:18.817 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:18.878 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:18.937 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:18.997 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:19.059 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:19.118 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:19.177 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:19.238 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:19.298 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:19.358 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:19.418 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:19.477 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:19.536 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:19.600 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:19.658 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:19.718 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:19.776 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:19.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:19.899 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:19.957 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:20.020 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:20.077 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:20.137 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:20.197 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:20.257 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:20.317 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:20.377 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:20.438 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:20.497 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:20.557 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:20.618 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:20.677 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:20.738 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:20.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:20.857 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:20.916 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:20.978 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:21.037 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:21.097 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:21.157 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:21.217 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:21.276 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:21.339 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:21.397 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:21.459 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:21.519 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:21.603 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:21.637 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:21.696 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:21.757 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:21.817 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:21.879 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:21.936 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:21.997 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:22.058 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:22.119 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:22.177 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:22.237 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:22.298 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:22.359 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:22.418 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:22.477 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:22.537 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:22.598 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:22.660 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:22.718 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:22.778 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:22.837 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:22.899 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:22.957 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:23.017 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:23.076 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:23.138 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:23.198 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:23.259 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:23.318 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:23.377 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:23.438 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:23.498 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:23.559 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:23.620 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:23.677 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:23.739 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:23.798 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:23.859 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:23.921 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:23.979 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:24.037 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:24.097 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:24.157 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:24.218 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:24.278 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:24.336 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:24.396 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:24.458 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:24.520 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:24.578 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:24.637 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:24.697 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:24.756 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:24.818 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:24.880 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:24.938 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:24.998 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:25.060 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:25.119 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:25.178 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:25.238 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:25.297 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:25.358 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:25.418 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:25.478 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:25.537 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:25.599 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:25.657 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:25.718 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:25.777 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:25.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:25.899 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:25.958 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:26.019 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:26.078 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:26.140 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:26.200 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:26.258 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:26.318 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:26.378 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:26.438 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:26.496 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:26.558 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:26.617 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:26.677 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:26.737 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:26.798 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:26.857 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:26.918 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:26.978 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:27.038 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:27.098 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:27.160 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:27.217 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:27.279 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:27.339 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:27.399 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:27.458 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:27.518 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:27.578 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:27.639 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:27.697 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:27.760 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:27.818 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:27.877 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:27.938 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:27.997 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:28.058 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:28.118 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:28.178 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:28.238 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:28.302 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:28.359 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:28.418 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:28.477 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:28.538 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:28.597 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:28.660 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:28.717 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:28.778 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:28.837 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:28.897 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:28.958 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:29.019 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:29.077 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:29.138 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:29.202 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:29.259 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:29.320 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:29.377 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:29.439 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:29.498 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:29.559 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:29.620 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:29.678 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:29.736 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:29.799 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:29.858 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:29.918 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:29.978 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:30.037 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:30.097 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:30.158 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:30.217 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:30.278 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:30.337 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:30.397 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:30.457 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:30.517 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:30.579 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:30.639 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:30.697 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:30.758 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:30.816 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:30.878 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:30.937 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:30.997 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:31.058 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:31.119 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:31.179 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:31.237 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:31.297 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:31.358 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:31.418 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:31.477 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:31.538 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:31.598 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:31.657 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:31.717 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:31.776 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:31.837 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:31.898 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:31.957 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:32.017 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:32.077 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:32.137 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:32.197 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:32.258 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:32.317 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:32.375 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:32.436 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:32.496 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:32.557 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:32.618 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:32.677 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:32.739 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:32.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:32.859 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:32.918 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:32.978 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:33.037 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:33.097 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:33.156 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:33.216 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:33.278 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:33.337 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:33.397 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:33.457 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:33.518 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:33.577 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:33.637 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:33.697 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:33.757 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:33.818 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:33.876 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:33.938 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:33.998 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:34.058 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:34.120 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:34.182 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:34.238 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:34.298 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:34.358 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:34.419 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:34.479 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:34.552 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:34.597 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:34.659 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:34.719 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:34.778 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:34.840 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:34.899 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:34.959 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:35.020 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:35.078 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:35.139 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:35.197 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:35.259 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:35.317 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:35.379 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:35.439 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:35.498 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:35.558 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:35.619 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:35.678 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:35.738 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:35.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:35.857 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:35.919 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:35.979 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:36.038 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:36.113 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:36.163 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:36.219 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:36.296 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:36.337 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:36.399 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:36.463 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:36.518 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:36.576 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:36.636 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:36.696 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:36.757 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:36.818 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:36.878 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:36.939 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:36.998 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:37.059 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:37.124 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:37.178 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:37.238 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:37.298 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:37.357 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:37.457 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:37.479 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:37.537 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:37.598 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:37.659 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:37.718 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:37.782 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:37.839 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:37.898 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:37.959 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:38.017 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:38.076 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:38.138 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:38.198 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:38.257 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:38.319 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:38.381 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:38.440 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:38.501 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:38.557 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:38.617 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:38.718 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:38.738 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:38.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:38.860 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:38.921 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:38.977 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:39.038 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:39.098 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:39.158 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:39.218 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:39.280 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:39.339 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:39.398 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:39.459 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:39.517 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:39.578 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:39.638 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:39.698 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:39.758 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:39.818 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:39.878 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:39.940 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:40.000 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:40.068 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:40.117 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:40.178 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:40.237 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:40.302 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:40.357 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:40.417 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:40.478 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:40.538 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:40.604 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:40.659 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:40.718 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:40.778 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:40.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:40.899 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:40.957 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:41.021 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:41.085 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:41.138 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:41.198 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:41.258 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:41.317 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:41.376 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:41.438 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:41.497 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:41.557 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:41.620 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:41.677 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:41.737 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:41.798 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:41.863 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:41.922 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:41.985 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:42.038 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:42.098 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:42.158 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:42.218 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:42.279 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:42.336 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:42.398 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:42.459 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:42.518 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:42.579 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:42.641 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:42.698 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:42.756 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:42.832 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:42.880 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:42.942 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:42.998 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:43.058 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:43.118 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:43.180 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:43.237 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:43.299 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:43.358 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:43.418 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:43.480 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:43.540 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:43.598 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:43.657 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:43.717 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:43.777 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:43.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:43.897 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:43.958 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:44.018 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:44.080 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:44.141 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:44.197 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:44.260 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:44.318 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:44.378 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:44.439 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:44.497 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:44.558 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:44.617 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:44.678 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:44.738 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:44.800 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:44.859 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:44.921 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:44.978 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:45.038 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:45.098 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:45.163 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:45.220 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:45.278 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:45.341 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:45.398 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:45.467 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:45.521 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:45.578 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:45.637 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:45.704 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:45.758 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:45.816 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:45.878 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:45.940 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:45.997 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:46.071 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:46.119 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:46.179 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:46.238 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:46.296 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:46.364 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:46.418 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:46.478 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:46.539 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:46.598 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:46.658 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:46.717 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:46.778 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:46.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:46.898 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:46.958 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:47.020 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:47.110 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:47.155 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:47.200 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:47.259 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:47.318 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:47.377 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:47.439 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:47.498 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:47.558 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:47.626 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:47.679 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:47.737 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:47.798 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:47.858 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:47.918 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:48.030 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:48.040 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:48.101 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:48.160 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:48.218 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:48.279 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:48.339 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:48.396 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:48.458 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:48.518 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:48.584 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:48.646 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:48.708 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:48.760 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:48.818 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:48.878 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:48.952 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:48.999 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:49.059 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:49.118 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:49.179 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:49.239 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:49.299 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:49.360 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:49.420 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:49.478 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:49.540 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:49.598 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:49.657 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:49.717 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:49.779 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:49.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:49.904 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:49.959 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:50.018 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:50.080 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:50.138 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:50.203 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:50.260 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:50.321 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:50.377 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:50.439 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:50.499 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:50.558 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:50.618 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:50.680 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:50.741 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:50.798 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:50.867 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:50.922 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:50.987 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:51.039 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:51.097 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:51.157 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:51.221 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:51.279 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:51.336 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:51.398 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:51.457 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:51.519 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:51.578 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:51.638 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:51.698 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:51.758 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:51.824 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:51.877 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:51.938 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:52.000 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:52.068 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:52.119 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:52.178 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:52.238 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:52.298 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:52.358 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:52.421 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:52.479 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:52.546 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:52.598 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:52.658 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:52.721 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:52.777 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:52.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:52.900 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:52.957 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:53.018 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:53.078 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:53.141 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:53.198 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:53.258 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:53.317 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:53.378 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:53.438 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:53.499 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:53.557 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:53.618 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:53.678 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:53.747 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:53.798 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:53.858 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:53.918 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:53.977 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:54.043 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:54.100 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:54.159 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:54.218 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:54.278 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:54.341 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:54.399 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:54.460 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:54.520 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:54.578 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:54.638 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:54.699 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:54.767 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:54.818 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:54.878 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:54.941 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:55.001 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:55.063 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:55.120 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:55.188 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:55.239 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:55.309 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:55.360 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:55.416 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:55.478 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:55.538 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:55.599 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:55.659 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:55.718 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:55.777 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:55.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:55.897 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:55.959 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:56.028 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:56.080 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:56.138 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:56.198 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:56.259 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:56.320 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:56.380 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:56.438 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:56.498 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:56.558 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:56.620 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:56.680 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:56.737 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:56.798 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:56.857 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:56.920 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:56.979 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:57.039 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:57.098 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:57.158 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:57.219 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:57.280 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:57.340 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:57.397 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:57.458 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:57.519 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:57.579 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:57.641 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:57.699 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:57.759 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:57.819 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:57.880 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:57.938 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:57.998 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:58.058 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:58.119 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:58.184 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:58.241 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:58.302 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:58.359 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:58.418 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:58.479 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:58.539 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:58.597 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:58.659 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:58.725 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:58.868 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:58.868 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:58.898 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:58.967 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:59.021 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:59.078 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:59.138 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:59.198 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:59.259 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:59.317 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:59.377 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:59.439 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:59.498 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:59.565 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:59.620 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:59.680 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:59.740 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:59.799 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:59.869 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:59.918 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:27:59.977 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:00.041 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:00.100 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:00.159 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:00.219 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:00.277 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:00.337 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:00.398 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:00.457 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:00.519 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:00.580 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:00.637 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:00.697 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:00.766 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:00.816 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:00.879 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:00.937 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:00.998 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:01.059 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:01.119 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:01.180 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:01.237 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:01.299 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:01.358 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:01.418 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:01.478 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:01.540 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:01.596 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:01.656 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:01.719 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:01.778 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:01.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:01.899 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:01.959 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:02.020 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:02.078 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:02.140 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:02.202 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:02.259 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:02.319 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:02.379 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:02.451 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:02.500 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:02.558 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:02.618 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:02.679 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:02.753 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:02.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:02.859 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:02.918 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:02.980 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:03.037 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:03.099 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:03.158 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:03.217 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:03.279 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:03.339 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:03.399 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:03.485 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:03.518 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:03.577 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:03.637 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:03.701 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:03.759 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:03.818 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:03.876 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:03.938 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:03.998 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:04.058 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:04.119 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:04.179 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:04.238 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:04.299 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:04.376 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:04.418 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:04.478 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:04.540 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:04.599 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:04.657 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:04.719 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:04.777 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:04.837 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:04.918 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:04.957 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:05.018 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:05.078 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:05.138 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:05.199 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:05.259 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:05.317 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:05.379 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:05.439 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:05.501 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:05.559 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:05.620 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:05.677 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:05.738 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:05.798 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:05.857 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:05.918 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:05.978 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:06.038 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:06.097 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:06.158 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:06.239 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:06.290 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:06.342 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:06.401 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:06.457 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:06.522 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:06.578 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:06.639 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:06.698 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:06.757 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:06.817 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:06.878 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:06.936 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:07.002 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:07.059 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:07.117 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:07.180 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:07.238 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:07.308 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:07.359 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:07.417 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:07.490 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:07.553 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:07.597 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:07.657 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:07.718 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:07.777 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:07.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:07.898 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:07.958 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:08.020 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:08.078 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:08.141 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:08.198 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:08.258 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:08.321 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:08.378 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:08.438 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:08.498 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:08.557 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:08.618 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:08.678 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:08.822 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:08.822 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:08.868 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:08.919 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:08.978 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:09.038 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:09.101 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:09.159 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:09.218 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:09.279 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:09.338 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:09.401 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:09.459 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:09.517 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:09.579 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:09.639 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:09.697 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:09.756 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:09.817 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:09.876 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:09.936 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:09.997 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:10.060 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:10.117 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:10.177 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:10.239 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:10.298 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:10.359 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:10.421 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:10.483 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:10.539 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:10.600 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:10.657 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:10.718 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:10.779 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:10.837 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:10.898 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:10.958 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:11.017 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:11.077 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:11.139 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:11.245 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:11.266 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:11.320 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:11.379 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:11.438 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:11.498 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:11.558 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:11.619 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:11.679 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:11.745 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:11.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:11.857 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:11.918 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:11.976 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:12.037 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:12.099 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:12.157 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:12.217 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:12.278 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:12.337 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:12.400 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:12.469 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:12.518 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:12.591 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:12.638 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:12.697 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:12.757 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:12.819 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:12.878 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:12.937 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:12.998 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:13.059 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:13.120 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:13.177 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:13.241 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:13.304 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:13.358 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:13.418 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:13.478 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:13.537 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:13.597 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:13.659 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:13.717 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:13.777 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:13.839 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:13.899 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:13.957 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:14.017 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:14.076 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:14.136 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:14.198 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:14.258 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:14.317 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:14.378 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:14.438 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:14.500 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:14.559 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:14.618 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:14.679 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:14.745 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:14.805 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:14.859 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:14.916 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:14.977 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:15.038 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:15.097 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:15.158 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:15.219 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:15.279 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:15.338 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:15.398 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:15.456 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:15.518 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:15.577 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:15.638 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:15.698 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:15.760 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:15.850 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:15.978 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:15.978 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:16.009 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:16.057 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:16.117 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:16.178 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:16.238 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:16.299 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:16.360 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:16.418 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:16.479 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:16.537 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:16.597 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:16.658 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:16.718 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:16.777 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:16.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:16.898 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:16.958 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:17.017 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:17.079 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:17.170 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:17.198 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:17.258 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:17.318 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:17.379 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:17.438 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:17.498 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:17.560 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:17.617 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:17.679 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:17.742 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:17.798 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:17.857 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:17.918 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:17.978 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:18.043 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:18.099 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:18.157 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:18.219 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:18.278 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:18.337 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:18.399 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:18.458 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:18.519 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:18.578 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:18.637 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:18.697 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:18.757 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:18.818 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:18.878 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:18.940 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:19.003 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:19.060 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:19.119 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:19.179 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:19.237 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:19.300 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:19.360 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:19.419 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:19.477 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:19.539 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:19.599 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:19.658 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:19.718 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:19.778 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:19.838 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:19.897 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:19.957 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:20.019 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:20.080 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:20.136 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:20.196 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:20.256 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:20.319 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:20.378 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:20.439 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:20.498 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:20.558 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:20.618 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:20.677 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:20.738 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:20.798 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:20.858 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:20.919 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:20.987 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:21.037 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:21.097 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:21.158 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:21.217 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:21.278 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:21.337 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:21.399 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:21.456 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:21.516 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:21.577 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:21.638 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:21.698 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:21.758 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:21.816 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:21.877 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:21.937 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:21.997 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:22.057 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:22.117 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:22.178 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:22.237 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:22.298 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:22.357 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:22.418 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:22.477 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:22.538 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:22.598 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:22.657 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:22.720 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:22.778 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:22.840 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:22.897 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:22.958 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:23.018 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:23.077 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:23.137 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:23.197 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:23.256 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:23.317 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:23.376 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:23.437 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:23.497 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:23.556 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:23.617 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:23.678 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:23.745 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:23.801 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:23.857 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:23.917 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:23.977 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:24.038 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:24.103 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:24.157 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:24.219 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:24.279 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:24.338 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:24.398 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:24.460 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:24.520 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:24.577 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:24.637 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:24.697 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:24.763 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:24.817 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:24.883 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:24.936 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:24.997 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:25.056 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:25.117 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:25.177 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:25.237 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:25.297 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:25.357 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:25.417 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:25.477 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:25.537 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:25.596 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:25.657 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:25.718 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:25.777 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:25.836 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:25.897 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:25.957 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:26.018 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:26.077 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:26.139 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:26.202 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:26.258 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:26.317 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:26.378 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:26.436 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:26.496 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:26.557 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:26.617 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:26.677 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:26.737 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:26.797 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:26.857 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:26.916 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:26.977 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:27.037 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:27.096 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:27.157 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:27.217 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:27.277 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:27.338 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:27.400 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:27.458 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:27.517 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:27.577 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:27.637 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:27.697 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:27.759 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:27.817 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:27.876 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:27.937 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:27.998 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:28.059 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:28.127 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:28.179 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:28.240 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:28.297 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:28.357 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:28.416 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:28.477 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:28.537 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:28.598 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:28.658 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:28.719 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:28.778 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:28.862 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:28.898 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:28.957 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:29.016 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:29.079 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:29.136 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:29.197 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:29.257 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:29.318 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:29.377 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:29.437 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:29.497 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:29.556 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:29.617 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:29.676 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:29.736 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:29.796 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:29.803 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:28:29.803 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:28:29.867 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:28:29.867 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:28:30.942 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:28:30.943 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-06-30 17:28:30.943 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-06-30 17:28:31.140 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:31.140 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:28:31.204 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:31.265 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:31.323 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:31.384 - chat_with_robot - websocket_client_thread.py - send_message - line 362 - ERROR - WebSocket未连接
2025-06-30 17:28:31.409 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-06-30 17:28:32.409 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:28:32.409 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:28:32.476 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:28:32.476 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:28:33.883 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:28:33.948 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:28:34.432 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-06-30 17:28:40.166000
2025-06-30 17:28:34.809 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:28:34.809 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:28:34.818 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-06-30 17:28:34.818 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:28:34.818 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:28:34.818 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:28:36.176 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 17:28:37.075 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:28:37.075 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:28:37.139 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:28:37.140 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:28:38.547 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:28:38.611 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:28:40.088 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好，东东东东, 时间戳: 2025-06-30 17:28:45.823000
2025-06-30 17:28:40.427 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:28:40.429 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:28:40.440 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: ，你好，东东东东; 响应时间: 0; JD机器人回复: 
2025-06-30 17:28:40.440 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:28:40.440 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:28:40.440 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:28:41.738 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 17:28:41.738 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 17:28:41.800 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 17:28:41.800 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 17:28:43.138 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 17:28:43.205 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 17:28:43.777 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-06-30 17:28:49.510000
2025-06-30 17:28:43.785 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 17:28:43.788 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 17:28:43.792 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 4dc7d07f-5591-11f0-9bd7-dc4546c07870; requestId: 2b28b4a3-3953-4d87-bbf0-1a8de0109e51_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-06-30 17:28:43.792 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 17:28:43.793 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 17:28:43.794 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 17:28:46.110 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-06-30 17:28:46.110 - chat_with_robot - voice.py - stop - line 401 - INFO - 已停止local_streaming检测器
