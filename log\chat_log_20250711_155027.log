2025-07-11 15:50:29.114 - chat_with_robot - chat_with_robot.py - <module> - line 640 - INFO - use_action: dont
2025-07-11 15:50:29.115 - chat_with_robot - chat_with_robot.py - <module> - line 641 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-11 15:50:29.115 - chat_with_robot - chat_with_robot.py - init_websocket - line 319 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1752220229115&accessNonce=486964f1-cbe8-4b9d-b114-18ca38790810&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=b6c06867-5e2b-11f0-b52d-dc4546c07870&requestId=9f5b3c35-e201-4a71-b447-46db50c71638_joyinside&accessSign=2dc132d9343133750f61fa0e3d4b3bc4, request_id: 9f5b3c35-e201-4a71-b447-46db50c71638_joyinside
2025-07-11 15:50:29.116 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-11 15:50:29.116 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-11 15:50:29.618 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-11 15:50:29.763 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-07-11 15:50:31.225 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-11 15:50:31.226 - chat_with_robot - voice.py - _setup_audio_stream - line 324 - INFO - 使用音频设备: 1
2025-07-11 15:50:31.226 - chat_with_robot - voice.py - _setup_audio_stream - line 325 - INFO - channels: 4 <class 'int'>
2025-07-11 15:50:31.226 - chat_with_robot - voice.py - _setup_audio_stream - line 326 - INFO - rate: 44100.0 <class 'float'>
2025-07-11 15:50:31.280 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-11 15:50:31.280 - chat_with_robot - voice.py - init_wakeup - line 311 - INFO - 本地流式KWS检测器启动成功
2025-07-11 15:50:32.281 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-11 15:50:32.281 - chat_with_robot - chat_with_robot.py - play_audio - line 525 - INFO - 使用 audio_action_controller 播放: ./asserts/ding.wav
2025-07-11 15:50:32.344 - chat_with_robot - chat_with_robot.py - play_audio - line 527 - INFO - audio_action_controller 播放完成: ./asserts/ding.wav
2025-07-11 15:50:32.344 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 84 - INFO - Windows 系统，使用 audio_action_controller 播放: asserts/tts/dog_ok.mp3
2025-07-11 15:50:36.839 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-07-11 15:50:36.839 - chat_with_robot - voice.py - stop - line 421 - INFO - 已停止local_streaming检测器
