import socket
import time
ENGINEAI_TCP_IP = "*************"
ENGINEAI_TCP_PORT = 30000

# 创建TCP连接
s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
s.settimeout(5) # 5 秒超时
s.connect((ENGINEAI_TCP_IP, ENGINEAI_TCP_PORT))

# 发送数据
# s.sendall("enable-hands".encode('utf-8'))
# s.sendall("disable-hands".encode('utf-8'))
# s.sendall("pd-stand".encode('utf-8'))
# time.sleep(1)
# s.sendall("passive".encode('utf-8'))
# time.sleep(1)
s.sendall("idle".encode('utf-8'))

# 关闭连接
s.close()