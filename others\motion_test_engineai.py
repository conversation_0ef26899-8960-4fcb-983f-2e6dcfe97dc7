"""
@Author: lid<PERSON><PERSON><EMAIL>
@Create Date: 2025.04.10
@Description: 用于测试EngineAI机器人动作的命令行工具，只需在终端输入动作编号即可向机器人发送指令

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
"""
import sys
import os
import time
import argparse
import socket

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from robot_agent.engineai_commander import RobotCommanderEngineAI
from robot_agent.engineai_controller import RobotControllerEngineAI
from robot_agent.engineai_config import ENGINEAI_CONFIG
from util.logger import logger

def check_robot_connection(ip="*************", port=30000):
    """检查EngineAI机器人连接状态"""
    try:
        logger.info(f"检查连接到机器人 {ip}:{port}...")
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(3)
        result = s.connect_ex((ip, port))
        s.close()
        
        if result == 0:
            logger.info(f"网络连接正常，可以连接到机器人 {ip}:{port}")
            return True
        else:
            logger.error(f"警告: 无法连接到机器人 {ip}:{port}，请检查网络连接")
            return False
    except Exception as e:
        logger.error(f"网络检查失败: {e}")
        return False

def print_skill_menu(command_map):
    """打印技能菜单"""
    print("\n======== EngineAI机器人动作测试工具 ========")
    print("可用动作列表:")
    
    # 显示配置文件中的命令
    for cmd_id, cmd_list in command_map.items():
        cmd_desc = " → ".join(cmd_list)
        print(f"{cmd_id:2d}. {cmd_desc}")
    
    # 额外选项
    print("101. 自定义命令")
    print("0. 退出程序")
    print("==========================================")

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="EngineAI机器人动作测试工具")
    parser.add_argument('--ip', type=str, default=None,
                        help="机器人IP地址，默认使用配置文件中的IP")
    parser.add_argument('--port', type=int, default=None,
                        help="机器人端口，默认使用配置文件中的端口")
    args = parser.parse_args()
    
    # 获取机器人IP和端口
    robot_ip = args.ip if args.ip else ENGINEAI_CONFIG.get("engineai_tcp_ip", "*************")
    robot_port = args.port if args.port else ENGINEAI_CONFIG.get("engineai_tcp_port", 30000)
    
    # 检查机器人连接
    logger.info(f"尝试连接EngineAI机器人 {robot_ip}:{robot_port}...")
    check_result = check_robot_connection(robot_ip, robot_port)
    retry_count = 0
    while not check_result and retry_count < 3:
        user_input = input("无法连接到机器人，是否重试? (y/n): ")
        if user_input.lower() != 'y':
            logger.error("退出程序")
            return
        check_result = check_robot_connection(robot_ip, robot_port)
        retry_count += 1
        time.sleep(1)
    
    # 初始化机器人通信管理器
    logger.info("初始化EngineAI机器人通信管理器...")
    commander = RobotCommanderEngineAI(robot_ip, robot_port)
    
    # 测试连接
    if not commander.check_connection():
        logger.error("无法建立机器人连接，退出程序")
        return
    
    # 创建控制器
    controller = RobotControllerEngineAI(commander)
    command_map = ENGINEAI_CONFIG.get("command_map", {})
    
    # 主循环
    try:
        while True:
            print_skill_menu(command_map)
            user_input = input("请输入动作编号或命令: ")
            
            try:
                # 处理退出
                if user_input == "0":
                    logger.info("退出程序")
                    break
                
                # 处理自定义命令
                elif user_input == "101":
                    custom_cmd = input("请输入自定义命令: ")
                    logger.info(f"发送自定义命令: {custom_cmd}")
                    result = commander.run(custom_cmd)
                    if result:
                        logger.info("命令发送成功")
                    else:
                        logger.error("命令发送失败")
                    continue
                
                # 处理数字命令ID
                cmd_id = int(user_input)
                if cmd_id in command_map:
                    logger.info(f"执行动作ID: {cmd_id}")
                    result = controller.control_robot(cmd_id)
                    if result:
                        logger.info("动作执行完成")
                    else:
                        logger.error("动作执行失败")
                else:
                    logger.warning(f"无效的动作编号: {cmd_id}")
                    
            except ValueError:
                logger.warning("请输入有效的数字")
                
    except KeyboardInterrupt:
        logger.info("用户中断，退出程序")
    finally:
        # 关闭连接
        logger.info("关闭连接...")
        commander.on_closing()

if __name__ == "__main__":
    main()