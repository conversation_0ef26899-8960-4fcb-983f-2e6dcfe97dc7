[16:08:38.540] INFO     | audio_action_controller.py:144 - Modbus 功能已禁用（pymodbus 未安装）
[16:08:42.385] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: ./asserts/ding.wav
[16:08:42.385] INFO     | audio_action_controller.py:193 - 音频队列处理线程已启动
[16:08:42.387] INFO     | audio_action_controller.py:230 - 音频已添加到队列: ./asserts/ding.wav
[16:08:42.387] INFO     | audio_action_controller.py:176 - 队列播放音频: ./asserts/ding.wav
[16:08:42.388] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: asserts/tts/dog_ok.mp3
[16:08:42.388] INFO     | audio_action_controller.py:230 - 音频已添加到队列: asserts/tts/dog_ok.mp3
[16:08:42.452] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:08:42.453] INFO     | audio_action_controller.py:78 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[16:08:42.453] INFO     | audio_action_controller.py:245 - 正在加载音频: ./asserts/ding.wav
[16:08:42.453] INFO     | audio_action_controller.py:50 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[16:08:42.455] INFO     | audio_action_controller.py:259 - 音频长度(从文件获取): 0.9675963521003723秒
[16:08:42.455] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:08:42.456] INFO     | audio_action_controller.py:277 - 开始播放音频
[16:08:42.457] INFO     | audio_action_controller.py:282 - 嘴部动作控制线程已启动
[16:08:42.457] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:42.457] INFO     | audio_action_controller.py:285 - 脖子动作控制线程已启动
[16:08:42.457] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:42.578] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:42.608] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:42.699] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:42.759] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:42.820] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:42.910] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:42.940] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:43.061] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:43.061] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:43.182] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:43.212] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:43.303] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:43.363] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:43.466] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:08:43.466] INFO     | audio_action_controller.py:302 - 音频播放完成
[16:08:43.466] INFO     | audio_action_controller.py:176 - 队列播放音频: asserts/tts/dog_ok.mp3
[16:08:43.515] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:43.551] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:08:43.551] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:08:43.552] INFO     | audio_action_controller.py:245 - 正在加载音频: asserts/tts/dog_ok.mp3
[16:08:43.557] INFO     | audio_action_controller.py:50 - 音频音量设置为: 85.0% (针对WAV无损格式优化)
[16:08:43.558] INFO     | audio_action_controller.py:259 - 音频长度(从文件获取): 4.439773082733154秒
[16:08:43.559] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:08:43.559] INFO     | audio_action_controller.py:277 - 开始播放音频
[16:08:43.560] INFO     | audio_action_controller.py:282 - 嘴部动作控制线程已启动
[16:08:43.560] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:43.560] INFO     | audio_action_controller.py:285 - 脖子动作控制线程已启动
[16:08:43.560] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:43.681] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:43.711] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:43.801] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:43.862] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:43.922] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:44.013] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:44.043] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:44.164] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:44.164] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:44.286] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:44.316] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:44.407] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:44.467] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:44.528] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:44.617] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:44.649] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:44.768] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:44.770] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:44.890] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:44.919] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:45.011] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:45.070] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:45.132] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:45.221] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:45.253] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:45.372] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:45.374] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:45.495] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:45.523] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:45.616] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:45.675] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:45.737] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:45.825] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:45.857] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:45.977] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:45.978] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:46.099] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:46.128] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:46.220] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:46.279] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:46.341] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:46.430] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:46.461] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:46.581] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:46.583] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:46.703] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:46.732] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:46.824] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:46.883] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:46.945] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:47.034] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:47.065] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:47.185] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:47.186] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:47.309] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:47.336] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:47.430] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:47.487] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:47.551] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:47.637] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:47.672] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:47.789] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:47.793] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:47.939] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:48.091] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:48.092] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:08:48.092] INFO     | audio_action_controller.py:302 - 音频播放完成
[16:08:48.242] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:48.408] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:48.559] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:48.710] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:48.861] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:49.330] INFO     | audio_action_controller.py:207 - 收到音频打断信号
[16:08:49.331] INFO     | audio_action_controller.py:213 - 已停止当前音频播放
[16:08:49.331] INFO     | audio_action_controller.py:221 - 已清空音频播放队列
[16:08:49.332] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: asserts/zaine.wav
[16:08:49.332] INFO     | audio_action_controller.py:230 - 音频已添加到队列: asserts/zaine.wav
[16:08:49.332] INFO     | audio_action_controller.py:176 - 队列播放音频: asserts/zaine.wav
[16:08:49.393] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:08:49.393] INFO     | audio_action_controller.py:78 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[16:08:49.394] INFO     | audio_action_controller.py:245 - 正在加载音频: asserts/zaine.wav
[16:08:49.910] INFO     | audio_action_controller.py:50 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[16:08:49.912] INFO     | audio_action_controller.py:259 - 音频长度(从文件获取): 0.8769161105155945秒
[16:08:49.912] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:08:49.913] INFO     | audio_action_controller.py:277 - 开始播放音频
[16:08:49.913] INFO     | audio_action_controller.py:282 - 嘴部动作控制线程已启动
[16:08:49.914] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:49.914] INFO     | audio_action_controller.py:285 - 脖子动作控制线程已启动
[16:08:49.914] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:50.035] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:50.066] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:50.156] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:50.216] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:50.277] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:50.367] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:50.398] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:50.518] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:50.519] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:50.640] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:50.669] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:50.820] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:08:50.820] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:08:50.820] INFO     | audio_action_controller.py:302 - 音频播放完成
[16:08:55.474] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: temp_tts_audio.mp3
[16:08:55.474] INFO     | audio_action_controller.py:230 - 音频已添加到队列: temp_tts_audio.mp3
[16:08:55.474] INFO     | audio_action_controller.py:176 - 队列播放音频: temp_tts_audio.mp3
[16:08:55.570] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:08:55.571] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:08:55.572] INFO     | audio_action_controller.py:245 - 正在加载音频: temp_tts_audio.mp3
[16:08:55.573] ERROR    | audio_action_controller.py:309 - 播放音频失败: No file 'temp_tts_audio.mp3' found in working directory 'D:\prooject_code\Possessed_AI'.
[16:08:55.949] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: temp_tts_audio.mp3
[16:08:55.949] INFO     | audio_action_controller.py:230 - 音频已添加到队列: temp_tts_audio.mp3
[16:08:55.949] INFO     | audio_action_controller.py:176 - 队列播放音频: temp_tts_audio.mp3
[16:08:56.024] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:08:56.024] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:08:56.025] INFO     | audio_action_controller.py:245 - 正在加载音频: temp_tts_audio.mp3
[16:08:56.026] ERROR    | audio_action_controller.py:309 - 播放音频失败: No file 'temp_tts_audio.mp3' found in working directory 'D:\prooject_code\Possessed_AI'.
[16:08:56.810] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: temp_tts_audio.mp3
[16:08:56.812] INFO     | audio_action_controller.py:230 - 音频已添加到队列: temp_tts_audio.mp3
[16:08:56.812] INFO     | audio_action_controller.py:176 - 队列播放音频: temp_tts_audio.mp3
[16:08:56.899] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:08:56.899] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:08:56.901] INFO     | audio_action_controller.py:245 - 正在加载音频: temp_tts_audio.mp3
[16:08:56.902] ERROR    | audio_action_controller.py:309 - 播放音频失败: No file 'temp_tts_audio.mp3' found in working directory 'D:\prooject_code\Possessed_AI'.
[16:08:57.119] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: temp_tts_audio.mp3
[16:08:57.120] INFO     | audio_action_controller.py:230 - 音频已添加到队列: temp_tts_audio.mp3
[16:08:57.120] INFO     | audio_action_controller.py:176 - 队列播放音频: temp_tts_audio.mp3
[16:08:57.169] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:08:57.171] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:08:57.172] INFO     | audio_action_controller.py:245 - 正在加载音频: temp_tts_audio.mp3
[16:08:57.173] ERROR    | audio_action_controller.py:309 - 播放音频失败: No file 'temp_tts_audio.mp3' found in working directory 'D:\prooject_code\Possessed_AI'.
[16:09:04.760] INFO     | audio_action_controller.py:207 - 收到音频打断信号
[16:09:04.761] INFO     | audio_action_controller.py:213 - 已停止当前音频播放
[16:09:04.761] INFO     | audio_action_controller.py:221 - 已清空音频播放队列
[16:09:04.762] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: asserts/shenmeshi.wav
[16:09:04.762] INFO     | audio_action_controller.py:230 - 音频已添加到队列: asserts/shenmeshi.wav
[16:09:04.762] INFO     | audio_action_controller.py:176 - 队列播放音频: asserts/shenmeshi.wav
[16:09:04.830] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:09:04.832] INFO     | audio_action_controller.py:78 - 检测到WAV/FLAC无损音频文件，已应用高质量音频优化
[16:09:04.832] INFO     | audio_action_controller.py:245 - 正在加载音频: asserts/shenmeshi.wav
[16:09:05.033] INFO     | audio_action_controller.py:50 - 音频音量设置为: 92.0% (针对WAV无损格式优化)
[16:09:05.034] INFO     | audio_action_controller.py:259 - 音频长度(从文件获取): 0.7745804786682129秒
[16:09:05.034] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:09:05.036] INFO     | audio_action_controller.py:277 - 开始播放音频
[16:09:05.036] INFO     | audio_action_controller.py:282 - 嘴部动作控制线程已启动
[16:09:05.036] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:09:05.036] INFO     | audio_action_controller.py:285 - 脖子动作控制线程已启动
[16:09:05.037] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:09:05.157] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:09:05.188] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:09:05.278] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:09:05.339] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:09:05.399] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:09:05.490] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:09:05.520] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:09:05.641] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:09:05.641] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:09:05.794] DEBUG    | audio_action_controller.py:319 - Modbus 客户端未连接，跳过寄存器写入
[16:09:05.844] DEBUG    | audio_action_controller.py:338 - Modbus 客户端未连接，跳过线圈写入
[16:09:05.844] INFO     | audio_action_controller.py:302 - 音频播放完成
[16:09:07.062] INFO     | audio_action_controller.py:449 - 添加音频到播放队列: temp_tts_audio.mp3
[16:09:07.062] INFO     | audio_action_controller.py:230 - 音频已添加到队列: temp_tts_audio.mp3
[16:09:07.062] INFO     | audio_action_controller.py:176 - 队列播放音频: temp_tts_audio.mp3
[16:09:07.145] INFO     | audio_action_controller.py:105 - 音频系统初始化成功 - 44100Hz, 16bit, 立体声, 缓冲区2048 (针对WAV无损格式优化)
[16:09:07.147] INFO     | audio_action_controller.py:75 - 检测到MP3文件，已应用针对压缩音频的优化设置
[16:09:07.147] INFO     | audio_action_controller.py:245 - 正在加载音频: temp_tts_audio.mp3
[16:09:07.148] ERROR    | audio_action_controller.py:309 - 播放音频失败: No file 'temp_tts_audio.mp3' found in working directory 'D:\prooject_code\Possessed_AI'.
