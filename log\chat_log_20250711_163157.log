2025-07-11 16:31:59.916 - chat_with_robot - chat_with_robot.py - <module> - line 640 - INFO - use_action: dont
2025-07-11 16:31:59.917 - chat_with_robot - chat_with_robot.py - <module> - line 641 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-11 16:31:59.918 - chat_with_robot - chat_with_robot.py - init_websocket - line 319 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1752222719918&accessNonce=9678483b-01a0-434b-9b8c-48831c120b5d&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=8362b162-5e31-11f0-83b9-dc4546c07870&requestId=7e6de27a-1bdb-4db0-a31e-963bfe9f4f55_joyinside&accessSign=7f1dfef8cdd80ea7b97af9b46a50d083, request_id: 7e6de27a-1bdb-4db0-a31e-963bfe9f4f55_joyinside
2025-07-11 16:31:59.941 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-11 16:31:59.941 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-11 16:32:00.596 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-11 16:32:00.828 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-07-11 16:32:04.162 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-11 16:32:04.165 - chat_with_robot - voice.py - _setup_audio_stream - line 324 - INFO - 使用音频设备: 1
2025-07-11 16:32:04.165 - chat_with_robot - voice.py - _setup_audio_stream - line 325 - INFO - channels: 4 <class 'int'>
2025-07-11 16:32:04.165 - chat_with_robot - voice.py - _setup_audio_stream - line 326 - INFO - rate: 44100.0 <class 'float'>
2025-07-11 16:32:04.286 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-11 16:32:04.286 - chat_with_robot - voice.py - init_wakeup - line 311 - INFO - 本地流式KWS检测器启动成功
2025-07-11 16:32:05.287 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-11 16:32:05.288 - chat_with_robot - chat_with_robot.py - play_audio - line 525 - INFO - 使用统一音频控制器播放: ./asserts/ding.wav
2025-07-11 16:32:05.292 - chat_with_robot - chat_with_robot.py - play_audio - line 527 - INFO - 统一音频控制器播放完成: ./asserts/ding.wav
2025-07-11 16:32:05.292 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 84 - INFO - Windows 系统，使用统一音频控制器播放: asserts/tts/dog_ok.mp3
