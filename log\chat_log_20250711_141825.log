2025-07-11 14:18:26.597 - chat_with_robot - chat_with_robot.py - <module> - line 640 - INFO - use_action: dont
2025-07-11 14:18:26.598 - chat_with_robot - chat_with_robot.py - <module> - line 641 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-11 14:18:26.598 - chat_with_robot - chat_with_robot.py - init_websocket - line 319 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1752214706599&accessNonce=05873765-d496-4a5c-ad37-da8117ee4b34&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=db133d85-5e1e-11f0-b23d-dc4546c07870&requestId=6d87202f-8294-400a-b40e-f340af071244_joyinside&accessSign=c0efa800e262931840f7262b57bc47fd, request_id: 6d87202f-8294-400a-b40e-f340af071244_joyinside
2025-07-11 14:18:26.598 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-11 14:18:26.598 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-11 14:18:26.958 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-11 14:18:27.255 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-07-11 14:18:27.291 - chat_with_robot - voice.py - init_wakeup - line 314 - ERROR - 初始化本地流式KWS失败: No module named 'sherpa_onnx'
2025-07-11 14:18:28.292 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-11 14:18:28.293 - chat_with_robot - chat_with_robot.py - play_audio - line 525 - INFO - 使用 audio_action_controller 播放: ./asserts/ding.wav
2025-07-11 14:18:29.454 - chat_with_robot - chat_with_robot.py - play_audio - line 527 - INFO - audio_action_controller 播放完成: ./asserts/ding.wav
2025-07-11 14:18:29.454 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 84 - INFO - Windows 系统，使用 audio_action_controller 播放: asserts/tts/dog_ok.mp3
