"""
@Author: <EMAIL> <EMAIL>
@Create Date: 2025.03.28
@Description: 

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
"""
import socket
import struct
import time
import threading
import sys
import os
import subprocess
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from util.logger import logger
from robot_agent.robot_config import ROBOT_COMMANDS, ROBOT_CONFIG, ROBOT_STATES, get_robot_state

class RobotMonitor:
    def __init__(self):
        self.mon_sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        mon_ip = ROBOT_CONFIG.get("mon_ip", "*************")
        mon_port = ROBOT_CONFIG.get("mon_port", 43897)
        server_address = (mon_ip, mon_port)
        self.mon_sock.bind(server_address)
        self.robot_state = None
        self.monitor_running = True
        self.mon_thread = threading.Thread(target=self.run, daemon=True)
        self.mon_thread.start()
        
        # 电池电量相关属性
        self.battery_level = 100.0  # 初始值设为100%
        self.last_battery_alert_time = 0  # 上次电量提醒的时间
        self.battery_alert_interval = 300  # 提醒间隔(秒)，避免频繁提醒
        # 电量提醒阈值和提示信息
        self.battery_thresholds = {
            50.0: "电池电量已降至50%，请注意充电。",
            25.0: "电池电量已降至25%，请尽快充电。",
            10.0: "电池电量仅剩10%，即将关机，请立即充电。"
        }
        self.alerted_thresholds = set()  # 已经提醒过的阈值
        self.is_first_battery_reading = True  # 标记是否是第一次读取电池数据

    def run(self):
        while self.monitor_running == True:
            buffer, _ = self.mon_sock.recvfrom(1024)
            header_size = struct.calcsize('3i')
            data_size = struct.calcsize('2i18d1I1?1I1i1d1i2?2d')
            total_size = header_size + data_size
            if len(buffer) == total_size:
                code, size, cons_code = struct.unpack('3i', buffer[:header_size])
                if code == 0x0901:
                    data_format = '2i18d1I1?1I1i1d1i2?2d'
                    data = struct.unpack(data_format, buffer[header_size:])
                    basic_state = data[0]
                    gait_state = data[1]
                    motion_state = data[23]
                    self.robot_state = get_robot_state(basic_state, gait_state, motion_state)
                    # logger.info(f"robot_state: {self.robot_state}")
                    # print(f"robot_state: {self.robot_state}")
                    
                    # 提取电池电量信息
                    self.battery_level = data[24]
                    print(f"battery_state: {self.battery_level}")

                    # 首次读取电池数据时的处理
                    if self.is_first_battery_reading:
                        self.initialize_battery_alerts()
                        self.is_first_battery_reading = False

                    # 监控电池电量
                    self.monitor_battery_level()
                    
            time.sleep(0.01)
        self.on_closing()
    
    def monitor_battery_level(self):
        """监控电池电量，在达到阈值时播放语音提示"""
        current_time = time.time()
        
        # 按照从小到大的顺序排序阈值
        sorted_thresholds = sorted(self.battery_thresholds.keys())
        
        # 找到当前电量对应的区间
        current_threshold = None
        for threshold in sorted_thresholds:
            if self.battery_level <= threshold:
                current_threshold = threshold
                break
        
        # 如果找到了对应区间，且未提醒过，且满足时间间隔要求
        if (current_threshold is not None and 
            current_threshold not in self.alerted_thresholds and 
            current_time - self.last_battery_alert_time > self.battery_alert_interval):
            
            message = self.battery_thresholds[current_threshold]
            logger.warning(f"电池电量警告: {self.battery_level:.1f}% - {message}")
            
            # 播放语音提示
            self.play_battery_alert(current_threshold, message)
            
            # 更新提醒状态
            self.alerted_thresholds.add(current_threshold)
            # 同时标记更高级别的阈值为已提醒
            for threshold in sorted_thresholds:
                if threshold > current_threshold:
                    self.alerted_thresholds.add(threshold)
            
            self.last_battery_alert_time = current_time
        
        # 如果电量恢复到某个阈值以上，从已提醒集合中移除
        for threshold in list(self.alerted_thresholds):
            if self.battery_level > threshold + 5.0:  # 添加5%的滞后，防止在阈值附近波动
                self.alerted_thresholds.remove(threshold)
    
    def play_battery_alert(self, threshold, message):
        """播放电池电量提醒"""
        try:
            # 判断阈值，使用不同的语音文件
            if threshold == 50.0:
                audio_file = '/home/<USER>/Possessed_AI/asserts/tts/charge05.mp3'
            elif threshold == 25.0:
                audio_file = '/home/<USER>/Possessed_AI/asserts/tts/charge025.mp3'
            elif threshold == 10.0:
                audio_file = '/home/<USER>/Possessed_AI/asserts/tts/charge010.mp3'
            else:
                audio_file = '/home/<USER>/Possessed_AI/asserts/tts/battery_low.mp3'
            
            # 检查文件是否存在，如果不存在则创建默认文件
            if not os.path.exists(audio_file):
                # 确保asserts/tts目录存在
                os.makedirs(os.path.dirname(audio_file), exist_ok=True)
                # 使用系统命令生成语音文件
                os.system(f'espeak -v zh "{message}" --stdout > {audio_file}')
            
            # 使用subprocess播放音频
            subprocess.run(['play', audio_file], check=True, stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL)
            
            logger.info(f"已播放电池电量提醒: {message}")
        except Exception as e:
            logger.error(f"播放电池提示音频时出错: {e}")
    
    def on_closing(self):
        logger.info("关闭机器人监控器")
        self.mon_thread.join()
        self.mon_sock.close()

    def initialize_battery_alerts(self):
        """初始化电池警报状态，将所有已经低于的阈值标记为已提醒"""
        sorted_thresholds = sorted(self.battery_thresholds.keys(), reverse=True)
        for threshold in sorted_thresholds:
            if self.battery_level <= threshold:
                # 将所有低于当前电量的阈值标记为已提醒
                self.alerted_thresholds.add(threshold)
                logger.info(f"初始化：电量已低于 {threshold}%，标记为已提醒")

if __name__ == "__main__":
    monitor = RobotMonitor()
    try:
        print("监控机器人状态中，按Ctrl+C停止...")
        # 保持程序运行并等待状态更新
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("停止监控")
        monitor.monitor_running = False
        time.sleep(0.5)
