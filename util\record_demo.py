"""
@Author: <EMAIL> <EMAIL>
@Create Date: 2025.03.28
@Description: 

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
"""
import sounddevice as sd
import numpy as np
import time
import sounddevice as sd

# 列出所有音频输入设备
print("音频输入设备列表:")
for i in range(sd.query_devices()):
    info = sd.query_devices(i)
    if info['max_input_channels'] > 0:
        print(f"{i}: {info['name']}")
# 用于存储录音数据的列表
recorded_data = []

def audio_callback(indata, frames, time, status):
    if status:
        print(f"Status: {status}")
    print(f"Frames: {frames}")
    print(f"Max value in indata: {np.max(indata)}")  # 查看音频数据的最大值
    recorded_data.append(indata.copy())

# 设置录音流，使用 blocksize 参数代替 frames_per_buffer
with sd.InputStream(callback=audio_callback, channels=1, samplerate=8000, blocksize=1024) as stream:
    print("开始录制...")
    time.sleep(10)  # 录制10秒钟

# 将录制的数据合并成一个数组
recorded_audio = np.concatenate(recorded_data, axis=0)

# 保存为 WAV 文件（如果需要）
import scipy.io.wavfile as wav
wav.write('recorded_audio.wav', 8000, recorded_audio)
print("录制完成，文件已保存为 'recorded_audio.wav'")
