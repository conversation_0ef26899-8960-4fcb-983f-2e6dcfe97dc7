"""
@Author: <EMAIL> <EMAIL>
@Create Date: 2025.03.28
@Description: 

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
"""
import socket
import struct
import time
import threading
import sys
import os
import subprocess
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from util.logger import logger
from robot_agent.robot_config import ROBOT_COMMANDS, ROBOT_CONFIG, ROBOT_STATES, get_robot_state

class RobotMonitor:
    def __init__(self):
        self.mon_sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        mon_ip = ROBOT_CONFIG.get("mon_ip", "*************")
        mon_port = ROBOT_CONFIG.get("mon_port", 43897)
        server_address = (mon_ip, mon_port)
        self.mon_sock.bind(server_address)
        self.robot_state = None
        self.monitor_running = True
        self.mon_thread = threading.Thread(target=self.run, daemon=True)
        self.mon_thread.start()
        
        # 电量提醒阈值和提示信息
        self.battery_thresholds = {
            50.0: "电池电量已降至50%，请注意充电。",
            25.0: "电池电量已降至25%，请尽快充电。",
            10.0: "电池电量仅剩10%，即将关机，请立即充电。"
        }
        # 记录已经播放过提醒的阈值
        self.alerted_thresholds = set()

    def run(self):
        while self.monitor_running == True:
            buffer, _ = self.mon_sock.recvfrom(1024)
            header_size = struct.calcsize('3i')
            data_size = struct.calcsize('2i18d1I1?1I1i1d1i2?2d')
            total_size = header_size + data_size
            if len(buffer) == total_size:
                code, size, cons_code = struct.unpack('3i', buffer[:header_size])
                if code == 0x0901:
                    data_format = '2i18d1I1?1I1i1d1i2?2d'
                    data = struct.unpack(data_format, buffer[header_size:])
                    basic_state = data[0]
                    gait_state = data[1]
                    motion_state = data[23]
                    self.robot_state = get_robot_state(basic_state, gait_state, motion_state)
                    # logger.info(f"robot_state: {self.robot_state}")
                    # print(f"robot_state: {self.robot_state}")

                    # 提取电池电量信息
                    self.battery_level = data[24]
                    # print(f"battery_state: {self.battery_level}")

                    # 监控电池电量
                    self.monitor_battery_level()

            time.sleep(0.01)
        self.on_closing()
    
    def monitor_battery_level(self):
        """监控电池电量，在达到阈值时播放语音提示"""
        for threshold, message in self.battery_thresholds.items():
            # 如果电量低于阈值且之前没有播放过该阈值的提醒
            if self.battery_level == threshold and threshold not in self.alerted_thresholds:  # not <= 
                self.play_battery_alert(threshold, message)
                self.alerted_thresholds.add(threshold)
                logger.info(f"播放电量提醒: {threshold}%, {message}")
                break
            # 如果电量恢复到阈值以上，从已提醒列表中移除
            elif self.battery_level > threshold and threshold in self.alerted_thresholds:
                self.alerted_thresholds.remove(threshold)

    def play_battery_alert(self, threshold, message):
        """播放电池电量提醒音频"""
        audio_path = "/home/<USER>/Possessed_AI/asserts/tts"

        if threshold == 50.0:
            audio_file = f"{audio_path}/charge050.mp3"
        elif threshold == 25.0:
            audio_file = f"{audio_path}/charge025.mp3"
        elif threshold == 10.0:
            audio_file = f"{audio_path}/charge010.mp3"
        else:
            return

        try:
            # 使用统一音频控制器播放音频（支持嘴巴动作）
            from utils.unified_audio_controller import play_audio_file
            logger.info(f"使用统一音频控制器播放电量提醒: {audio_file}")
            play_audio_file(audio_file)
        except Exception as e:
            logger.error(f"使用统一音频控制器播放失败，回退到系统播放: {e}")
            try:
                # 回退到系统播放（无嘴巴动作）
                subprocess.run(['play', audio_file], check=True, stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL)
            except Exception as e2:
                logger.error(f"播放电量提醒音频失败: {e2}")

    def on_closing(self):
        logger.info("关闭机器人监控器")
        self.mon_thread.join()
        self.mon_sock.close()

if __name__ == "__main__":
    monitor = RobotMonitor()
    try:
        print("监控机器人状态中，按Ctrl+C停止...")
        # 保持程序运行并等待状态更新
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("停止监控")
        monitor.monitor_running = False
        time.sleep(0.5)
