
import simpleaudio as sa
import os

wav_path = "asserts/zaine.wav"

if not os.path.exists(wav_path):
    print(f"错误: 文件未找到 '{wav_path}'")
    print(f"当前工作目录: {os.getcwd()}")
else:
    try:
        print(f"尝试播放: {wav_path}")
        wave_obj = sa.WaveObject.from_wave_file(wav_path)
        print("文件加载成功.")
        play_obj = wave_obj.play()
        print("开始播放...")
        play_obj.wait_done()
        print("播放完成.")
    except Exception as e:
        print(f"播放时发生错误: {e}")

"""
import sounddevice as sd
import soundfile as sf
import os

wav_path = "asserts/zaine.wav"
# wav_path = "HardwareAIAgent_web/asserts/zaine.wav"

if not os.path.exists(wav_path):
    print(f"错误: 文件未找到 '{wav_path}'")
    print(f"当前工作目录: {os.getcwd()}")
else:
    try:
        print("可用的音频设备:")
        devices = sd.query_devices()
        print(devices) # 打印所有设备信息

        # -- 设备选择 --
        selected_device_id = None
        while selected_device_id is None:
            try:
                choice = input(f"请输入要使用的输出设备 ID (0-{len(devices)-1})，或直接回车使用默认设备: ")
                if not choice: # 用户回车==使用默认设备
                    selected_device_id = sd.default.device[1] # 获取默认输出设备的ID
                    print(f"使用默认输出设备 ID: {selected_device_id}")
                    break
                choice_int = int(choice)
                # 检查ID是否有效并且设备支持输出
                if 0 <= choice_int < len(devices) and devices[choice_int]['max_output_channels'] > 0:
                    selected_device_id = choice_int
                    print(f"已选择设备 ID: {selected_device_id} ({devices[selected_device_id]['name']})")
                else:
                    print("无效的设备 ID 或该设备不支持输出，请重新输入。")
            except ValueError:
                print("输入无效，请输入数字。")
            except Exception as e:
                print(f"选择设备时出错: {e}")
        # -- 结束设备选择 --

        # 尝试读取 WAV 文件
        data, fs = sf.read(wav_path, dtype='float32')
        print(f"文件加载成功: {wav_path}, 采样率: {fs} Hz")

        print(f"开始播放 (使用设备 ID: {selected_device_id})...")
        # 使用选择的设备进行播放
        sd.play(data, fs, device=selected_device_id)

        # 等待播放完成
        duration = len(data) / fs
        print(f"音频时长: {duration:.2f} 秒")
        sd.wait() # 等待播放完成

        print("播放完成.")

    except Exception as e:
        print(f"播放时发生错误: {e}")
"""