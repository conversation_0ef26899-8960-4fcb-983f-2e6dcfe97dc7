2025-06-30 16:25:16.441 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-06-30 16:25:16.443 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-06-30 16:25:16.457 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751271916458&accessNonce=c772218f-6944-42a8-b7ea-3711f77761ef&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=c05c5802-558b-11f0-9d1d-dc4546c07870&requestId=d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside&accessSign=af96637af32d1c283e8ebfaac9053093, request_id: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside
2025-06-30 16:25:16.458 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-06-30 16:25:16.459 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-06-30 16:25:16.980 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-06-30 16:25:17.163 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-06-30 16:25:18.758 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-06-30 16:25:18.759 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-06-30 16:25:18.759 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-06-30 16:25:18.759 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-06-30 16:25:18.790 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-06-30 16:25:18.790 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-06-30 16:30:03.155 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:30:03.155 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:30:03.222 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:30:03.222 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:30:04.531 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:30:04.542 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:30:08.095 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:30:08.095 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:30:08.154 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:30:08.154 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:30:09.188 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:30:09.256 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:30:11.440 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东，你会做什么？, 时间戳: 2025-06-30 16:30:17.198000
2025-06-30 16:30:11.832 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:30:11.833 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:30:11.837 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 你好，东东，你会做什么？; 响应时间: 0; JD机器人回复: 
2025-06-30 16:30:11.837 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:30:11.837 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:30:11.837 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:30:19.403 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:30:19.403 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:30:19.469 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:30:19.469 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:30:20.799 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:30:20.866 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:30:21.716 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-06-30 16:30:27.474000
2025-06-30 16:30:22.238 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:30:22.242 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:30:22.246 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 
2025-06-30 16:30:22.246 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:30:22.246 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:30:22.246 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:30:22.248 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:30:22.248 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:30:22.350 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:30:24.910 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你在做什么？, 时间戳: 2025-06-30 16:30:30.668000
2025-06-30 16:30:25.302 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:30:25.303 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:30:25.306 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 你在做什么？; 响应时间: 0; JD机器人回复: 
2025-06-30 16:30:25.306 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:30:25.306 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:30:25.306 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:30:26.813 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:30:29.134 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 好听的一个还是没穿衣服的, 时间戳: 2025-06-30 16:30:34.890000
2025-06-30 16:30:29.418 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:30:29.421 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:30:29.432 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 好听的一个还是没穿衣服的; 响应时间: 0; JD机器人回复: 
2025-06-30 16:30:29.432 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:30:29.432 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:30:29.432 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:30:29.793 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:30:32.430 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:30:32.431 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:30:40.717 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 这是义务吗？这是义务吗？, 时间戳: 2025-06-30 16:30:46.471000
2025-06-30 16:30:41.010 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:30:41.011 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:30:41.013 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 这是义务吗？这是义务吗？; 响应时间: 0; JD机器人回复: 
2025-06-30 16:30:41.013 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:30:41.013 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:30:41.013 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:30:42.469 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 跑出来为什么那么抽象啊？, 时间戳: 2025-06-30 16:30:48.226000
2025-06-30 16:30:42.792 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:30:42.792 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:30:42.804 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 跑出来为什么那么抽象啊？; 响应时间: 0; JD机器人回复: 
2025-06-30 16:30:42.804 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:30:42.804 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:30:42.804 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:30:49.412 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 这个贴去不上但是, 时间戳: 2025-06-30 16:30:55.165000
2025-06-30 16:30:49.699 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:30:49.700 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:30:49.703 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 这个贴去不上但是; 响应时间: 0; JD机器人回复: 
2025-06-30 16:30:49.703 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:30:49.703 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:30:49.703 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:30:50.441 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:30:56.856 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: model1你嘛哎你那个的接接, 时间戳: 2025-06-30 16:31:02.614000
2025-06-30 16:30:57.296 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:30:57.299 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:30:57.299 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:30:57.300 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:30:57.308 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: model1你嘛哎你那个的接接; 响应时间: 0; JD机器人回复: 
2025-06-30 16:30:57.308 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:30:57.308 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:30:57.308 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:30:57.400 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:31:00.119 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 刚才你, 时间戳: 2025-06-30 16:31:05.875000
2025-06-30 16:31:00.651 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:31:00.656 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:31:00.656 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:31:00.657 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:31:00.665 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 刚才你; 响应时间: 0; JD机器人回复: 
2025-06-30 16:31:00.665 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:31:00.665 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:31:00.665 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:31:00.758 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:31:00.835 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:31:00.835 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:31:00.896 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:31:00.896 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:31:01.929 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:31:01.996 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:31:02.624 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-06-30 16:31:08.379000
2025-06-30 16:31:03.542 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:31:09.295000
2025-06-30 16:31:03.542 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 916
2025-06-30 16:31:03.609 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:31:03.618 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:31:03.619 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:31:03.938 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:31:03.940 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:31:04.277 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:31:04.278 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:31:04.523 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:31:04.524 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:31:04.589 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:31:04.589 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:31:04.619 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:31:04.622 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:31:04.627 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:31:04.996 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:31:04.996 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:31:05.003 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:31:05.015 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 你好呀！我是JoyInside，一个活泼开朗的AI小伙伴！我对动漫、游戏、科技啥的都超感兴趣，有啥想聊的尽管说，我随时在线陪你玩！😄 最近有啥新鲜事吗？
2025-06-30 16:31:05.015 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:31:05.015 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:31:05.906 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:31:05.974 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:31:08.700 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:31:10.620 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:31:10.620 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:31:13.394 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我陪你玩最近有新鲜事吗你会做什么, 时间戳: 2025-06-30 16:31:19.152000
2025-06-30 16:31:13.717 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:31:13.718 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:31:13.723 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 我陪你玩最近有新鲜事吗你会做什么; 响应时间: 0; JD机器人回复: 
2025-06-30 16:31:13.723 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:31:13.723 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:31:13.723 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:31:14.929 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:31:16.476 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:31:16.476 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:31:20.224 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:31:20.224 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:31:28.630 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:31:28.632 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:31:31.033 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:31:31.033 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:31:34.368 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:31:34.369 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:31:37.372 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:31:37.372 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:31:39.512 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:31:39.512 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:31:46.034 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:31:46.035 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:31:49.137 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:31:49.137 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:31:50.704 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:31:50.705 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:31:54.439 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:31:54.440 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:32:01.670 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:32:01.671 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:32:05.139 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:32:05.140 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:32:06.415 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:32:06.415 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:32:11.801 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:32:11.801 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:32:11.868 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:32:11.869 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:32:12.902 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:32:12.969 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:32:14.816 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东，你会做什么？, 时间戳: 2025-06-30 16:32:20.547000
2025-06-30 16:32:15.193 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:32:15.193 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:32:15.200 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 你好，东东，你会做什么？; 响应时间: 0; JD机器人回复: 
2025-06-30 16:32:15.200 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:32:15.200 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:32:15.200 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:32:17.338 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:32:19.879 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:32:19.880 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:32:26.885 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:32:26.886 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:32:27.898 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:32:27.899 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:32:36.316 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:32:36.316 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:32:36.383 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:32:36.383 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:32:37.419 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:32:37.485 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:32:39.051 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好你好，你好东东, 时间戳: 2025-06-30 16:32:44.807000
2025-06-30 16:32:39.455 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:32:39.455 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:32:39.462 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 你好你好，你好东东; 响应时间: 0; JD机器人回复: 
2025-06-30 16:32:39.462 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:32:39.462 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:32:39.462 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:32:42.533 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:32:42.533 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:32:42.595 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:32:42.595 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:32:43.618 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:32:43.686 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:32:47.928 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好东东，你会做什么？, 时间戳: 2025-06-30 16:32:53.685000
2025-06-30 16:32:48.264 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-06-30 16:32:48.264 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:32:48.266 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: ，你好东东，你会做什么？; 响应时间: 0; JD机器人回复: 
2025-06-30 16:32:48.266 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:32:48.266 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:32:48.266 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:32:48.476 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:32:49.446 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:32:49.446 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:32:53.562 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:32:53.564 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:32:55.202 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:32:55.203 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:32:57.996 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:32:57.997 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-06-30 16:32:58.693 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:32:58.693 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:32:58.756 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:32:58.756 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:33:00.049 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:33:00.118 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:33:01.679 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东，退下吧, 时间戳: 2025-06-30 16:33:07.435000
2025-06-30 16:33:02.284 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:33:08.039000
2025-06-30 16:33:02.284 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 604
2025-06-30 16:33:02.372 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:02.373 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:33:05.464 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:33:05.464 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:33:05.529 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:33:05.529 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:33:06.813 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:33:06.823 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:33:09.499 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，今天天气怎么样？, 时间戳: 2025-06-30 16:33:15.257000
2025-06-30 16:33:10.223 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:33:10.228 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:33:10.233 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:33:10.233 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:33:10.237 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: ，今天天气怎么样？; 响应时间: 0; JD机器人回复: 
2025-06-30 16:33:10.237 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:33:10.237 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:33:10.238 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:33:10.334 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:33:12.950 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:33:18.700000
2025-06-30 16:33:12.950 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 3443
2025-06-30 16:33:13.040 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:13.047 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:13.055 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:33:13.340 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:13.350 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:13.643 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:13.653 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:13.971 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:13.977 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:14.021 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:33:14.023 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:33:14.027 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:33:14.027 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:33:14.030 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: ，今天天气怎么样？; 响应时间: 0; JD机器人回复: 今天北京有雷阵雨，温度在23°到31°之间，体感温度32°，南风3级，相对湿度59%，空气质量一般。出门记得带伞，小心路滑，
2025-06-30 16:33:14.030 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:33:14.030 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:33:14.030 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:33:14.061 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:33:14.128 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:33:17.377 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天北京, 时间戳: 2025-06-30 16:33:23.133000
2025-06-30 16:33:18.791 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:33:24.545000
2025-06-30 16:33:18.793 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1412
2025-06-30 16:33:18.840 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:33:18.844 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:33:18.848 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:33:18.848 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:33:18.849 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 今天北京; 响应时间: 0; JD机器人回复: 
2025-06-30 16:33:18.849 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:33:18.849 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:33:18.849 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:33:18.949 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:33:21.228 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-06-30 16:33:21.228 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-06-30 16:33:21.291 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:33:21.291 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:33:22.591 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-06-30 16:33:22.659 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-06-30 16:33:24.065 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好东东，你好东东, 时间戳: 2025-06-30 16:33:29.821000
2025-06-30 16:33:24.760 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:33:24.765 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:33:24.768 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:33:24.768 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:33:24.769 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 你好东东，你好东东; 响应时间: 0; JD机器人回复: 
2025-06-30 16:33:24.769 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:33:24.769 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:33:24.769 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:33:24.869 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:33:27.408 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 天气怎么样？, 时间戳: 2025-06-30 16:33:33.165000
2025-06-30 16:33:28.439 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:33:34.195000
2025-06-30 16:33:28.440 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1030
2025-06-30 16:33:28.504 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:28.514 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:28.514 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:33:28.879 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:28.882 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:29.211 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:29.211 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:29.447 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:33:29.447 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:33:29.450 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 天气怎么样？; 响应时间: 0; JD机器人回复: 今天北京的天气是雷阵雨，温度在23°到31°之间。现在的温度是31°，
2025-06-30 16:33:29.450 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:33:29.450 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:33:29.450 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:33:29.450 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:33:29.451 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:33:29.522 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:33:29.552 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:33:32.693 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你要咚咚, 时间戳: 2025-06-30 16:33:38.449000
2025-06-30 16:33:33.953 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:33:39.706000
2025-06-30 16:33:33.953 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1257
2025-06-30 16:33:34.013 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:34.023 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:34.034 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:33:34.347 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:34.356 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:34.669 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:34.678 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:35.026 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:35.035 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:35.320 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:33:35.321 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:33:35.324 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:33:35.324 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:33:35.327 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 你要咚咚; 响应时间: 0; JD机器人回复: 今天北京的天气是雷阵雨，温度在23°到31°之间。现在的温度是31°，体感温度为32°，有南风3级，
2025-06-30 16:33:35.327 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:33:35.327 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:33:35.327 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:33:35.343 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:33:35.425 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:33:37.169 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 今天北京的天气, 时间戳: 2025-06-30 16:33:42.926000
2025-06-30 16:33:38.398 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:33:44.154000
2025-06-30 16:33:38.400 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1228
2025-06-30 16:33:38.461 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:38.472 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:38.476 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:33:38.756 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:38.765 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:39.062 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:39.062 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:39.410 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:39.417 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:39.744 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:39.752 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:39.880 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:33:39.881 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:33:39.892 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 今天北京的天气; 响应时间: 0; JD机器人回复: 今天北京的天气是雷阵雨，温度在23°到31°之间。现在的温度是31°，体感温度为32°，有南风3级，相对湿度为59%，空气质量一般，
2025-06-30 16:33:39.892 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:33:39.892 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:33:39.893 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:33:39.893 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:33:39.893 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:33:39.988 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:33:39.994 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:33:44.950 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:33:50.704000
2025-06-30 16:33:44.950 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 7778
2025-06-30 16:33:45.019 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:45.029 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:45.040 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:33:45.278 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:33:45.285 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:33:45.286 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 今天北京的天气; 响应时间: 0; JD机器人回复: 今天北京的天气是雷阵雨，
2025-06-30 16:33:45.286 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:33:45.286 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:33:45.286 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:33:45.287 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:33:45.287 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:33:45.343 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:33:45.389 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:33:49.687 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:33:50.577 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:33:50.764 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:33:56.518000
2025-06-30 16:33:50.764 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 13592
2025-06-30 16:33:50.828 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:50.829 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:50.831 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:33:51.136 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:51.144 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:51.460 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:51.471 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:51.789 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:51.797 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:51.880 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:33:51.880 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:33:51.883 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:33:51.883 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:33:51.884 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: 今天北京的天气; 响应时间: 0; JD机器人回复: 今天北京的天气是雷阵雨，温度在23°到31°之间。现在的温度是31°，体感温度为32°，有3级的南风，
2025-06-30 16:33:51.884 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:33:51.884 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:33:51.884 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:33:51.944 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:33:51.984 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:33:55.231 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，今天北京的, 时间戳: 2025-06-30 16:34:00.988000
2025-06-30 16:33:56.401 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:34:02.148000
2025-06-30 16:33:56.401 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1160
2025-06-30 16:33:56.473 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:56.481 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:56.491 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:33:56.774 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:56.783 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:57.078 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:57.080 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:57.230 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:33:57.233 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:33:57.233 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:33:57.236 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:33:57.243 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: ，今天北京的; 响应时间: 0; JD机器人回复: 今天北京有雷阵雨，温度和昨天差不多，现在31度，体感温度32度，空气一般，风力为3级南风，
2025-06-30 16:33:57.243 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:33:57.243 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:33:57.243 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:33:57.298 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:33:57.334 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:33:59.220 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:34:04.973000
2025-06-30 16:33:59.220 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 3985
2025-06-30 16:33:59.289 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:59.292 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:59.298 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:33:59.590 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:33:59.599 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:33:59.621 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:33:59.623 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:33:59.623 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:33:59.627 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:33:59.632 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: ，今天北京的; 响应时间: 0; JD机器人回复: 今天北京有雷阵雨，温度和昨天差不多，现在31度，有风，空气一般。
2025-06-30 16:33:59.632 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:33:59.632 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:33:59.632 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:33:59.700 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:33:59.724 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:34:01.604 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:34:02.495 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-06-30 16:34:02.666 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-06-30 16:34:08.418000
2025-06-30 16:34:02.667 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 7430
2025-06-30 16:34:02.735 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:34:02.746 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:34:02.746 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-06-30 16:34:03.048 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:34:03.060 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:34:03.361 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-06-30 16:34:03.366 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-06-30 16:34:03.689 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-06-30 16:34:03.695 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-06-30 16:34:03.697 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: c05c5802-558b-11f0-9d1d-dc4546c07870; requestId: d99da429-d34c-4d37-aa52-1a737162d1f9_joyinside; asr: ，今天北京的; 响应时间: 0; JD机器人回复: 今天北京的天气是雷阵雨，温度在23°到31°之间。现在的温度是31°，
2025-06-30 16:34:03.697 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-06-30 16:34:03.697 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-06-30 16:34:03.700 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-06-30 16:34:03.700 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-06-30 16:34:03.700 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-06-30 16:34:03.753 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-06-30 16:34:03.801 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-06-30 16:34:05.284 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-06-30 16:34:05.284 - chat_with_robot - voice.py - stop - line 401 - INFO - 已停止local_streaming检测器
