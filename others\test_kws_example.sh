#!/bin/bash

# 确保脚本在HardwareAIAgent_web目录下执行
cd "$(dirname "$0")"

# 设置环境变量
export LD_LIBRARY_PATH="$(pwd)/kws/cpp_demo:$LD_LIBRARY_PATH"

# 显示环境变量
echo "LD_LIBRARY_PATH=$LD_LIBRARY_PATH"
echo "当前目录: $(pwd)"

# 确认示例音频文件存在
if [ -f "kws/cpp_demo/recording_1742536222_2.wav" ]; then
    echo "示例音频文件存在"
    
    # 直接运行C++可执行文件进行测试
    echo -e "\n=== 直接运行C++可执行文件 ==="
    cd kws/cpp_demo
    ./kws_tiny_tranducer_simulate_stream_main 40 50 10 models recording_1742536222_2.wav
    cd ../..
    
    # 使用Python测试脚本
    echo -e "\n=== 使用Python测试脚本 ==="
    python test_kws.py --file kws/cpp_demo/recording_1742536222_2.wav
else
    echo "错误: 示例音频文件不存在"
    exit 1
fi 