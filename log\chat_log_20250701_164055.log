2025-07-01 16:40:56.824 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-07-01 16:40:56.824 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-01 16:40:56.840 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751359256840&accessNonce=3e43754f-bde7-4cdb-8887-619966262e86&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=1b4911bb-5657-11f0-8238-dc4546c07870&requestId=8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside&accessSign=fe7bdebe5060ed853e35e7b1398aefd1, request_id: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside
2025-07-01 16:40:56.841 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-01 16:40:56.842 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-01 16:40:57.435 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-01 16:40:57.673 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-07-01 16:40:59.254 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-01 16:40:59.254 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 0
2025-07-01 16:40:59.256 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 2 <class 'int'>
2025-07-01 16:40:59.256 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-07-01 16:40:59.315 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-01 16:40:59.315 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-07-01 16:46:03.176 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 16:46:03.176 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 16:46:03.241 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 16:46:03.241 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 16:46:04.363 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 16:46:04.367 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 16:46:06.338 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 16:46:06.339 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 16:46:08.139 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 16:46:08.140 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 16:48:20.153 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: So., 时间戳: 2025-07-01 16:48:26.282000
2025-07-01 16:48:20.905 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 16:48:27.026000
2025-07-01 16:48:20.905 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 744
2025-07-01 16:48:20.943 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:48:20.949 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:48:20.960 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:48:21.223 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:48:21.234 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:48:21.507 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 16:48:21.508 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 16:48:21.515 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 16:48:21.526 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: So.; 响应时间: 0; JD机器人回复: Sure, what's up? Let's chat in English! What do you wanna talk about?
2025-07-01 16:48:21.526 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 16:48:21.528 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 16:48:22.175 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:48:23.587 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 16:48:25.301 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 16:48:25.301 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 16:51:35.509 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:07:34.310 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:07:34.311 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:07:34.311 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 17:07:34.311 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 17:07:34.374 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 17:07:34.374 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 17:07:35.415 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 17:07:35.476 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 17:07:40.586 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:07:40.587 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:07:44.050 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:07:44.051 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:07:56.053 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: , hmm., 时间戳: 2025-07-01 17:08:02.174000
2025-07-01 17:07:56.763 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 17:08:02.884000
2025-07-01 17:07:56.763 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 710
2025-07-01 17:07:56.798 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:07:56.802 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:07:56.804 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:07:57.099 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:07:57.106 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:07:57.373 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:07:57.384 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:07:57.614 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:07:57.672 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:07:57.672 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:07:57.677 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:07:57.688 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: , hmm.; 响应时间: 0; JD机器人回复: Hey there! How's your day going? What's on your mind? Let's keep the convo going!
2025-07-01 17:07:57.688 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:07:57.688 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:07:58.724 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:07:59.932 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:08:01.644 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 17:08:01.645 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:12:51.150 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: , hmm., 时间戳: 2025-07-01 17:12:57.272000
2025-07-01 17:12:51.983 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 17:12:58.102000
2025-07-01 17:12:51.984 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 830
2025-07-01 17:12:52.021 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:12:52.027 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:12:52.030 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:12:52.299 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:12:52.307 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:12:52.551 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:12:52.563 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:12:52.589 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:12:52.596 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:12:52.609 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:12:52.618 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: , hmm.; 响应时间: 0; JD机器人回复: Hey! What's up? How's your day going? Let's chat!
2025-07-01 17:12:52.619 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:12:52.619 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:12:53.241 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:12:54.551 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:12:55.357 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 17:12:55.357 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:14:22.813 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:14:41.667 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:14:41.668 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:19:37.109 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:19:37.109 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:22:36.153 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-01 17:22:42.272000
2025-07-01 17:22:36.474 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:22:36.475 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:22:36.484 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 17:22:36.484 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:22:36.484 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:22:36.484 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:22:39.727 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:22:42.037 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:22:49.042 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 17:22:49.043 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 17:22:49.106 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 17:22:49.106 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 17:22:50.244 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 17:22:50.306 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 17:23:00.881 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，7块八, 时间戳: 2025-07-01 17:23:06.999000
2025-07-01 17:23:01.198 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:23:01.198 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:23:01.207 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，7块八; 响应时间: 0; JD机器人回复: 
2025-07-01 17:23:01.207 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:23:01.208 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:23:01.208 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:23:08.506 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 性建设银行也有你要不要试试, 时间戳: 2025-07-01 17:23:14.623000
2025-07-01 17:23:08.768 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:23:08.769 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:23:08.777 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 性建设银行也有你要不要试试; 响应时间: 0; JD机器人回复: 
2025-07-01 17:23:08.777 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:23:08.777 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:23:08.777 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:23:10.345 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:23:23.358 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 农行是只有四川有, 时间戳: 2025-07-01 17:23:29.475000
2025-07-01 17:23:24.549 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 17:23:30.658000
2025-07-01 17:23:24.549 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1183
2025-07-01 17:23:24.594 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:23:24.606 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:23:24.611 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:23:24.894 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:23:24.898 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:23:25.263 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:23:25.263 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:23:25.272 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:23:25.283 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 农行是只有四川有; 响应时间: 0; JD机器人回复: 农行全称是中国农业银行，在全国各地都有分支机构，不仅仅在四川。你可以通过官网或客服查询附近网点。
2025-07-01 17:23:25.283 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:23:25.283 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:23:26.931 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:23:29.218 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:23:29.246 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:23:31.256 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: too many., 时间戳: 2025-07-01 17:23:37.373000
2025-07-01 17:23:32.537 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 17:23:38.654000
2025-07-01 17:23:32.537 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1281
2025-07-01 17:23:32.538 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 17:23:32.539 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 17:23:32.538 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:23:32.570 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 17:23:32.579 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:23:32.581 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:23:32.581 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:23:32.640 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-07-01 17:23:32.849 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:23:32.854 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:23:33.179 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:23:33.187 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:23:33.214 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:23:33.214 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:23:33.219 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:23:33.230 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: too many.; 响应时间: 0; JD机器人回复: No worries! How can I help you today? Let's keep it simple and fun! 😊
2025-07-01 17:23:33.230 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:23:33.230 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:23:33.588 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:23:35.200 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:23:37.116 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 17:23:37.116 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:23:37.656 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 农行是建设银行，是10块钱, 时间戳: 2025-07-01 17:23:43.773000
2025-07-01 17:23:37.942 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:23:37.943 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:23:37.951 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 农行是建设银行，是10块钱; 响应时间: 0; JD机器人回复: 
2025-07-01 17:23:37.951 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:23:37.951 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:23:37.951 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:23:48.177 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:23:52.926 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，就是满10块减10块的样子，大概应该是这种, 时间戳: 2025-07-01 17:23:59.041000
2025-07-01 17:23:53.228 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:23:53.234 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:23:53.244 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，就是满10块减10块的样子，大概应该是这种; 响应时间: 0; JD机器人回复: 
2025-07-01 17:23:53.244 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:23:53.244 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:23:53.244 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:23:55.028 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: have a nice day., 时间戳: 2025-07-01 17:24:01.146000
2025-07-01 17:23:55.891 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 17:24:02.007000
2025-07-01 17:23:55.891 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 861
2025-07-01 17:23:55.929 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:23:55.935 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:23:55.935 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:23:56.235 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:23:56.236 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:23:56.278 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:23:56.290 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:23:56.291 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:23:56.301 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: have a nice day.; 响应时间: 0; JD机器人回复: Thanks! You too! Have a great day!
2025-07-01 17:23:56.301 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:23:56.301 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:23:57.144 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:23:58.356 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 17:23:58.356 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:23:59.904 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:23:59.905 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:24:01.473 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:24:01.473 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:24:03.418 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 要用建行的那个APP, 时间戳: 2025-07-01 17:24:09.534000
2025-07-01 17:24:04.742 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 17:24:10.858000
2025-07-01 17:24:04.742 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1324
2025-07-01 17:24:04.819 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:24:04.821 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:24:04.822 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:24:05.159 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:24:05.160 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:24:05.161 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:24:05.172 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 要用建行的那个APP; 响应时间: 0; JD机器人回复: 建行的APP是“中国建设银行”手机银行，可以下载使用，方便办理各种业务。
2025-07-01 17:24:05.172 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:24:05.172 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:24:05.323 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ,对, 时间戳: 2025-07-01 17:24:11.431000
2025-07-01 17:24:05.607 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:24:05.608 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:24:08.452 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:24:11.676 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 17:24:11.677 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:24:11.689 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ,对; 响应时间: 0; JD机器人回复: 
2025-07-01 17:24:11.689 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:24:11.689 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:24:11.689 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:24:16.580 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:24:21.028 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:24:21.029 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:26:35.492 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 17:26:35.492 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 17:26:35.556 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 17:26:35.556 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 17:26:36.852 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 17:26:36.914 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 17:28:02.690 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，比如说，你领了，你你领了两个5块嘛, 时间戳: 2025-07-01 17:28:08.806000
2025-07-01 17:28:02.985 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:28:02.986 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:28:02.996 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，比如说，你领了，你你领了两个5块嘛; 响应时间: 0; JD机器人回复: 
2025-07-01 17:28:02.996 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:28:02.996 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:28:02.996 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:28:04.672 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你下次去复试, 时间戳: 2025-07-01 17:28:10.788000
2025-07-01 17:28:04.956 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:28:04.958 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:28:04.963 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，你下次去复试; 响应时间: 0; JD机器人回复: 
2025-07-01 17:28:04.963 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:28:04.963 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:28:04.963 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:28:09.054 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 去付15块钱，你就直接只付5块钱这样, 时间戳: 2025-07-01 17:28:15.168000
2025-07-01 17:28:09.314 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:28:09.315 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:28:09.324 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 去付15块钱，你就直接只付5块钱这样; 响应时间: 0; JD机器人回复: 
2025-07-01 17:28:09.324 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:28:09.324 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:28:09.324 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:28:14.830 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 它是这么简单，它可以对，它可以叠在一起用, 时间戳: 2025-07-01 17:28:20.945000
2025-07-01 17:28:15.150 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:28:15.151 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:28:15.161 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 它是这么简单，它可以对，它可以叠在一起用; 响应时间: 0; JD机器人回复: 
2025-07-01 17:28:15.161 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:28:15.161 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:28:15.161 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:28:16.414 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，不管你买, 时间戳: 2025-07-01 17:28:22.529000
2025-07-01 17:28:16.716 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:28:16.718 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:28:16.724 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，不管你买; 响应时间: 0; JD机器人回复: 
2025-07-01 17:28:16.724 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:28:16.724 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:28:16.724 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:28:17.496 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 什么都可以, 时间戳: 2025-07-01 17:28:23.612000
2025-07-01 17:28:17.768 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:28:17.769 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:28:17.774 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 什么都可以; 响应时间: 0; JD机器人回复: 
2025-07-01 17:28:17.774 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:28:17.774 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:28:17.774 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:28:26.357 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-07-01 17:28:32.474000
2025-07-01 17:28:26.609 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:28:26.610 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:28:26.611 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 17:28:26.611 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:28:26.611 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:28:26.611 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:28:28.071 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，农商银行, 时间戳: 2025-07-01 17:28:34.187000
2025-07-01 17:28:28.486 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:28:28.487 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:28:28.488 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，农商银行; 响应时间: 0; JD机器人回复: 
2025-07-01 17:28:28.488 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:28:28.488 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:28:28.488 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:28:30.219 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，农商银行这个, 时间戳: 2025-07-01 17:28:36.334000
2025-07-01 17:28:30.475 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:28:30.477 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:28:30.486 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，农商银行这个; 响应时间: 0; JD机器人回复: 
2025-07-01 17:28:30.486 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:28:30.486 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:28:30.486 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:28:33.299 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:28:35.854 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:28:44.219 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:28:44.220 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:30:18.020 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 17:30:18.020 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 17:30:18.086 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 17:30:18.086 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 17:30:19.110 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 17:30:19.173 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 17:30:38.320 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 17:30:38.320 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 17:30:38.383 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 17:30:38.383 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 17:30:39.410 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 17:30:39.473 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 17:30:43.260 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 17:30:43.260 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 17:30:43.323 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 17:30:43.323 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 17:30:44.352 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 17:30:44.413 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 17:30:47.840 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 17:30:47.840 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 17:30:47.905 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 17:30:47.905 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 17:30:49.202 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 17:30:49.262 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 17:30:54.669 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 17:30:54.669 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 17:30:54.733 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 17:30:54.733 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 17:30:56.026 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 17:30:56.090 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 17:31:33.776 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 17:31:33.776 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 17:31:33.841 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 17:31:33.841 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 17:31:34.872 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 17:31:34.934 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 17:32:07.278 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:32:07.278 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:34:43.334 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 限了嘛，明天你今你今天过了12点或者明天早上再开一次，那就又可以继续用了, 时间戳: 2025-07-01 17:34:49.418000
2025-07-01 17:34:43.608 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:34:43.608 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:34:43.619 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 限了嘛，明天你今你今天过了12点或者明天早上再开一次，那就又可以继续用了; 响应时间: 0; JD机器人回复: 
2025-07-01 17:34:43.619 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:34:43.619 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:34:43.619 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:34:46.526 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 那个改用, 时间戳: 2025-07-01 17:34:52.640000
2025-07-01 17:34:46.837 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:34:46.838 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:34:46.846 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 那个改用; 响应时间: 0; JD机器人回复: 
2025-07-01 17:34:46.846 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:34:46.846 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:34:46.847 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:34:49.284 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:34:54.195 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你没领到，没领到那个，没到微信钱包里面，它就可以分次用, 时间戳: 2025-07-01 17:35:00.308000
2025-07-01 17:34:54.494 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:34:54.495 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:34:54.499 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，你没领到，没领到那个，没到微信钱包里面，它就可以分次用; 响应时间: 0; JD机器人回复: 
2025-07-01 17:34:54.499 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:34:54.499 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:34:54.499 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:34:54.728 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:35:11.278 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你要不给他们看一下，他说今天要有一个, 时间戳: 2025-07-01 17:35:17.390000
2025-07-01 17:35:11.575 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:35:11.575 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:35:11.576 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，你要不给他们看一下，他说今天要有一个; 响应时间: 0; JD机器人回复: 
2025-07-01 17:35:11.577 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:35:11.577 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:35:11.577 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:35:11.886 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:35:12.974 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:35:12.974 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:35:53.411 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:35:53.411 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:35:54.370 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:35:54.374 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:39:30.624 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:39:30.624 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:39:35.213 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我最后一天, 时间戳: 2025-07-01 17:39:41.323000
2025-07-01 17:39:35.659 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:39:35.662 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:39:35.665 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 我最后一天; 响应时间: 0; JD机器人回复: 
2025-07-01 17:39:35.665 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:39:35.665 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:39:35.665 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:41:40.445 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:43:39.128 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:43:39.128 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:44:13.456 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:44:13.457 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:44:14.037 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:44:14.037 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:45:21.370 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:45:21.370 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:49:18.384 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-07-01 17:49:24.491000
2025-07-01 17:49:18.390 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:49:18.391 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:49:18.407 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 17:49:18.409 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:49:18.409 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:49:18.409 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:53:05.789 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:53:07.356 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 17:53:11.853 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 17:53:11.854 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 17:53:11.908 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 17:53:11.908 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 17:53:13.098 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 17:53:13.164 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 17:53:40.633 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 17:53:40.633 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 17:53:40.633 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 17:53:40.749 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 17:53:45.921 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，找找找你和扬州那边能用的，我们和扬州就通, 时间戳: 2025-07-01 17:53:52.021000
2025-07-01 17:53:46.207 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:53:46.209 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:53:46.220 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，找找找你和扬州那边能用的，我们和扬州就通; 响应时间: 0; JD机器人回复: 
2025-07-01 17:53:46.220 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:53:46.220 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:53:46.220 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:53:48.164 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我发消息给他就完事了, 时间戳: 2025-07-01 17:53:54.271000
2025-07-01 17:53:48.437 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:53:48.440 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:53:48.446 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，我发消息给他就完事了; 响应时间: 0; JD机器人回复: 
2025-07-01 17:53:48.446 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:53:48.446 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:53:48.446 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:53:49.281 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:57:06.413 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，他这个，你没和他交互还, 时间戳: 2025-07-01 17:57:12.515000
2025-07-01 17:57:06.702 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:57:06.704 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:57:06.714 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，他这个，你没和他交互还; 响应时间: 0; JD机器人回复: 
2025-07-01 17:57:06.714 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:57:06.714 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:57:06.714 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:57:09.683 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 17:57:26.264 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: thank you., 时间戳: 2025-07-01 17:57:32.364000
2025-07-01 17:57:27.103 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 17:57:33.204000
2025-07-01 17:57:27.103 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 840
2025-07-01 17:57:27.134 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:57:27.135 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:57:27.137 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:57:27.430 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:57:27.434 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:57:27.731 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:57:27.731 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:57:27.763 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:57:27.764 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:57:27.789 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:57:27.798 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: thank you.; 响应时间: 0; JD机器人回复: You're welcome! Feel free to ask if you need more help. Have a great day!
2025-07-01 17:57:27.798 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:57:27.798 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:57:28.307 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:57:30.440 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:57:31.652 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 17:57:31.654 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:59:36.237 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 全屏吗？, 时间戳: 2025-07-01 17:59:42.335000
2025-07-01 17:59:36.489 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 17:59:36.491 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:59:36.498 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 全屏吗？; 响应时间: 0; JD机器人回复: 
2025-07-01 17:59:36.498 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:59:36.498 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:59:36.498 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:59:43.067 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: you're welcome., 时间戳: 2025-07-01 17:59:49.170000
2025-07-01 17:59:43.773 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 17:59:43.774 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 17:59:43.774 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 17:59:43.780 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:59:43.786 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: you're welcome.; 响应时间: 0; JD机器人回复: 
2025-07-01 17:59:43.786 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:59:43.787 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:59:43.787 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:59:43.875 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 17:59:49.428 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本:  JDA game应该有有那种设置，就是让他自己全屏，适应屏幕全屏, 时间戳: 2025-07-01 17:59:55.531000
2025-07-01 17:59:49.998 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 17:59:49.998 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:59:50.004 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 17:59:50.005 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 17:59:50.006 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr:  JDA game应该有有那种设置，就是让他自己全屏，适应屏幕全屏; 响应时间: 0; JD机器人回复: 
2025-07-01 17:59:50.006 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:59:50.006 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:59:50.006 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 17:59:50.106 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 17:59:53.566 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 不用写，你现在应该是写的那个固定分辨率吧, 时间戳: 2025-07-01 17:59:59.669000
2025-07-01 17:59:54.506 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 18:00:00.609000
2025-07-01 17:59:54.506 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 940
2025-07-01 17:59:54.550 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:59:54.553 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:59:54.564 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:59:54.881 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:59:54.886 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:59:55.153 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:59:55.165 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:59:55.502 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:59:55.510 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:59:55.813 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:59:55.817 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:59:56.106 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:59:56.112 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:59:56.421 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 17:59:56.421 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 17:59:56.433 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 17:59:56.444 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 不用写，你现在应该是写的那个固定分辨率吧; 响应时间: 0; JD机器人回复: JDA game 确实有全屏设置，可以在代码中启用自动全屏，让游戏适应屏幕分辨率。具体实现方式取决于你使用的游戏引擎或框架，通常在初始化阶段设置全屏模式即可。如果你有具体代码问题，可以分享出来，我帮你看看！
2025-07-01 17:59:56.444 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 17:59:56.444 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 17:59:56.650 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 17:59:59.124 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 18:00:00.687 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-07-01 18:00:06.791000
2025-07-01 18:00:00.999 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:00:00.999 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:00:01.257 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 18:00:05.406 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 18:00:08.741 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 18:00:10.811 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 18:00:13.508 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 18:00:13.508 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:00:13.519 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 18:00:13.519 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:00:13.519 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:00:13.519 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:01:05.951 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:02:41.957 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 18:02:41.958 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:02:43.394 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 18:02:43.396 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:12:14.930 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 18:12:14.930 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 18:12:14.976 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 18:12:14.976 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 18:12:16.257 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 18:12:16.321 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 18:12:45.385 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 18:12:45.385 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 18:12:45.435 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 18:12:45.435 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 18:12:47.083 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 18:12:47.149 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 18:13:07.268 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 18:13:07.269 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 18:13:07.319 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 18:13:07.319 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 18:13:08.616 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 18:13:08.680 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 18:13:36.784 - chat_with_robot - voice.py - detect_callback - line 406 - INFO - [wakeup] 检测到唤醒词
2025-07-01 18:13:36.784 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-01 18:13:36.832 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 18:13:36.833 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 18:13:38.065 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-01 18:13:38.130 - chat_with_robot - voice.py - run - line 462 - INFO - [run] 持续监听状态...
2025-07-01 18:20:15.342 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 18:20:15.343 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:20:21.806 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 18:20:21.807 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:20:22.758 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 18:20:22.759 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:25:15.592 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 18:25:15.594 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:25:29.709 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 18:25:29.710 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:25:33.340 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 18:25:33.340 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:25:39.631 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 看这个呀，哎呦, 时间戳: 2025-07-01 18:25:45.722000
2025-07-01 18:25:39.924 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:25:39.925 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:25:39.925 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 看这个呀，哎呦; 响应时间: 0; JD机器人回复: 
2025-07-01 18:25:39.925 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:25:39.925 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:25:39.925 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:25:40.282 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:25:42.345 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 18:25:42.346 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:25:48.255 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 哎呦，你还看这个呀？啊，哎呦，这个还在看哟！哎呦！, 时间戳: 2025-07-01 18:25:54.341000
2025-07-01 18:25:48.522 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:25:48.523 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:25:48.527 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 哎呦，你还看这个呀？啊，哎呦，这个还在看哟！哎呦！; 响应时间: 0; JD机器人回复: 
2025-07-01 18:25:48.527 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:25:48.527 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:25:48.527 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:25:50.926 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 可以啊，看完了吗？这个, 时间戳: 2025-07-01 18:25:57.011000
2025-07-01 18:25:51.208 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:25:51.208 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:25:51.219 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 可以啊，看完了吗？这个; 响应时间: 0; JD机器人回复: 
2025-07-01 18:25:51.219 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:25:51.219 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:25:51.219 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:25:52.947 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 要出第2季吗？, 时间戳: 2025-07-01 18:25:59.035000
2025-07-01 18:25:53.244 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:25:53.245 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:25:53.248 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 要出第2季吗？; 响应时间: 0; JD机器人回复: 
2025-07-01 18:25:53.248 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:25:53.248 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:25:53.249 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:25:55.225 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:25:57.389 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 第2季还没出吗？, 时间戳: 2025-07-01 18:26:03.477000
2025-07-01 18:25:58.710 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 18:26:04.801000
2025-07-01 18:25:58.710 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1324
2025-07-01 18:25:58.816 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 18:25:58.821 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 18:25:58.822 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 18:25:59.104 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 18:25:59.105 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:25:59.105 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 18:25:59.116 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 第2季还没出吗？; 响应时间: 0; JD机器人回复: 第2季还没出，但可以关注官方消息或社交媒体，获取最新动态！
2025-07-01 18:25:59.116 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:25:59.116 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:26:03.296 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 18:26:03.844 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 18:26:03.847 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:26:04.813 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 18:26:04.813 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:26:05.763 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 它就是漫画型的, 时间戳: 2025-07-01 18:26:11.855000
2025-07-01 18:26:06.073 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:26:06.074 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:26:06.076 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 它就是漫画型的; 响应时间: 0; JD机器人回复: 
2025-07-01 18:26:06.076 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:26:06.076 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:26:06.076 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:26:07.304 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:26:15.589 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 山水画，他就是不对，山秀是那个，你居然不看我操, 时间戳: 2025-07-01 18:26:21.680000
2025-07-01 18:26:15.884 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:26:15.886 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:26:15.889 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 山水画，他就是不对，山秀是那个，你居然不看我操; 响应时间: 0; JD机器人回复: 
2025-07-01 18:26:15.889 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:26:15.889 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:26:15.889 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:26:17.040 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:26:19.324 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:26:32.013 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 18:26:32.013 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:26:43.174 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 第2季吗？我第一季看了。第2季都出来了，我第一季在第2季比第一季还精彩，我操, 时间戳: 2025-07-01 18:26:49.266000
2025-07-01 18:26:43.480 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:26:43.480 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:26:43.485 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 第2季吗？我第一季看了。第2季都出来了，我第一季在第2季比第一季还精彩，我操; 响应时间: 0; JD机器人回复: 
2025-07-01 18:26:43.485 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:26:43.485 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:26:43.485 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:26:43.520 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:26:44.873 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 第2季, 时间戳: 2025-07-01 18:26:50.963000
2025-07-01 18:26:46.272 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 18:26:52.355000
2025-07-01 18:26:46.272 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1392
2025-07-01 18:26:46.360 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 18:26:46.363 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 18:26:46.370 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 18:26:46.520 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 18:26:46.521 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:26:46.525 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 第2季; 响应时间: 0; JD机器人回复: 第2季还没出，不过可以关注官方消息或社交媒体，
2025-07-01 18:26:46.525 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:26:46.525 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:26:46.525 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:26:46.527 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 18:26:46.528 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 18:26:46.572 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 18:26:46.630 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 18:26:50.523 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我第一季也啊，我之前在那个那个那个那个腾讯视频上看到, 时间戳: 2025-07-01 18:26:56.614000
2025-07-01 18:26:50.798 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:26:50.803 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:26:50.812 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 我第一季也啊，我之前在那个那个那个那个腾讯视频上看到; 响应时间: 0; JD机器人回复: 
2025-07-01 18:26:50.812 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:26:50.812 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:26:50.812 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:26:56.510 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:27:00.372 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 老板，老板, 时间戳: 2025-07-01 18:27:06.457000
2025-07-01 18:27:00.637 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:27:00.639 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:27:00.641 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 老板，老板; 响应时间: 0; JD机器人回复: 
2025-07-01 18:27:00.641 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:27:00.641 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:27:00.641 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:27:01.313 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 不喜欢看, 时间戳: 2025-07-01 18:27:07.405000
2025-07-01 18:27:01.587 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:27:01.588 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:27:01.591 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 不喜欢看; 响应时间: 0; JD机器人回复: 
2025-07-01 18:27:01.591 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:27:01.591 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:27:01.591 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:27:05.872 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，是的, 时间戳: 2025-07-01 18:27:11.960000
2025-07-01 18:27:07.038 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 18:27:13.123000
2025-07-01 18:27:07.038 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1163
2025-07-01 18:27:07.092 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 18:27:07.093 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 18:27:07.097 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 18:27:07.418 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 18:27:07.418 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:27:07.422 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 18:27:07.433 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，是的; 响应时间: 0; JD机器人回复: 好的，有什么问题尽管问，我随时为你解答！😊
2025-07-01 18:27:07.433 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:27:07.433 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:27:09.552 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 18:27:11.075 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 18:27:11.075 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:27:13.908 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，没什么问题, 时间戳: 2025-07-01 18:27:19.986000
2025-07-01 18:27:14.176 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:27:14.181 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:27:14.183 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，没什么问题; 响应时间: 0; JD机器人回复: 
2025-07-01 18:27:14.183 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:27:14.183 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:27:14.183 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:27:16.016 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，还在玩我，他妈的, 时间戳: 2025-07-01 18:27:22.054000
2025-07-01 18:27:16.262 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:27:16.264 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:27:16.270 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，还在玩我，他妈的; 响应时间: 0; JD机器人回复: 
2025-07-01 18:27:16.270 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:27:16.270 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:27:16.270 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:27:24.341 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 后面放的痒痒的老子不想看他了，就看到他觉醒就没看了。我也没看，我看他上那个天龙岛我就没看, 时间戳: 2025-07-01 18:27:30.430000
2025-07-01 18:27:24.607 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:27:24.608 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:27:24.617 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 后面放的痒痒的老子不想看他了，就看到他觉醒就没看了。我也没看，我看他上那个天龙岛我就没看; 响应时间: 0; JD机器人回复: 
2025-07-01 18:27:24.617 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:27:24.617 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:27:24.617 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:27:28.649 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 这个他妈的还没出来，我一直想看, 时间戳: 2025-07-01 18:27:34.740000
2025-07-01 18:27:29.935 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 18:27:36.025000
2025-07-01 18:27:29.935 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1285
2025-07-01 18:27:29.958 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 18:27:29.963 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 18:27:29.963 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 18:27:29.965 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:27:29.971 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 这个他妈的还没出来，我一直想看; 响应时间: 0; JD机器人回复: 
2025-07-01 18:27:29.971 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:27:29.971 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:27:29.971 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:27:30.065 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 18:27:33.210 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，有可能，国呃，有可能那个地方有, 时间戳: 2025-07-01 18:27:39.301000
2025-07-01 18:27:33.523 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:27:33.524 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:27:33.532 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，有可能，国呃，有可能那个地方有; 响应时间: 0; JD机器人回复: 
2025-07-01 18:27:33.532 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:27:33.532 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:27:33.532 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:27:34.283 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:27:43.060 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 看一下海外的站海外哪个在啊海外哪个在搜啊谷歌上搜他, 时间戳: 2025-07-01 18:27:49.151000
2025-07-01 18:27:43.332 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:27:43.333 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:27:43.339 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 看一下海外的站海外哪个在啊海外哪个在搜啊谷歌上搜他; 响应时间: 0; JD机器人回复: 
2025-07-01 18:27:43.339 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:27:43.339 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:27:43.340 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:27:43.594 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:27:46.074 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 就刚刚今天早上你这儿收的那个吧, 时间戳: 2025-07-01 18:27:52.164000
2025-07-01 18:27:46.350 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:27:46.351 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:27:46.353 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 就刚刚今天早上你这儿收的那个吧; 响应时间: 0; JD机器人回复: 
2025-07-01 18:27:46.353 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:27:46.355 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:27:46.355 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:27:46.548 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-07-01 18:27:52.639000
2025-07-01 18:27:46.822 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:27:46.825 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:27:46.831 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 
2025-07-01 18:27:46.831 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:27:46.831 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:27:46.831 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:27:48.628 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我要收太阳怪物吧, 时间戳: 2025-07-01 18:27:54.718000
2025-07-01 18:27:48.897 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:27:48.898 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:27:48.901 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 我要收太阳怪物吧; 响应时间: 0; JD机器人回复: 
2025-07-01 18:27:48.901 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:27:48.901 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:27:48.901 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:28:05.379 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:28:06.458 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 18:28:06.459 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:28:08.010 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 18:28:08.012 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:28:16.412 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-01 18:28:16.414 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:28:21.726 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，台湾上映, 时间戳: 2025-07-01 18:28:27.813000
2025-07-01 18:28:22.976 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 18:28:29.062000
2025-07-01 18:28:22.976 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1249
2025-07-01 18:28:23.052 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 18:28:23.052 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:28:23.053 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 18:28:23.063 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 18:28:23.065 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，台湾上映; 响应时间: 0; JD机器人回复: 十分抱歉，这个问题我暂时还不了解。
2025-07-01 18:28:23.065 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:28:23.065 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:28:23.065 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:28:23.265 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:28:26.498 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-01 18:28:26.515 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 只是预告片, 时间戳: 2025-07-01 18:28:32.606000
2025-07-01 18:28:27.325 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 18:28:27.326 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:28:27.328 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 只是预告片; 响应时间: 0; JD机器人回复: 
2025-07-01 18:28:27.330 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:28:27.330 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:28:27.330 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:28:27.335 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 18:28:27.335 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 18:28:27.437 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 18:28:29.049 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 它应该还没有上映吧, 时间戳: 2025-07-01 18:28:35.136000
2025-07-01 18:28:29.350 - chat_with_robot - websocket_client_thread.py - _on_message - line 164 - WARNING - 响应code不是200: code=50010
2025-07-01 18:28:29.350 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:28:29.361 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: 它应该还没有上映吧; 响应时间: 0; JD机器人回复: 
2025-07-01 18:28:29.361 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:28:29.361 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:28:29.361 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:28:32.245 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-01 18:28:40.505 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，八号不是九号，你是哪号？应该已经是七号、八号、九号，反正是789没有错啊, 时间戳: 2025-07-01 18:28:46.591000
2025-07-01 18:28:41.726 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-01 18:28:41.730 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:28:41.735 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-01 18:28:41.735 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-01 18:28:41.741 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，八号不是九号，你是哪号？应该已经是七号、八号、九号，反正是789没有错啊; 响应时间: 0; JD机器人回复: 
2025-07-01 18:28:41.741 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:28:41.741 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:28:41.741 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-01 18:28:41.840 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-01 18:28:42.472 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，没那么快, 时间戳: 2025-07-01 18:28:48.561000
2025-07-01 18:28:44.031 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-01 18:28:50.119000
2025-07-01 18:28:44.031 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1558
2025-07-01 18:28:44.111 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 18:28:44.111 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 18:28:44.111 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 18:28:44.411 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 18:28:44.412 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 18:28:44.694 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 18:28:44.696 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 18:28:45.017 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-01 18:28:45.017 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-01 18:28:45.020 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-01 18:28:45.031 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 1b4911bb-5657-11f0-8238-dc4546c07870; requestId: 8a41debe-e206-43ed-a6e9-b9315858ee87_joyinside; asr: ，没那么快; 响应时间: 0; JD机器人回复: 看来你在讨论某个特定的日期或事件，但具体细节我还不太清楚。如果你能提供更多信息，我会尽力帮你解答。
2025-07-01 18:28:45.031 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-01 18:28:45.031 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-01 18:28:47.352 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-01 18:28:49.820 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
