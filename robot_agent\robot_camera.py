"""
@Author: lid<PERSON><PERSON><EMAIL>
@Create Date: 2025.03.28
@Description: 拉取机器人广角相机视频流

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
"""
import cv2
import sys
import os
import time
import argparse
import signal

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from util.logger import logger

def signal_handler(sig, frame):
    """处理Ctrl+C信号"""
    logger.info("程序正在退出...")
    cv2.destroyAllWindows()
    sys.exit(0)

class RobotCameraStream:
    def __init__(self, rtsp_url):
        self.rtsp_url = rtsp_url
        self.cap = None
        self.is_running = False
        logger.info(f"初始化机器人相机流，RTSP地址: {self.rtsp_url}")
    
    def connect(self):
        """连接RTSP视频流"""
        try:
            logger.info(f"正在连接到RTSP流: {self.rtsp_url}")
            self.cap = cv2.VideoCapture(self.rtsp_url)
            if not self.cap.isOpened():
                logger.error("无法打开RTSP流")
                return False
            logger.info("成功连接到RTSP流")
            return True
        except Exception as e:
            logger.error(f"连接RTSP流时出错: {e}")
            return False
    
    def start(self, display=True):
        """启动视频流处理"""
        if not self.connect():
            return
        
        self.is_running = True
        logger.info("开始处理视频流")
        
        try:
            while self.is_running:
                ret, frame = self.cap.read()
                if not ret:
                    logger.warning("无法读取视频帧，尝试重新连接...")
                    if not self.reconnect():
                        break
                    continue
                
                # 根据参数决定是否显示
                if display:
                    cv2.imshow('机器人广角相机', frame)
                    key = cv2.waitKey(1) & 0xFF
                    if key == 27:
                        break
        finally:
            self.stop()
    
    def reconnect(self):
        """重新连接视频流"""
        logger.info("尝试重新连接视频流...")
        if self.cap is not None:
            self.cap.release()
        time.sleep(1)  # 等待1秒再重连
        return self.connect()
    
    def stop(self):
        """停止视频流处理"""
        self.is_running = False
        if self.cap is not None:
            self.cap.release()
        cv2.destroyAllWindows()
        logger.info("视频流已停止")

    def save_video(self, output_file, duration=60):
        if not self.connect():
            return
        
        width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = 30  # 假设30fps
        
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_file, fourcc, fps, (width, height))
        
        start_time = time.time()
        self.is_running = True
        
        try:
            while self.is_running and time.time() - start_time < duration:
                ret, frame = self.cap.read()
                if not ret:
                    if not self.reconnect():
                        break
                    continue
                out.write(frame)
        finally:
            out.release()
            self.stop()


if __name__ == "__main__":
    # 注册信号处理程序，使得可以用Ctrl+C优雅地退出
    signal.signal(signal.SIGINT, signal_handler)
    
    parser = argparse.ArgumentParser(description="机器人相机视频流查看器")
    parser.add_argument('--rtsp_url', type=str, default="rtsp://*************:8554/test",
                        help="RTSP视频流地址，默认为机器人广角相机")
    parser.add_argument('--headless', action='store_true', 
                        help="在无图形界面环境下运行，不显示视频")
    parser.add_argument('--save', type=str, 
                        help="保存视频到指定文件而非显示")
    args = parser.parse_args()
    
    logger.info("\n[启动机器人相机视频流查看器]\n")
    
    camera_stream = RobotCameraStream(args.rtsp_url)
    
    if args.save:
        # 保存视频模式，不启动显示
        camera_stream.save_video(args.save)
    else:
        # 正常显示模式
        if args.headless:
            camera_stream.start(display=False)
        else:
            camera_stream.start()