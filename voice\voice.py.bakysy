import os
import sys
import json
import io
import re
import copy
import urllib
import time
from pathlib import Path
import argparse
import threading
import pyaudio
#import requests
import wave
import numpy as np
import random
import functools
#from pypinyin import pinyin, Style
#import subprocess

#from asr.robot import ASR
sys.path.append((Path(__file__).resolve().parent/"src").__str__())
sys.path.append(Path(__file__).resolve().parent.__str__())
from datetime import datetime
from .constants import no_alsa_error
import collections
import queue
#import webrtcvad
from collections import deque
from util.logger import logger
import base64


import simpleaudio as sa
from scipy import signal
import samplerate


class RingBuffer(object):
    """Ring buffer to hold audio from PortAudio"""

    def __init__(self, size=4096):
        self._buf = collections.deque(maxlen=size)

    def extend(self, data):
        """Adds data to the end of buffer"""
        self._buf.extend(data)

    def get(self):
        """Retrieves data from the beginning of buffer and clears it"""
        tmp = bytes(bytearray(self._buf))
        self._buf.clear()
        return tmp

    
def play_audio_wakeup():
    """
    播放音频数据
    
    参数:
        audio_data: 解码后的音频二进制数据
    """
    try:
        
        # import simpleaudio as sa
        # 先将音频数据写入临时文件
        # temp_file = "temp_output.wav"
        # with open(temp_file, 'wb') as f:
        #     f.write(audio_data)
        
        # 使用 aplay 播放音频
        try:

            tempfile_zaine = "asserts/zaine.wav"
            tempfile_shenmeshi = "asserts/shenmeshi.wav"

            if random.random() < 0.5:
                tempfile = tempfile_zaine
            else:
                tempfile = tempfile_shenmeshi

            # wave_obj = sa.WaveObject.from_wave_file('/home/<USER>/Possessed_AI/asserts/dog_bark.wav')
            # play_obj = wave_obj.play()
            # play_obj.wait_done()  # 等待播放完成

            """ffplay"""
            # subprocess.run(['ffplay', '-nodisp', '-autoexit', '-i', tempfile], stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL)

            """simpleaudio"""
            wave_obj = sa.WaveObject.from_wave_file(tempfile)
            play_obj = wave_obj.play()
            play_obj.wait_done()

            #subprocess.run(['aplay', '/home/<USER>/Possessed_AI/asserts/dog_bark.wav'],stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL)
            #print("使用ffplay播放成功")
            return
        except FileNotFoundError:
            # print("ffplay不可用，尝试其他播放器")
            logger.error(f"唤醒音频文件未找到: {tempfile}")
        
    except Exception as e:
        print(f"播放音频时出错: {str(e)}")


import subprocess
def play_tts_voice(file_path: str):
    """
    调用系统play命令播放音频文件，直到播放结束才返回。
    :param file_path: 音频文件路径
    """
    try:
        subprocess.run(['play', file_path], check=True)
    except Exception as e:
        print(f"播放音频时出错: {e}")

class MultiChat(object):
    def __init__(self, det_log=1, chat_log=1, det_th=2000, chat_th=2000, asr_engine="jd-ws-asr", kws_mode="api", use_action="dont", echo_cancel=True, use_save_audio=False):
        super(MultiChat, self).__init__()

        # 添加保存音频的选项
        self.use_save_audio = use_save_audio

        # ---------------------------- #
        self.CHUNK = 1920 # ~128ms # 190 ~音频读取的缓冲区大小  2048 6400 2048
        self.FORMAT = pyaudio.paInt16  # 采样位数
        self.CHANNELS = 1  # 声道数
        self.RATE = 48000 #16000  # 采样率，每秒采集的样例数
        self._running = True
        self.lock = threading.Lock()  # 创建独立的锁

        # resample
        self.overlap_size = int(self.CHUNK * 0.5)
        self.buffer = np.zeros(self.overlap_size, dtype=np.float32)
        self.ratio = 16000 / float(self.RATE)
        
        # KWS模式设置
        self.kws_mode = kws_mode
        self.kws_detector = None

        self.use_action = use_action
        self.echo_cancel = echo_cancel

        # 创建两个缓冲区
        # self.ring_buffer = RingBuffer(
        #     self.CHANNELS * self.RATE * 5
        # )

        self.ring_buffer = queue.Queue(maxsize=10000)

        self.detector_ring_buffer = RingBuffer(
            self.CHANNELS * self.RATE * 5
        )

        self.mode = 'wait'
        self.log = chat_log

            
        self.silent_th = chat_th
        self.silent_status = True
        self.silent_temp = sys.maxsize
        self.silent_count = 0
        self.silent_count_threshold = 8

        self.save_record_file = "asserts/save_listen_file.wav"
        self.messages = []

        
        ###
        ###
        # 创建共享的音频设备
        self.audio = None
        self.stream = None

        self.robot_up_state = 0
        self.commander = None


        self.wakeup_flag = False



        self.websocket_send_index = 0

        self.record_flag = True


        # self.resampler = samplerate.Resampler(converter_type='sinc_best')

    def save_audio(self, audio, frames, save_file):
        # 保存为wav文件
        wf = wave.open(save_file, 'wb')
        wf.setnchannels(self.CHANNELS)  # 设置声道数
        wf.setsampwidth(audio.get_sample_size(self.FORMAT))  # 设置采样宽度
        wf.setframerate(self.RATE)  # 设置采样率
        wf.writeframes(b''.join(frames))  # 写入音频数据
        wf.close()

    def start_streaming(self):
        #self.ring_buffer.get()
        logger.info("[start recording]...")
        self.stream.start_stream()

    def end_streaming(self):
        logger.info("[end recording]...")
        self.stream.stop_stream()

    def stream_downsample(self, chunk, ratio=3):
        new_length = int(len(chunk) * ratio)
        resampled = np.interp(
            np.linspace(0, len(chunk) - 1, new_length),
            np.arange(len(chunk)),
            chunk
        )
        return resampled

    def audio_callback(self, in_data, frame_count, time_info, status):
            # 向两个缓冲区发送数据
            ##self.ring_buffer.extend(in_data)
            #self.detector_ring_buffer.extend(in_data)
        
        if in_data is None or len(in_data) == 0:
            return chr(0), pyaudio.paContinue

        audio_data = np.frombuffer(in_data, dtype=np.int16)
        audio_data = audio_data.astype(np.float32) / 32768.0
        # audio_data = audio_data.reshape(-1, channels)
        extended_chunk = np.concatenate([self.buffer, audio_data])
        resampled_data = self.stream_downsample(extended_chunk, self.ratio)
        overlap_resampled = int(self.overlap_size * self.ratio)
        self.buffer = audio_data[-self.overlap_size:]
        resampled_data = resampled_data[overlap_resampled:]
        resampled_data = (resampled_data * 32768.0).astype(np.int16).tobytes()

        if self.echo_cancel:
            self.ring_buffer.put(resampled_data)
        else:
            if self.websocket_manager.code != 200:
                self.record_flag  = True
            if (not self.audio_player.pygame.mixer.music.get_busy()) and self.audio_player.audio_queue.empty() and self.record_flag:
                self.ring_buffer.put(resampled_data)
        
        # 将数据也发送给KWS检测器
        self.kws_detector.accept_audio(in_data)
        
        play_data = chr(0) * len(in_data)
        return play_data, pyaudio.paContinue

    def init_wakeup(self):
        # 首先初始化音频设备
        #
        self.audio = pyaudio.PyAudio()
        if self.kws_mode == "local_streaming":
            # 本地流式KWS模式
            try:
                from .kws_wrapper import KWSStreamDetector
                
                # 创建流式KWS检测器
                self.kws_detector = KWSStreamDetector()
                self.kws_detector.set_detection_callback(self.detect_callback)
                
                # 重新设置音频流回调
                self._setup_audio_stream(self.audio_callback)
                
                # 启动KWS检测器
                self.kws_detector.start()
                logger.info("本地流式KWS检测器启动成功")
            
            except Exception as e:
                logger.error(f"初始化本地流式KWS失败: {e}")
    
    def _setup_audio_stream(self, audio_callback):
        """重新设置音频流回调"""
        if self.stream:
            self.stream.stop_stream()
            self.stream.close()
        
        input_device_index = self._select_input_device_auto()
        #input_device_index = 25  # 0
        
        # 创建新的音频流
        self.stream = self.audio.open(
            format=self.FORMAT,
            channels=self.CHANNELS,
            rate=self.RATE,
            input=True,
            output=False,
            frames_per_buffer=self.CHUNK,
            start=True,
            stream_callback=audio_callback,
            input_device_index=input_device_index,
        )

    def _select_input_device_auto(self) -> int:
        """持续检测直到找到目标设备"""
        #target_device_name = "4-mic Microphone: USB Audio"
        target_device_name = ["XFM-DP-V0.0.18: USB Audio", "DOV: USB Audio", "MAXHUB_BM20_Speaker", "XFM-DP-V0.0.18", "mic","CP900: USB Audio", "spdif","dsnooper","speaker","XFM-DP-V0.0.18"]
        # target_device_name = ["XFM-DP-V0.0.18", "mic", "spdif","dsnooper","speaker","XFM-DP-V0.0.18"]
        
        # 获取所有音频设备
        while True:  # 持续循环直到找到设备
            print("\n正在搜索音频设备...")
            input_devices = []
            selected_index = None
            selected_device_name = None
            
            # 遍历所有音频设备
            for i in range(self.audio.get_device_count()):
                device_info = self.audio.get_device_info_by_index(i)
                if device_info.get('maxInputChannels') > 0:  # 只显示输入设备
                    input_devices.append((i, device_info))
                    device_name = device_info.get('name')
                    print(f"{len(input_devices)-1}) {device_name}")
                    
                    # 自动匹配目标设备
                    for target in target_device_name:
                        # if target in device_name:
                        if target.lower() in device_name.lower():
                            selected_index = i
                            selected_device_name = device_name
                            break  # 找到目标设备后立即跳出循环
                    if selected_index is not None:
                        break
            
            if not input_devices:
                print("未找到任何输入设备，3秒后重试...")
                time.sleep(3)
                continue
            
            if selected_index is not None:
                print(f"\n已找到目标设备: {selected_device_name}")
                return selected_index
            else:
                play_tts_voice('/home/<USER>/network_func/test_tts/error_audio.mp3')
            
            # 如果没找到目标设备，等待后重试
            print(f"\n未找到目标设备: {target_device_name}，3秒后重试...")
            time.sleep(3)

    def stop(self):
        """停止所有线程"""
        self._running = False
        
        # 调用原有的清理代码
        if self.stream:
            self.stream.stop_stream()
            self.stream.close()
        if self.audio:
            self.audio.terminate()

        # 如果使用的是本地KWS，停止KWS检测器
        if self.kws_mode == "local" or self.kws_mode == "local_streaming" and self.kws_detector:
            self.kws_detector.stop()
            logger.info(f"已停止{self.kws_mode}检测器")

    def detect_callback(self):

        with self.lock:
            logger.info("[wakeup] 检测到唤醒词")
            

            self.end_streaming()
            #清空音频缓冲区
            self.ring_buffer.queue.clear()
            self.websocket_manager.wakeup_flag = True

            self.websocket_manager.wakeup_num +=1
            self.audio_player.interrupt()
            if self.use_action == "lite3":
                self.controller.stop()
                self.controller.turn_off_follow()

            #self.voice_angle.execute_skill_locator()
            
            play_audio_wakeup()
            # time.sleep(0.1)
            self.wakeup_flag = True
            

            if self.echo_cancel == False:
                self.record_flag = True
            

            self.start_streaming()

            #self.chat_id = str(uuid.uuid1(node=None, clock_seq=None))
            self.mode = 'hot'
            #self.init_websocket()
            self.websocket_manager.connect()

            if self.use_action == "lite3":
                self.controller.control_robot(7)

    def run(self):
        try:
            # 如果启用了音频保存，创建recordings目录
            recordings_dir = None
            control_file = None
            
            if self.use_save_audio:
                # 创建recordings目录
                recordings_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "recordings")
                os.makedirs(recordings_dir, exist_ok=True)
                
                # 控制文件路径
                control_file = os.path.join(recordings_dir, "record_control.txt")
                logger.info(f"音频将保存至: {recordings_dir}")
                logger.info(f"创建文件 {control_file} 启动录音，删除该文件停止录音")  # touch recordings/record_control.txt , rm recordings/record_control.txt

            while self._running is True:
                if self.mode == 'hot':
                    self.mode = 'detect'
                    logger.info("[run] 持续监听状态...")
                if self.mode == 'detect' or self.mode == 'record':
                    data = self.ring_buffer.get()
                    if len(data) == 0:
                        time.sleep(0.03)
                        continue
                    
                    # 通过检查控制文件是否存在来决定是否保存音频
                    recording_enabled = False
                    if self.use_save_audio and recordings_dir and control_file:
                        recording_enabled = os.path.exists(control_file)
                    
                    if self.use_save_audio and recording_enabled:
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                        audio_filename = f"recording_{timestamp}.wav"
                        audio_path = os.path.join(recordings_dir, audio_filename)
                        
                        # 保存为WAV文件
                        with wave.open(audio_path, 'wb') as wf:
                            wf.setnchannels(self.CHANNELS)
                            wf.setsampwidth(self.audio.get_sample_size(self.FORMAT))
                            wf.setframerate(16000)  # self.RATE
                            wf.writeframes(data)

                    self.websocket_send_index += 1
                    base64_data = base64.b64encode(data).decode('utf-8')
                    self.send_message(base64_data,self.websocket_send_index,0)
                    #self.mode = 'detect'
                        
                else:
                    #print(self.model)
                    self.ring_buffer.queue.clear()
                    time.sleep(0.01)
        except KeyboardInterrupt:
            self.stop()
