2025-07-10 13:04:06.704 - chat_with_robot - chat_with_robot.py - <module> - line 632 - INFO - use_action: dont
2025-07-10 13:04:06.704 - chat_with_robot - chat_with_robot.py - <module> - line 633 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-10 13:04:06.739 - chat_with_robot - chat_with_robot.py - init_websocket - line 311 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1752123846739&accessNonce=70f65e8a-f89b-4057-a2e1-22916dfb6584&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=4e60d94e-5d4b-11f0-8762-dc4546c07870&requestId=557093cd-2a17-498a-b203-15c001ca5e7d_joyinside&accessSign=d93e09489623556bfeece81121539014, request_id: 557093cd-2a17-498a-b203-15c001ca5e7d_joyinside
2025-07-10 13:04:06.740 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-10 13:04:06.741 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-10 13:04:07.181 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-10 13:04:07.286 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-07-10 13:04:08.957 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-10 13:04:08.957 - chat_with_robot - voice.py - _setup_audio_stream - line 324 - INFO - 使用音频设备: 1
2025-07-10 13:04:08.957 - chat_with_robot - voice.py - _setup_audio_stream - line 325 - INFO - channels: 4 <class 'int'>
2025-07-10 13:04:08.957 - chat_with_robot - voice.py - _setup_audio_stream - line 326 - INFO - rate: 44100.0 <class 'float'>
2025-07-10 13:04:09.019 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-10 13:04:09.020 - chat_with_robot - voice.py - init_wakeup - line 311 - INFO - 本地流式KWS检测器启动成功
2025-07-10 13:04:10.020 - chat_with_robot - chat_with_robot.py - play_audio - line 509 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-10 13:04:10.020 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 使用 audio_action_controller 播放: ./asserts/ding.wav
2025-07-10 13:04:11.095 - chat_with_robot - chat_with_robot.py - play_audio - line 519 - INFO - audio_action_controller 播放完成: ./asserts/ding.wav
2025-07-10 13:04:11.802 - chat_with_robot - voice.py - detect_callback - line 426 - INFO - [wakeup] 检测到唤醒词
2025-07-10 13:04:11.802 - chat_with_robot - voice.py - end_streaming - line 225 - INFO - [end recording]...
2025-07-10 13:04:11.866 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-10 13:04:11.866 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-10 13:04:11.867 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-07-10 13:04:11.867 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用 audio_action_controller 播放唤醒音频: asserts/shenmeshi.wav
2025-07-10 13:04:12.926 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-07-10 13:04:12.926 - chat_with_robot - voice.py - start_streaming - line 221 - INFO - [start recording]...
2025-07-10 13:04:12.933 - chat_with_robot - voice.py - run - line 482 - INFO - [run] 持续监听状态...
2025-07-10 13:04:15.905 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想知道回锅肉怎么做, 时间戳: 2025-07-10 13:04:15.639000
2025-07-10 13:04:17.425 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-10 13:04:17.157000
2025-07-10 13:04:17.425 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1518
2025-07-10 13:04:17.526 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-10 13:04:17.533 - chat_with_robot - chat_with_robot.py - _task_worker - line 421 - INFO - 存入音频
2025-07-10 13:04:17.538 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-10 13:04:17.789 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-10 13:04:17.790 - chat_with_robot - chat_with_robot.py - _task_worker - line 421 - INFO - 存入音频
2025-07-10 13:04:18.139 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-10 13:04:18.147 - chat_with_robot - chat_with_robot.py - _task_worker - line 421 - INFO - 存入音频
2025-07-10 13:04:18.711 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-10 13:04:18.722 - chat_with_robot - chat_with_robot.py - _task_worker - line 421 - INFO - 存入音频
2025-07-10 13:04:19.043 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-10 13:04:19.046 - chat_with_robot - chat_with_robot.py - _task_worker - line 421 - INFO - 存入音频
2025-07-10 13:04:19.354 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-10 13:04:19.357 - chat_with_robot - chat_with_robot.py - _task_worker - line 421 - INFO - 存入音频
2025-07-10 13:04:19.630 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-10 13:04:19.631 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-10 13:04:19.636 - chat_with_robot - chat_with_robot.py - _task_worker - line 421 - INFO - 存入音频
2025-07-10 13:04:19.647 - chat_with_robot - chat_with_robot.py - _task_worker - line 375 - INFO - session_id: 4e60d94e-5d4b-11f0-8762-dc4546c07870; requestId: 557093cd-2a17-498a-b203-15c001ca5e7d_joyinside; asr: 我想知道回锅肉怎么做; 响应时间: 0; JD机器人回复: 回锅肉做法简单：1.五花肉冷水下锅，加姜片料酒煮20分钟，捞出切片。2.热锅少油，下肉片煸炒至微焦，加豆瓣酱、甜面酱炒香。3.放蒜苗、青椒翻炒，加糖、生抽调味即可。香辣下饭超好吃！
2025-07-10 13:04:19.647 - chat_with_robot - chat_with_robot.py - _task_worker - line 377 - INFO - 等待控制完成
2025-07-10 13:04:19.647 - chat_with_robot - chat_with_robot.py - _task_worker - line 382 - INFO - 等待音频播放完成
2025-07-10 13:04:21.471 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-10 13:04:23.888 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-10 13:04:26.674 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-10 13:04:26.811 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-10 13:04:31.345 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-10 13:04:34.074 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-10 13:04:36.490 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-10 13:04:38.407 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-10 13:04:38.407 - chat_with_robot - chat_with_robot.py - _task_worker - line 392 - INFO - 任务完成，继续
2025-07-10 13:05:51.634 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-10 13:05:51.634 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-10 13:06:52.432 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-07-10 13:06:52.432 - chat_with_robot - voice.py - stop - line 421 - INFO - 已停止local_streaming检测器
