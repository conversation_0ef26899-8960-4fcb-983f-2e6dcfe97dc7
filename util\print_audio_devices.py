import pyaudio

def print_audio_devices():
    p = pyaudio.PyAudio()
    
    # 打印所有可用的音频设备信息
    print("\n=== 输入设备 ===")
    for i in range(p.get_device_count()):
        dev = p.get_device_info_by_index(i)
        if dev['maxInputChannels'] > 0:
            print(f"设备编号: {i}")
            print(f"设备名称: {dev['name']}")
            print(f"输入通道: {dev['maxInputChannels']}")
            print(f"默认采样率: {int(dev['defaultSampleRate'])}Hz")
            print("---")
    
    print("\n=== 输出设备 ===")
    for i in range(p.get_device_count()):
        dev = p.get_device_info_by_index(i)
        if dev['maxOutputChannels'] > 0:
            print(f"设备编号: {i}")
            print(f"设备名称: {dev['name']}")
            print(f"输出通道: {dev['maxOutputChannels']}")
            print(f"默认采样率: {int(dev['defaultSampleRate'])}Hz")
            print("---")
    
    # 打印默认设备
    print("\n=== 默认设备 ===")
    print(f"默认输入设备编号: {p.get_default_input_device_info()['index']}")
    print(f"默认输出设备编号: {p.get_default_output_device_info()['index']}")
    
    p.terminate()

# 运行函数查看设备信息
print_audio_devices()


## ec -i 4-mic Microphone: USB Audio -o default