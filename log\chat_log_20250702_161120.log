2025-07-02 16:11:20.979 - chat_with_robot - chat_with_robot.py - <module> - line 612 - INFO - use_action: dont
2025-07-02 16:11:20.979 - chat_with_robot - chat_with_robot.py - <module> - line 613 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-02 16:11:20.996 - chat_with_robot - chat_with_robot.py - init_websocket - line 310 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1751443880997&accessNonce=6e0372b6-3a11-463e-a022-92a87288d95d&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=23369765-571c-11f0-a3ef-dc4546c07870&requestId=3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside&accessSign=24963bfa8c0d4cf9eb85b80ff2e97071, request_id: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside
2025-07-02 16:11:20.997 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-02 16:11:20.997 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-02 16:11:21.326 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-02 16:11:21.557 - chat_with_robot - audio_player.py - start - line 52 - INFO - 音频播放线程已启动
2025-07-02 16:11:22.944 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-02 16:11:22.945 - chat_with_robot - voice.py - _setup_audio_stream - line 305 - INFO - 使用音频设备: 1
2025-07-02 16:11:22.945 - chat_with_robot - voice.py - _setup_audio_stream - line 306 - INFO - channels: 4 <class 'int'>
2025-07-02 16:11:22.945 - chat_with_robot - voice.py - _setup_audio_stream - line 307 - INFO - rate: 44100.0 <class 'float'>
2025-07-02 16:11:23.005 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-02 16:11:23.006 - chat_with_robot - voice.py - init_wakeup - line 292 - INFO - 本地流式KWS检测器启动成功
2025-07-02 16:11:30.584 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:11:30.585 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:11:30.649 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:11:30.649 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:11:31.731 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:11:31.733 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:11:34.952 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 现在你可以回声消除吗？, 时间戳: 2025-07-02 16:11:41.163000
2025-07-02 16:11:36.156 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:11:42.364000
2025-07-02 16:11:36.156 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1201
2025-07-02 16:11:36.245 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:36.254 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:36.265 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:11:36.546 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:36.556 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:36.864 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:36.866 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:37.139 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:37.151 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:37.538 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:37.550 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:37.855 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:37.862 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:38.215 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:38.221 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:38.907 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:38.910 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:39.242 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 帮助进行回收, 时间戳: 2025-07-02 16:11:45.455000
2025-07-02 16:11:39.242 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:11:39.246 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:11:39.246 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:11:39.249 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:11:39.255 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 帮助进行回收; 响应时间: 0; JD机器人回复: 是的，我可以帮助进行回声消除。回声消除是一种信号处理技术，通常用于消除音频信号中的回声，以提高语音通信的质量。在语音通信中，回声通常是由于扬声器的声音被麦克风重新捕获而产生的。

如果你有具体的音频文件或信号需要处理，可以将其提供给我，我会尽力帮助你进行回声消除。你可以使用一些常见的音频处理库（如Python中的`pydub`或`librosa`）
2025-07-02 16:11:39.255 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:11:39.255 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:11:39.255 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:11:39.288 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:11:39.347 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:11:40.597 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:11:46.809000
2025-07-02 16:11:40.597 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1354
2025-07-02 16:11:40.653 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:40.656 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:40.666 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:11:41.002 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:41.005 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:41.373 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:11:41.377 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:11:41.377 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:11:41.378 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:11:41.381 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 帮助进行回收; 响应时间: 0; JD机器人回复: 当然可以！回声消除（Acho Cancellation, AEC）是一种用于消除音频信号中回声的技术，
2025-07-02 16:11:41.381 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:11:41.381 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:11:41.381 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:11:41.448 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:11:41.448 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:11:41.472 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:11:41.478 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:11:41.514 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:11:41.514 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:11:42.573 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:11:42.635 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:11:43.130 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好东东, 时间戳: 2025-07-02 16:11:49.341000
2025-07-02 16:11:44.368 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:11:50.580000
2025-07-02 16:11:44.369 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1239
2025-07-02 16:11:44.430 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:11:44.431 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:11:44.431 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:11:44.432 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:11:44.435 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 你好东东; 响应时间: 0; JD机器人回复: 
2025-07-02 16:11:44.435 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:11:44.435 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:11:44.435 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:11:44.533 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:11:45.066 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你会什么技能？, 时间戳: 2025-07-02 16:11:51.279000
2025-07-02 16:11:46.218 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:11:52.428000
2025-07-02 16:11:46.218 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1149
2025-07-02 16:11:46.275 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:46.282 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:46.292 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:11:46.553 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:46.559 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:46.823 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:46.826 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:47.086 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:47.086 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:47.361 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:47.364 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:47.439 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好, 时间戳: 2025-07-02 16:11:53.650000
2025-07-02 16:11:47.440 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:11:47.442 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:11:47.442 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:11:47.442 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:11:47.452 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 你好; 响应时间: 0; JD机器人回复: 你好！我具备多种技能，包括但不限于：

1. 自然语言处理：理解和生成自然语言，进行对话和回答问题。
2025-07-02 16:11:47.452 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:11:47.452 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:11:47.452 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:11:47.502 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:11:47.543 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:11:48.574 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:11:54.781000
2025-07-02 16:11:48.574 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1131
2025-07-02 16:11:48.633 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:48.639 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:48.650 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:11:48.930 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:48.930 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:49.239 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:49.240 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:49.540 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:49.544 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:49.897 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:49.900 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:50.191 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:50.202 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:50.506 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:50.511 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:50.806 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:50.817 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:51.148 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:51.158 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:51.408 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:51.409 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:11:51.415 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:51.426 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 你好; 响应时间: 0; JD机器人回复: 你好！我可以帮助你进行回声消除。回声消除是一种信号处理技术，用于消除音频信号中的回声，提高语音通信的质量。如果你有具体的音频文件或信号需要处理，可以告诉我，我会尽力帮助你。此外，我还可以帮助你进行其他语音处理任务，如降噪、语音增强等。

如果你需要进一步的帮助或有其他问题，请随时告诉我！
2025-07-02 16:11:51.426 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:11:51.426 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:11:52.072 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:11:55.097 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:11:56.113 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你会回声消除吗？, 时间戳: 2025-07-02 16:12:02.326000
2025-07-02 16:11:57.243 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:12:03.453000
2025-07-02 16:11:57.243 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1127
2025-07-02 16:11:57.252 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:11:57.252 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:11:57.252 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:11:57.307 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:57.311 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:11:57.316 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:57.323 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:11:57.353 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-07-02 16:11:57.750 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:57.760 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:58.062 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:58.072 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:58.336 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:58.346 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:58.645 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:58.648 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:58.929 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:58.940 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:59.280 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:59.290 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:59.667 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:59.677 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:11:59.983 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:11:59.992 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:00.275 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:00.275 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:12:00.281 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:00.291 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: ，你会回声消除吗？; 响应时间: 0; JD机器人回复: 是的，我可以帮助你进行回声消除。回声消除是一种信号处理技术，用于消除音频信号中的回声，提高语音通信的质量。如果你有具体的音频文件或信号需要处理，可以告诉我，我会尽力帮助你。此外，我还可以帮助你进行其他语音处理任务，如降噪、语音增强等。如果你需要进一步的帮助或有其他问题，请随时告诉我！
2025-07-02 16:12:00.291 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:12:00.291 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:12:00.846 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:12:03.540 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 应该是这个数字, 时间戳: 2025-07-02 16:12:09.752000
2025-07-02 16:12:03.870 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:12:04.692 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:12:10.902000
2025-07-02 16:12:04.692 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1150
2025-07-02 16:12:04.694 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:12:04.694 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:12:04.694 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:12:04.756 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:04.759 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:04.780 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:12:04.795 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-07-02 16:12:04.943 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:12:04.943 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:12:04.945 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:12:04.945 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:12:04.954 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 应该是这个数字; 响应时间: 0; JD机器人回复: 好的，请问你需要我帮你做什么？
2025-07-02 16:12:04.954 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:12:04.954 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:12:04.954 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:12:04.981 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:12:05.045 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:12:07.171 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你还会其他技能吗？, 时间戳: 2025-07-02 16:12:13.382000
2025-07-02 16:12:08.263 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:12:14.474000
2025-07-02 16:12:08.263 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1092
2025-07-02 16:12:08.324 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:08.327 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:08.335 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:12:08.606 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:08.617 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:08.873 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:08.885 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:09.162 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:09.173 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:09.442 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:09.450 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:09.749 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:09.749 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:10.044 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:10.047 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:10.370 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:10.371 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:12:10.372 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:10.656 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:10.664 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:10.852 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:12:10.954 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:10.954 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:11.244 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:11.248 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:11.593 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:11.602 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:11.862 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:11.867 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:11.917 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:12:12.162 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:12.168 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:12.440 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:12.444 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:12.725 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:12.734 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:12.978 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:12.980 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:13.069 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:12:13.386 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:13.397 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:13.661 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:13.664 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:13.922 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:13.929 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:14.259 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:14.262 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:14.640 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:14.652 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:14.785 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:12:14.937 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:14.937 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:12:14.941 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:14.952 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: ，你还会其他技能吗？; 响应时间: 0; JD机器人回复: 当然，我具备多种技能，包括但不限于：

1. 自然语言处理：理解和生成自然语言，进行对话和回答问题。
2. 信息检索：查找和提供各种信息，如新闻、百科知识等。
3. 文本生成：帮助撰写文章、故事、诗歌等。
4. 代码编写：提供编程帮助，解决编程问题。
5. 数学计算：进行数学计算和解答数学问题。
6. 翻译：支持多种语言的翻译。
7. 语音处理：回声消除、降噪、语音增强等。
8. 教育辅导：提供学习资源和辅导，帮助解答学习中的问题。

如果你有任何具体需求，请告诉我，我会尽力帮助你！
2025-07-02 16:12:14.952 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:12:14.952 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:12:15.064 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-02 16:12:21.272000
2025-07-02 16:12:16.317 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:12:22.523000
2025-07-02 16:12:16.317 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1251
2025-07-02 16:12:16.323 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:12:16.323 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:12:16.324 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:12:16.365 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:12:16.371 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:12:16.378 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 嗯; 响应时间: 0; JD机器人回复: 
2025-07-02 16:12:16.378 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:12:16.378 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:12:16.378 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:12:16.396 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:12:16.425 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:12:16.435 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:12:16.435 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:12:16.537 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:12:18.209 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你能用英文回答我吗？, 时间戳: 2025-07-02 16:12:24.422000
2025-07-02 16:12:19.357 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:12:25.567000
2025-07-02 16:12:19.357 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1145
2025-07-02 16:12:19.417 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:19.423 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:19.431 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:12:19.682 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:19.688 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:20.135 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:20.135 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:20.136 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:12:20.146 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 你能用英文回答我吗？; 响应时间: 0; JD机器人回复: Sure, I can respond in English. What do you need help with? Feel free to ask any questions or share what you'd like assistance with.
2025-07-02 16:12:20.146 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:12:20.146 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:12:21.851 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:12:23.562 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:12:24.698 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你能用法语回答我吗？, 时间戳: 2025-07-02 16:12:30.908000
2025-07-02 16:12:25.924 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:12:32.135000
2025-07-02 16:12:25.924 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1227
2025-07-02 16:12:25.925 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:12:25.925 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:12:25.925 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:12:25.981 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:12:26.000 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:26.001 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:26.002 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:12:26.026 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-07-02 16:12:26.302 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:26.302 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:27.059 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:27.059 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:27.060 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:12:27.071 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 你能用法语回答我吗？; 响应时间: 0; JD机器人回复: Bien sûr, je peux répondre en français. Que puis-je faire pour vous aider aujourd'hui ? N'hésitez pas à poser vos questions ou à partager ce dont vous avez besoin.
2025-07-02 16:12:27.071 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:12:27.071 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:12:28.721 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:12:30.180 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你能用, 时间戳: 2025-07-02 16:12:36.393000
2025-07-02 16:12:31.105 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:12:31.110 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:12:31.115 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:12:31.115 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:12:31.115 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:12:31.126 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 你能用; 响应时间: 0; JD机器人回复: 
2025-07-02 16:12:31.126 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:12:31.126 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:12:31.126 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:12:31.136 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:12:31.216 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:12:32.847 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 思雨回答我吗？, 时间戳: 2025-07-02 16:12:39.059000
2025-07-02 16:12:33.425 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:12:39.635000
2025-07-02 16:12:33.425 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 576
2025-07-02 16:12:33.484 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:33.485 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:33.485 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:12:33.490 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:12:33.496 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 思雨回答我吗？; 响应时间: 0; JD机器人回复: 哈哈，声音魔法没生效！
2025-07-02 16:12:33.496 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:12:33.496 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:12:33.497 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:12:36.010 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:12:40.728 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 集中语言, 时间戳: 2025-07-02 16:12:46.941000
2025-07-02 16:12:41.834 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:12:48.044000
2025-07-02 16:12:41.834 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1103
2025-07-02 16:12:41.897 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:41.901 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:41.903 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:12:42.197 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:42.201 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:42.485 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:42.485 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:12:42.491 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:42.502 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 集中语言; 响应时间: 0; JD机器人回复: 好的，请问你需要我帮你做什么？如果你有具体的需求或问题，请告诉我，我会尽力帮助你。
2025-07-02 16:12:42.502 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:12:42.502 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:12:42.753 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:12:45.225 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:12:46.241 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:12:46.241 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:12:46.306 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:12:46.307 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:12:46.307 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:12:46.333 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:12:47.636 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:12:47.696 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:12:49.563 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:12:49.563 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:12:49.629 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:12:49.629 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:12:50.933 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:12:50.994 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:12:51.322 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好东东, 时间戳: 2025-07-02 16:12:57.534000
2025-07-02 16:12:52.331 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:12:58.541000
2025-07-02 16:12:52.331 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1007
2025-07-02 16:12:52.393 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:52.398 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:52.405 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:12:52.605 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:12:52.605 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:12:52.607 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:12:52.607 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:12:52.611 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: ，你好东东; 响应时间: 0; JD机器人回复: 你好呀，我是元萝卜，
2025-07-02 16:12:52.611 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:12:52.611 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:12:52.611 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:12:52.708 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:12:52.708 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:12:53.709 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你会集中预言, 时间戳: 2025-07-02 16:12:59.921000
2025-07-02 16:12:54.849 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:13:01.055000
2025-07-02 16:12:54.850 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1134
2025-07-02 16:12:54.936 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:54.943 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:54.955 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:12:55.251 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:12:55.251 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:12:55.263 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:12:55.274 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 你会集中预言; 响应时间: 0; JD机器人回复: 嗨，东东！当然啦，我可是个小小预言家呢，想知道什么秘密尽管告诉我吧！
2025-07-02 16:12:55.274 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:12:55.274 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:12:56.333 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:12:59.490 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:13:02.514 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:13:02.514 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:13:07.824 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我想知道你会多少种语言，英语法语, 时间戳: 2025-07-02 16:13:14.035000
2025-07-02 16:13:09.043 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:13:09.048 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:13:09.050 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: ，我想知道你会多少种语言，英语法语; 响应时间: 0; JD机器人回复: 
2025-07-02 16:13:09.050 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:13:09.050 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:13:09.050 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:13:09.054 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:13:09.054 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:13:09.155 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:13:09.320 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 德语之类的, 时间戳: 2025-07-02 16:13:15.533000
2025-07-02 16:13:10.746 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:13:16.957000
2025-07-02 16:13:10.747 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1424
2025-07-02 16:13:10.807 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:13:10.811 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:13:10.811 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:13:11.537 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:13:11.545 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:13:11.877 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:13:11.878 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:13:11.881 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:13:11.891 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 德语之类的; 响应时间: 0; JD机器人回复: 嗨，我会说多种语言哦，包括英语、法语、德语、西班牙语、日语、韩语等等。你具体想了解哪种语言呢？
2025-07-02 16:13:11.891 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:13:11.891 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:13:13.336 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:13:19.078 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:13:21.798 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:13:21.798 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:13:24.351 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你会俄罗斯语吗？, 时间戳: 2025-07-02 16:13:30.563000
2025-07-02 16:13:25.664 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:13:31.873000
2025-07-02 16:13:25.665 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1310
2025-07-02 16:13:25.726 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:13:25.726 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:13:25.731 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:13:26.080 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:13:26.082 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:13:26.404 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:13:26.415 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:13:26.681 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:13:26.681 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:13:26.682 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:13:26.693 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: ，你会俄罗斯语吗？; 响应时间: 0; JD机器人回复: 嗨，我会说一点俄语哦，但可能不是特别流利。如果你需要帮助，可以告诉我具体的问题，我会尽力帮你解答。
2025-07-02 16:13:26.693 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:13:26.693 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:13:28.349 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:13:30.468 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:13:34.597 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:13:36.613 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:13:36.614 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:13:39.353 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 俄语你好，是怎么说？, 时间戳: 2025-07-02 16:13:45.566000
2025-07-02 16:13:41.612 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:13:47.820000
2025-07-02 16:13:41.612 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 2254
2025-07-02 16:13:41.650 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:13:41.651 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:13:41.659 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:13:41.969 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:13:41.975 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:13:42.260 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:13:42.264 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:13:42.614 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:13:42.618 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:13:42.930 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:13:42.930 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:13:43.219 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:13:43.220 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:13:43.225 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:13:43.236 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 俄语你好，是怎么说？; 响应时间: 0; JD机器人回复: 俄语中“你好”是“Здравствуйте”（Zdravstvuyte）。发音类似于“兹德拉斯特维杰”。这是一个比较正式的问候方式，如果是朋友之间，也可以用更随意的“Привет”（Privet），发音类似于“普里维特”。希望对你有帮助！
2025-07-02 16:13:43.236 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:13:43.236 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:13:43.272 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:13:45.186 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:13:45.186 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:13:46.294 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:13:49.317 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:13:53.049 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:13:55.469 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:13:57.284 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:13:57.284 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:14:02.838 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 嗯, 时间戳: 2025-07-02 16:14:09.049000
2025-07-02 16:14:04.203 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:14:10.413000
2025-07-02 16:14:04.203 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1364
2025-07-02 16:14:04.268 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:14:04.277 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:14:04.283 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:14:04.653 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:14:04.664 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:14:04.916 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:14:04.917 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:14:04.920 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:14:04.931 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 嗯; 响应时间: 0; JD机器人回复: 你好，有什么我可以帮你的吗？如果有任何问题或需要帮助的地方，随时告诉我哦！
2025-07-02 16:14:04.931 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:14:04.931 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:14:07.405 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:14:10.730 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:14:12.342 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:14:12.342 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:14:35.992 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，行不行，还不是看你, 时间戳: 2025-07-02 16:14:42.202000
2025-07-02 16:14:37.144 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 自行评审, 时间戳: 2025-07-02 16:14:43.355000
2025-07-02 16:14:37.144 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:14:37.146 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:14:37.146 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:14:37.151 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:14:37.165 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 自行评审; 响应时间: 0; JD机器人回复: 
2025-07-02 16:14:37.165 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:14:37.165 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:14:37.165 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:14:37.247 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:14:38.518 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:14:44.729000
2025-07-02 16:14:38.518 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1374
2025-07-02 16:14:38.578 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:14:38.579 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:14:38.581 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:14:38.881 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:14:38.883 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:14:39.171 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:14:39.174 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:14:39.751 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:14:39.751 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:14:39.763 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:14:39.774 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 自行评审; 响应时间: 0; JD机器人回复: 哈哈，你这话有点意思！不过，我确实会尽力帮你解决问题。如果你有具体的需求或者问题，就告诉我吧，我会尽我所能来帮助你。
2025-07-02 16:14:39.774 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:14:39.774 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:14:40.025 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:14:41.099 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:14:44.622 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:14:47.643 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:14:48.060 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:14:48.060 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:14:48.126 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:14:48.126 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:14:48.126 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:14:48.145 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:14:49.405 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:14:49.467 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:15:06.453 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:15:06.453 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:16:18.814 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 是不是也是？, 时间戳: 2025-07-02 16:16:25.023000
2025-07-02 16:16:20.103 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:16:26.313000
2025-07-02 16:16:20.103 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1290
2025-07-02 16:16:20.174 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:16:20.183 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:16:20.183 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:16:20.523 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:16:20.533 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:16:20.837 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:16:20.849 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:16:21.133 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:16:21.133 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:16:21.134 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:16:21.145 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 是不是也是？; 响应时间: 0; JD机器人回复: 哈哈，看来你是在跟我玩文字游戏呢！没错，你说的没错，很多事情确实要看情况，但我这里会尽力帮你解答问题。有什么需要我帮忙的吗？
2025-07-02 16:16:21.145 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:16:21.145 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:16:23.909 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:16:26.985 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退下吧, 时间戳: 2025-07-02 16:16:33.195000
2025-07-02 16:16:28.442 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:16:28.755 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:16:34.965000
2025-07-02 16:16:28.755 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1770
2025-07-02 16:16:28.763 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:16:28.763 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:16:28.763 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:16:28.798 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:16:28.846 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:16:28.865 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-07-02 16:16:29.103 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:16:29.387 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:16:29.388 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:28:17.074 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:28:17.075 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:28:17.139 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:28:17.139 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:28:18.360 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:28:18.372 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:28:19.803 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:28:22.603 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，我找了半天，找不到没办, 时间戳: 2025-07-02 16:28:28.809000
2025-07-02 16:28:23.530 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 死去了, 时间戳: 2025-07-02 16:28:29.737000
2025-07-02 16:28:23.531 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:28:23.534 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:28:23.537 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:28:23.537 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:28:23.543 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 死去了; 响应时间: 0; JD机器人回复: 
2025-07-02 16:28:23.543 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:28:23.543 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:28:23.543 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:28:23.638 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:28:25.011 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:28:31.217000
2025-07-02 16:28:25.011 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1480
2025-07-02 16:28:25.073 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:25.083 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:25.094 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:28:25.410 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:25.420 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:25.895 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:25.901 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:26.191 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:26.194 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:26.596 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:26.597 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:28:26.598 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:26.609 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 死去了; 响应时间: 0; JD机器人回复: 看起来你可能在表达上有些混乱，或者可能是输入时出现了一些问题。如果你需要帮助，可以更详细地描述一下你遇到的问题，我会尽力帮助你解决。例如，你在找什么文件、信息或者遇到什么技术问题？
2025-07-02 16:28:26.609 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:28:26.609 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:28:28.321 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:28:31.543 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:28:36.886 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:28:37.934 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你教他读那个文件，你说明你告诉他，这个文件是不是标准的, 时间戳: 2025-07-02 16:28:44.143000
2025-07-02 16:28:38.516 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:28:38.517 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:28:38.520 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:28:38.520 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:28:38.520 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:28:38.534 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: ，你教他读那个文件，你说明你告诉他，这个文件是不是标准的; 响应时间: 0; JD机器人回复: 
2025-07-02 16:28:38.534 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:28:38.534 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:28:38.534 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:28:38.599 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:28:38.621 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:28:41.112 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，然后就知道是不是军哥的问题了, 时间戳: 2025-07-02 16:28:47.320000
2025-07-02 16:28:42.838 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:28:49.044000
2025-07-02 16:28:42.838 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1724
2025-07-02 16:28:42.952 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:42.962 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:42.974 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:28:43.269 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:43.273 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:43.525 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:43.531 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:43.825 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:43.831 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:44.127 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:44.127 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:44.485 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:44.494 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:44.825 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:44.832 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:45.071 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:45.073 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:45.377 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:45.378 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:45.704 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:45.704 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:45.993 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:45.994 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:46.323 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:46.331 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:46.590 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:46.601 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:46.927 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:46.939 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:47.197 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:47.209 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:47.551 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:28:47.552 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:28:47.558 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:28:47.569 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: ，然后就知道是不是军哥的问题了; 响应时间: 0; JD机器人回复: 你的表达有些模糊，但看起来你在谈论某个文件的读取和判断其是否符合标准，进而确定是否是“军哥”的问题。为了更有效地帮助你，我需要你提供更多具体的信息，比如：

1. 文件内容：你提到的文件具体是什么类型的文件？是文本文件、代码文件还是其他类型的文件？
2. 标准：你所说的“标准”具体指什么？是某种格式标准、编码标准还是其他标准？
3. 军哥的问题：你提到的“军哥的问题”具体是指什么？是文件中的某个错误，还是与文件相关的某个特定问题？

提供这些信息后，我可以更准确地帮助你分析问题并给出建议。
2025-07-02 16:28:47.569 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:28:47.569 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:28:50.222 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:28:50.665 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:28:50.666 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:28:52.940 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:28:55.057 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:28:58.078 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:28:59.576 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 。因为，因为看库库那些动作嘛，嗯，感觉感觉好像是没有那么麻烦, 时间戳: 2025-07-02 16:29:05.784000
2025-07-02 16:29:00.696 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:00.876 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:29:07.082000
2025-07-02 16:29:00.876 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1298
2025-07-02 16:29:00.878 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:29:00.878 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:29:00.878 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:29:00.898 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:29:00.939 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:29:00.943 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:29:00.952 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:00.979 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-07-02 16:29:01.187 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:29:01.192 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:29:01.192 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:29:01.192 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:29:01.198 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 。因为，因为看库库那些动作嘛，嗯，感觉感觉好像是没有那么麻烦; 响应时间: 0; JD机器人回复: 你的描述依然有些模糊，
2025-07-02 16:29:01.198 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:29:01.198 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:29:01.198 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:29:01.254 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:29:01.294 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:29:04.661 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，但但是现在就不知道为什么，就是你们很, 时间戳: 2025-07-02 16:29:10.870000
2025-07-02 16:29:05.149 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:29:05.157 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:29:05.159 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:29:05.159 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:29:05.161 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: ，但但是现在就不知道为什么，就是你们很; 响应时间: 0; JD机器人回复: 
2025-07-02 16:29:05.161 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:29:05.161 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:29:05.161 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:29:05.262 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:29:10.839 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你们很麻烦，我知道肯定是真的，但是我们为什么调用都这么麻烦？, 时间戳: 2025-07-02 16:29:17.046000
2025-07-02 16:29:12.060 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:29:18.266000
2025-07-02 16:29:12.060 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1220
2025-07-02 16:29:12.115 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:29:12.121 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:29:12.126 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:12.492 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:29:12.501 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:29:12.774 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:29:12.780 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:29:13.083 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:29:13.085 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:29:13.377 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:29:13.388 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:29:13.650 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:29:13.653 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:29:13.942 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:29:13.954 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:29:14.230 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:29:14.234 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:29:14.442 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:14.530 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:29:14.531 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:29:14.802 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:29:14.802 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:29:15.105 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:29:15.107 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:29:15.389 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:29:15.401 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:29:15.676 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:29:15.686 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:29:15.958 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:29:15.970 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:29:16.278 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:29:16.289 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:29:16.561 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:29:16.569 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:29:16.884 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:29:16.885 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:29:16.886 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:29:16.896 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 你们很麻烦，我知道肯定是真的，但是我们为什么调用都这么麻烦？; 响应时间: 0; JD机器人回复: 看起来你在讨论某个库（库库）的使用，并且觉得调用过程比预期中麻烦。为了更好地理解你的问题，我需要你提供更多的上下文信息，比如：

1. 库的名称：你具体指的是哪个库？
2. 调用过程：你是如何调用这个库的？有没有具体的代码示例？
3. 遇到的问题：你遇到了什么具体的麻烦？是编译错误、运行错误，还是其他问题？
4. 预期结果：你期望的调用过程是怎样的？

提供这些信息后，我可以更准确地帮助你分析问题并给出建议。
2025-07-02 16:29:16.896 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:29:16.896 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:29:18.577 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:21.203 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:21.400 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:29:21.951 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:29:21.951 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:29:22.922 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:29:22.922 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:29:24.522 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:27.042 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:29.259 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:31.272 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:33.688 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:36.108 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:38.324 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:40.945 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:43.568 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:45.883 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:47.191 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:50.011 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:52.027 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:29:56.257 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:29:56.257 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:30:55.819 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:30:55.819 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:30:55.878 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:30:55.878 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:30:57.653 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:30:57.716 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:31:47.474 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:31:47.474 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:32:29.823 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:32:29.823 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:32:29.888 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:32:29.888 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:32:31.308 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:32:31.370 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:34:14.283 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:34:14.283 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:34:17.721 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，一个元素如果被两个变形器调用, 时间戳: 2025-07-02 16:34:23.927000
2025-07-02 16:34:18.219 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:34:18.224 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:34:18.226 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:34:18.226 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:34:18.230 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: ，一个元素如果被两个变形器调用; 响应时间: 0; JD机器人回复: 
2025-07-02 16:34:18.230 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:34:18.230 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:34:18.230 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:34:18.327 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:34:19.496 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 就容易出问题, 时间戳: 2025-07-02 16:34:25.703000
2025-07-02 16:34:20.841 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:34:27.046000
2025-07-02 16:34:20.841 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1343
2025-07-02 16:34:20.933 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:20.939 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:20.949 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:34:21.216 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:34:21.218 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:34:21.218 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:34:21.222 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:34:21.230 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 就容易出问题; 响应时间: 0; JD机器人回复: 你的描述表明一个元素被两个变形器调用可能会导致问题。
2025-07-02 16:34:21.230 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:34:21.230 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:34:21.230 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:34:21.252 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:34:21.319 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:34:21.508 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，好了吗？没好, 时间戳: 2025-07-02 16:34:27.713000
2025-07-02 16:34:22.942 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:34:29.146000
2025-07-02 16:34:22.942 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1433
2025-07-02 16:34:23.028 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:23.033 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:23.044 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:34:23.335 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:23.342 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:23.629 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:23.631 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:23.982 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:23.986 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:24.378 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:24.382 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:24.649 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:24.651 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:24.960 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:24.965 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:25.298 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:25.307 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:25.590 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:25.598 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:25.873 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:25.878 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:26.167 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:26.171 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:26.481 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:26.491 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:26.846 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:26.851 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:27.121 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:27.121 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:27.457 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:27.467 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:27.766 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:27.777 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:28.041 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:28.041 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:28.194 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:34:28.357 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:28.358 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:34:28.359 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:28.370 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: ，好了吗？没好; 响应时间: 0; JD机器人回复: 看来你在讨论一个元素被两个变形器调用时出现的问题。为了更好地帮助你，我需要了解以下信息：

1. 变形器的具体定义：你所说的“变形器”具体指什么？是某种库中的函数、方法，还是某种特定的处理逻辑？
2. 元素的具体类型：你所说的“元素”具体是什么类型的对象？是数据结构中的元素，还是某种特定类型的变量？
3. 问题的具体表现：当元素被两个变形器调用时，出现了什么问题？是编译错误、运行时错误，还是逻辑错误？
4. 预期的行为：你期望的结果是什么？你希望元素在被两个变形器调用时如何表现？

提供这些信息后，我可以更准确地帮助你分析问题并给出建议。
2025-07-02 16:34:28.370 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:34:28.370 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:34:32.221 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:34:35.143 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:34:35.785 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:34:37.999 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:34:38.294 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:34:38.295 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:34:40.820 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:34:42.856 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:34:42.856 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:34:43.438 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:34:46.157 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:34:48.606 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，嗯, 时间戳: 2025-07-02 16:34:54.812000
2025-07-02 16:34:49.786 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:34:49.936 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:34:56.140000
2025-07-02 16:34:49.937 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1328
2025-07-02 16:34:49.941 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:34:49.941 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:34:49.942 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:34:49.990 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:34:49.999 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:50.008 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:50.012 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:34:50.043 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-07-02 16:34:50.312 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:50.323 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:50.679 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:50.682 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:50.996 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:50.998 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:51.325 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:34:51.325 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:34:51.335 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:34:51.346 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: ，嗯; 响应时间: 0; JD机器人回复: 看来你还在思考如何描述问题。如果你能提供更多具体的信息，比如代码示例、错误信息或者问题的详细描述，我会更有针对性地帮助你解决问题。你可以随时告诉我更多细节。
2025-07-02 16:34:51.346 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:34:51.346 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:34:51.696 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:34:52.938 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:34:55.787 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:34:55.787 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:34:55.859 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:35:00.398 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:35:03.824 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:35:06.647 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:35:06.647 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:35:09.574 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，它不能一起控制吗？这个步骤只能一个一个拉吗？, 时间戳: 2025-07-02 16:35:15.780000
2025-07-02 16:35:11.283 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:35:17.488000
2025-07-02 16:35:11.284 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1708
2025-07-02 16:35:11.353 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:11.362 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:11.372 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:35:11.701 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:11.708 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:12.069 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:12.078 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:12.371 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:12.382 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:12.741 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:12.752 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:13.106 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:13.112 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:13.433 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:13.438 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:13.784 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:13.787 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:14.171 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:14.179 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:14.464 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:14.466 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:14.739 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:35:14.745 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:35:14.746 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:35:14.746 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:35:14.756 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: ，它不能一起控制吗？这个步骤只能一个一个拉吗？; 响应时间: 0; JD机器人回复: 你的问题似乎涉及到某个操作或处理步骤，可能是关于数据流、任务调度或某种处理流程。为了更好地帮助你，我需要了解以下信息：

1. 具体操作：你指的是什么操作或处理步骤？例如，是数据转换、任务调度、文件处理还是其他类型的操作？
2. 控制方式：你提到的“不能一起控制”和“一个一个拉”是什么意思？是指并行处理、顺序处理还是其他控制方式？
3. 问题表现：
2025-07-02 16:35:14.756 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:35:14.756 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:35:14.756 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:35:14.794 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:35:14.848 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:35:15.120 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 哎呀, 时间戳: 2025-07-02 16:35:21.325000
2025-07-02 16:35:16.354 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:35:22.559000
2025-07-02 16:35:16.354 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1234
2025-07-02 16:35:16.437 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:16.443 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:16.454 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:35:16.864 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:16.865 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:17.195 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:17.197 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:17.512 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:17.518 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:17.832 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:17.834 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:18.232 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:18.239 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:18.912 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:18.913 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:19.273 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:19.280 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:19.632 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:19.632 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:19.928 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:19.934 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:20.253 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:20.261 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:20.593 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:20.602 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:20.694 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:35:20.923 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:20.923 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:35:20.931 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:20.942 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 哎呀; 响应时间: 0; JD机器人回复: 你的问题看起来涉及到某种操作或处理步骤，可能是关于数据流、任务调度或某种处理流程。为了更好地帮助你，我需要了解以下信息：

1. 具体操作：你指的是什么操作或处理步骤？例如，是数据转换、任务调度、文件处理还是其他类型的操作？
2. 控制方式：你提到的“不能一起控制”和“一个一个拉”是什么意思？是指并行处理、顺序处理还是其他控制方式？
3. 问题表现：你在尝试实现什么功能时遇到了这个问题？具体出现了什么错误或异常？
4. 预期结果：你希望如何实现控制？

如果你能提供更多具体的信息，我可以更准确地帮助你分析问题并给出建议。
2025-07-02 16:35:20.942 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:35:20.942 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:35:25.079 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:35:25.080 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:35:25.127 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:35:28.616 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:35:28.616 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:35:28.682 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:35:28.682 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:35:28.682 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:35:28.755 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:35:30.029 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:35:30.091 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:35:30.489 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，啊，东东, 时间戳: 2025-07-02 16:35:36.694000
2025-07-02 16:35:31.636 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:35:37.840000
2025-07-02 16:35:31.636 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1146
2025-07-02 16:35:31.699 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:31.710 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:31.721 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:35:31.995 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:32.001 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:32.309 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:32.316 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:32.591 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:32.598 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:32.883 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:32.888 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:33.162 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:33.163 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:35:33.174 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:33.185 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: ，啊，东东; 响应时间: 0; JD机器人回复: 当然可以！你可以这样回复对方：

---

嘿，很高兴认识你呀！我叫元萝卜，你呢？快来介绍一下自己吧，别让人家等太久哦！

---

这样既俏皮又友好，希望能帮到你！
2025-07-02 16:35:33.185 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:35:33.185 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:35:35.045 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:35:36.096 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:35:36.097 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:35:36.163 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:35:36.163 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:35:36.164 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:35:36.255 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:35:37.191 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:35:37.253 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:35:38.679 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好，东东, 时间戳: 2025-07-02 16:35:44.885000
2025-07-02 16:35:39.837 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:35:46.041000
2025-07-02 16:35:39.837 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1156
2025-07-02 16:35:39.900 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:39.903 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:39.910 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:35:40.201 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:40.202 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:35:40.204 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:35:40.215 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 你好，东东; 响应时间: 0; JD机器人回复: 你好东东！听说你有问题要问？快说来听听，我可是全能小助手哦！
2025-07-02 16:35:40.215 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:35:40.215 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:35:41.520 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:35:43.031 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:35:43.919 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:35:43.919 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:35:43.982 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:35:43.982 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:35:43.983 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:35:44.040 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:35:45.271 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:35:45.331 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:35:46.889 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好东东，退出, 时间戳: 2025-07-02 16:35:53.095000
2025-07-02 16:35:48.207 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:35:54.410000
2025-07-02 16:35:48.207 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1315
2025-07-02 16:35:48.270 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:48.586 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:48.877 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:49.182 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:49.471 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:49.743 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:50.040 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:50.312 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:50.604 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:50.868 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:51.166 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:51.466 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:51.755 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:35:51.756 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:39:03.698 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:39:03.698 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:39:03.763 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:39:03.763 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:39:05.070 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:39:05.081 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:39:05.934 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:39:06.871 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:39:06.872 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:39:09.637 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:39:09.638 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:39:16.271 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 看一不一样啊, 时间戳: 2025-07-02 16:39:22.477000
2025-07-02 16:39:17.463 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:39:23.666000
2025-07-02 16:39:17.463 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1189
2025-07-02 16:39:17.536 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:39:17.545 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:39:17.545 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:39:17.853 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:39:17.855 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:39:18.130 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:39:18.136 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:39:18.442 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:39:18.444 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:39:18.830 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:39:18.832 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:39:19.028 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，motion mark motion, 时间戳: 2025-07-02 16:39:25.231000
2025-07-02 16:39:19.028 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:39:19.033 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:39:19.038 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:39:19.038 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:39:19.048 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: ，motion mark motion; 响应时间: 0; JD机器人回复: 看起来你在问某个东西是否一样，但需要更多的上下文来准确回答。你可以提供以下信息：

1. 具体比较的对象：你是在比较什么？是两个文件、两段代码、两个数据结果，
2025-07-02 16:39:19.048 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:39:19.048 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:39:19.048 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:39:19.063 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:39:19.139 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:39:20.081 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:39:26.283000
2025-07-02 16:39:20.081 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1052
2025-07-02 16:39:20.143 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:39:20.149 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:39:20.159 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:39:20.604 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:39:20.612 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:39:20.876 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:39:20.881 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:39:21.186 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:39:21.197 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:39:21.488 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:39:21.499 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:39:21.789 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:39:21.789 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:39:22.072 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:39:22.081 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:39:22.428 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:39:22.437 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:39:22.676 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:39:22.697 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:39:22.709 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:39:22.988 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:39:22.990 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:39:23.351 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:39:23.356 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:39:23.631 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:39:23.639 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:39:23.919 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:39:23.919 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:39:23.920 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:39:23.931 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: ，motion mark motion; 响应时间: 0; JD机器人回复: Sure, I can help you compare things. Could you provide more details about what you want to compare? For example:

1. Specific Items: What are the items you want to compare? Are they files, code snippets, data results, or something else?
2. Comparison Criteria: What criteria should I use to determine if they are the same or different?

Let me know, and I'll do my best to assist you!
2025-07-02 16:39:23.931 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:39:23.931 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:39:26.406 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:39:26.588 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退出, 时间戳: 2025-07-02 16:39:32.793000
2025-07-02 16:39:27.385 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:39:27.386 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:39:27.395 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:39:27.395 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:39:27.395 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:39:27.415 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:39:27.496 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:42:00.459 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:42:00.459 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:42:00.524 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:42:00.524 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:42:01.813 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:42:01.823 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:42:02.629 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，不要说了, 时间戳: 2025-07-02 16:42:08.831000
2025-07-02 16:42:03.807 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:42:10.010000
2025-07-02 16:42:03.807 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1179
2025-07-02 16:42:03.869 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:42:03.873 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:42:03.881 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:42:03.897 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:42:03.900 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:42:03.902 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:42:03.902 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:42:03.905 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: ，不要说了; 响应时间: 0; JD机器人回复: 好的，我明白你的意思了。
2025-07-02 16:42:03.905 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:42:03.905 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:42:03.905 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:42:03.982 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:42:04.003 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:42:04.672 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，退下吧, 时间戳: 2025-07-02 16:42:10.874000
2025-07-02 16:42:06.051 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:42:12.252000
2025-07-02 16:42:06.051 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1378
2025-07-02 16:42:06.146 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:42:06.468 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:42:06.468 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:43:20.540 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:43:20.540 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:43:20.605 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:43:20.605 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:43:21.884 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:43:21.895 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:43:23.510 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:43:27.708 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:43:27.709 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:44:52.796 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:44:52.797 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:44:54.833 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:44:54.835 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:45:06.171 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:45:06.172 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:45:06.239 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:45:06.239 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:45:07.266 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:45:07.326 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:45:08.200 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:45:08.200 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:45:13.935 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:45:13.936 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:45:19.464 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:45:19.465 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:45:24.388 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我, 时间戳: 2025-07-02 16:45:30.591000
2025-07-02 16:45:25.747 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:45:31.948000
2025-07-02 16:45:25.747 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1357
2025-07-02 16:45:25.845 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:45:25.853 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:45:25.858 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:45:26.218 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:45:26.219 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:45:26.230 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:45:26.241 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 我; 响应时间: 0; JD机器人回复: 我明白了。如果你有任何其他问题或需要帮助的地方，随时欢迎回来。祝你一切顺利！
2025-07-02 16:45:26.241 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:45:26.241 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:45:28.392 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:45:28.392 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:45:28.459 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:45:28.459 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:45:28.459 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:45:28.474 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:45:29.766 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:45:29.829 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:45:32.130 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:45:33.195 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:45:33.195 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:45:51.553 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:45:51.553 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:49:38.233 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:49:38.234 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:50:55.576 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:50:55.576 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:50:55.641 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:50:55.641 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:50:56.689 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:50:56.750 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:52:26.766 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:52:26.767 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:52:56.997 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:52:56.997 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:52:57.062 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:52:57.062 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:52:58.099 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:52:58.160 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:53:05.910 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:53:05.911 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:53:21.808 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:53:21.808 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:53:21.875 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:53:21.875 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:53:23.352 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:53:23.414 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:56:53.065 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:56:53.066 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 16:56:55.630 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我要刷卡的，我试一下呢, 时间戳: 2025-07-02 16:57:01.827000
2025-07-02 16:56:56.976 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:57:03.173000
2025-07-02 16:56:56.976 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1346
2025-07-02 16:56:57.042 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:56:57.044 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:56:57.054 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:56:57.367 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:56:57.375 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:56:57.686 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:56:57.696 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:56:57.976 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:56:57.976 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:56:58.201 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 16:56:58.251 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:56:58.263 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:56:58.554 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:56:58.566 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:56:58.942 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:56:58.945 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:56:59.201 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:56:59.203 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:56:59.451 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:56:59.463 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:56:59.798 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:56:59.808 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:57:00.167 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:57:00.174 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:57:00.406 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:57:00.417 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:57:00.480 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:57:00.780 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:57:00.787 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:57:01.017 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:57:01.026 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:57:01.257 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:57:01.265 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:57:01.591 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:57:01.598 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:57:02.006 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:57:02.006 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:57:02.209 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:57:02.211 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:57:02.451 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:57:02.452 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:57:02.462 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:57:02.473 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 我要刷卡的，我试一下呢; 响应时间: 0; JD机器人回复: 你好！看起来你提到“刷卡”和“试一下”，可能是在讨论某种支付或身份验证流程。为了帮助你更好地理解或解决问题，我需要更多的上下文信息。你可以提供以下信息：

1. 具体操作：你是在进行什么类型的刷卡操作？比如支付、门禁、考勤等。
2. 设备或系统：你使用的是什么样的刷卡设备或系统？比如POS机、门禁系统、考勤机等。
3. 问题描述：你在尝试刷卡时遇到了什么问题？比如无法读取卡片、操作失败等。
4. 期望结果：你希望达到什么效果？比如成功完成支付、打开门禁等。

提供这些信息后，我可以更准确地帮助你解决问题。
2025-07-02 16:57:02.473 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:57:02.473 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:57:04.206 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:57:05.428 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，退出, 时间戳: 2025-07-02 16:57:11.626000
2025-07-02 16:57:06.860 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:57:13.058000
2025-07-02 16:57:06.860 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1432
2025-07-02 16:57:06.866 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:57:06.866 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:57:06.866 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:57:06.924 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:57:06.969 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:57:06.970 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-07-02 16:57:07.304 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:57:07.306 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:58:39.802 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 16:58:39.802 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 16:58:39.867 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:58:39.867 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:58:41.339 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 16:58:41.344 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 16:58:42.259 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 你好, 时间戳: 2025-07-02 16:58:48.455000
2025-07-02 16:58:43.305 - chat_with_robot - websocket_client_thread.py - _on_message - line 196 - INFO - message response: CALL_AGENT_INTERRUPTED
2025-07-02 16:58:43.311 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:58:43.314 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:58:43.314 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:58:43.315 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: 你好; 响应时间: 0; JD机器人回复: 
2025-07-02 16:58:43.315 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:58:43.315 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:58:43.315 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:58:43.414 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 345 - INFO - 任务被打断，取消执行, task_num: 0; llm_interrupt_flag: True
2025-07-02 16:58:44.193 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，没事，没事, 时间戳: 2025-07-02 16:58:50.391000
2025-07-02 16:58:45.297 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:58:51.494000
2025-07-02 16:58:45.297 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1103
2025-07-02 16:58:45.369 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:58:45.378 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:58:45.383 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:58:45.744 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:58:45.752 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:58:46.041 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:58:46.045 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:58:46.324 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:58:46.324 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:58:46.325 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:58:46.336 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: ，没事，没事; 响应时间: 0; JD机器人回复: 你好！很高兴认识你。如果你有任何问题或需要帮助的地方，随时告诉我，我会尽力帮助你。祝你今天过得愉快！
2025-07-02 16:58:46.336 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:58:46.336 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:58:46.436 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: ，你好, 时间戳: 2025-07-02 16:58:52.634000
2025-07-02 16:58:47.444 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:58:53.642000
2025-07-02 16:58:47.444 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1008
2025-07-02 16:58:47.454 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 16:58:47.455 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 16:58:47.455 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:58:47.501 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:58:47.504 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:58:47.513 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:58:47.515 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:58:47.555 - chat_with_robot - chat_with_robot.py - _interrupt_worker - line 337 - INFO - 任务被打断，取消执行, task_num: 1; llm_interrupt_flag: False
2025-07-02 16:58:47.802 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:58:47.804 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:58:48.077 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:58:48.077 - chat_with_robot - chat_with_robot.py - _task_worker - line 420 - INFO - 存入音频
2025-07-02 16:58:48.078 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 16:58:48.088 - chat_with_robot - chat_with_robot.py - _task_worker - line 374 - INFO - session_id: 23369765-571c-11f0-a3ef-dc4546c07870; requestId: 3f37f69a-7ced-42b2-b348-e3421bdfb86c_joyinside; asr: ，你好; 响应时间: 0; JD机器人回复: 你好呀！我叫元萝卜，你呢？快来介绍一下自己吧，咱们互相认识一下！
2025-07-02 16:58:48.088 - chat_with_robot - chat_with_robot.py - _task_worker - line 376 - INFO - 等待控制完成
2025-07-02 16:58:48.088 - chat_with_robot - chat_with_robot.py - _task_worker - line 381 - INFO - 等待音频播放完成
2025-07-02 16:58:49.931 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:58:52.954 - chat_with_robot - audio_player.py - _play_loop - line 121 - INFO - 开始播放音频
2025-07-02 16:58:54.869 - chat_with_robot - audio_player.py - _play_loop - line 129 - INFO - 所有音频任务已完成
2025-07-02 16:58:54.869 - chat_with_robot - chat_with_robot.py - _task_worker - line 391 - INFO - 任务完成，继续
2025-07-02 16:58:59.169 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 退下, 时间戳: 2025-07-02 16:59:05.365000
2025-07-02 16:59:00.580 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-02 16:59:06.777000
2025-07-02 16:59:00.580 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1412
2025-07-02 16:59:00.670 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:59:00.961 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-02 16:59:00.961 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-02 17:13:26.167 - chat_with_robot - voice.py - detect_callback - line 407 - INFO - [wakeup] 检测到唤醒词
2025-07-02 17:13:26.167 - chat_with_robot - voice.py - end_streaming - line 206 - INFO - [end recording]...
2025-07-02 17:13:26.231 - chat_with_robot - audio_player.py - _clear_queue - line 93 - INFO - 队列已清空
2025-07-02 17:13:26.231 - chat_with_robot - audio_player.py - interrupt - line 75 - INFO - 音频播放已打断
2025-07-02 17:13:27.424 - chat_with_robot - voice.py - start_streaming - line 202 - INFO - [start recording]...
2025-07-02 17:13:27.430 - chat_with_robot - voice.py - run - line 463 - INFO - [run] 持续监听状态...
2025-07-02 17:13:29.888 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 17:13:32.531 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-02 17:13:32.532 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-02 17:13:35.972 - chat_with_robot - kws_wrapper.py - stop - line 124 - INFO - sherpa_onnx流式KWS检测线程已停止
2025-07-02 17:13:35.973 - chat_with_robot - voice.py - stop - line 402 - INFO - 已停止local_streaming检测器
