2025-07-11 16:13:01.620 - chat_with_robot - chat_with_robot.py - <module> - line 640 - INFO - use_action: dont
2025-07-11 16:13:01.620 - chat_with_robot - chat_with_robot.py - <module> - line 641 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-11 16:13:01.620 - chat_with_robot - chat_with_robot.py - init_websocket - line 319 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1752221581621&accessNonce=ada4fbbe-351b-4477-b1be-0943be1d1f93&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=dce860d7-5e2e-11f0-a35c-dc4546c07870&requestId=75975438-7596-40a5-aaa7-b6513f565df8_joyinside&accessSign=4fc7a3f7c8175e387e0de38aeb8d7b23, request_id: 75975438-7596-40a5-aaa7-b6513f565df8_joyinside
2025-07-11 16:13:01.622 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-11 16:13:01.622 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-11 16:13:02.083 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-11 16:13:02.262 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-07-11 16:13:03.588 - chat_with_robot - kws_wrapper.py - __init__ - line 44 - INFO - sherpa_onnx流式KWS检测器初始化成功
2025-07-11 16:13:03.590 - chat_with_robot - voice.py - _setup_audio_stream - line 324 - INFO - 使用音频设备: 1
2025-07-11 16:13:03.590 - chat_with_robot - voice.py - _setup_audio_stream - line 325 - INFO - channels: 4 <class 'int'>
2025-07-11 16:13:03.590 - chat_with_robot - voice.py - _setup_audio_stream - line 326 - INFO - rate: 44100.0 <class 'float'>
2025-07-11 16:13:03.641 - chat_with_robot - kws_wrapper.py - start - line 115 - INFO - sherpa_onnx流式KWS检测线程已启动
2025-07-11 16:13:03.641 - chat_with_robot - voice.py - init_wakeup - line 311 - INFO - 本地流式KWS检测器启动成功
2025-07-11 16:13:04.642 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-11 16:13:04.642 - chat_with_robot - chat_with_robot.py - play_audio - line 525 - INFO - 使用 audio_action_controller 播放: ./asserts/ding.wav
2025-07-11 16:13:04.644 - chat_with_robot - chat_with_robot.py - play_audio - line 527 - INFO - audio_action_controller 播放完成: ./asserts/ding.wav
2025-07-11 16:13:04.644 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 84 - INFO - Windows 系统，使用 audio_action_controller 播放: asserts/tts/dog_ok.mp3
2025-07-11 16:13:06.722 - chat_with_robot - voice.py - detect_callback - line 426 - INFO - [wakeup] 检测到唤醒词
2025-07-11 16:13:06.722 - chat_with_robot - voice.py - end_streaming - line 225 - INFO - [end recording]...
2025-07-11 16:13:06.785 - chat_with_robot - audio_player.py - _clear_queue - line 94 - INFO - 队列已清空
2025-07-11 16:13:06.785 - chat_with_robot - audio_player.py - interrupt - line 76 - INFO - 音频播放已打断
2025-07-11 16:13:06.788 - chat_with_robot - voice.py - play_audio_wakeup - line 92 - INFO - 准备播放唤醒音频: asserts/shenmeshi.wav
2025-07-11 16:13:06.788 - chat_with_robot - voice.py - play_audio_wakeup - line 100 - INFO - 使用 audio_action_controller 播放唤醒音频: asserts/shenmeshi.wav
2025-07-11 16:13:06.789 - chat_with_robot - voice.py - play_audio_wakeup - line 102 - INFO - 唤醒音频播放完成: asserts/shenmeshi.wav
2025-07-11 16:13:06.789 - chat_with_robot - voice.py - start_streaming - line 221 - INFO - [start recording]...
2025-07-11 16:13:06.794 - chat_with_robot - voice.py - run - line 484 - INFO - [run] 持续监听状态...
2025-07-11 16:13:08.060 - chat_with_robot - websocket_client_thread.py - _on_message - line 258 - INFO - 收到空ASR文本，不处理
2025-07-11 16:13:08.060 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
2025-07-11 16:13:10.605 - chat_with_robot - websocket_client_thread.py - _on_message - line 241 - INFO - 收到ASR文本: 我想知道成都天气, 时间戳: 2025-07-11 16:13:10.629000
2025-07-11 16:13:12.559 - chat_with_robot - websocket_client_thread.py - _on_message - line 277 - INFO - 收到首个TTS数据包, 时间戳: 2025-07-11 16:13:12.569000
2025-07-11 16:13:12.559 - chat_with_robot - websocket_client_thread.py - _on_message - line 279 - INFO - 从ASR-TTS返回, 时间戳: 1940
2025-07-11 16:13:12.578 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:13:12.588 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:13:12.598 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:13:12.598 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:13:12.599 - chat_with_robot - audio_player.py - _play_single_audio - line 179 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
2025-07-11 16:13:12.601 - chat_with_robot - audio_player.py - _play_single_audio - line 183 - INFO - 大模型语音播放完成
2025-07-11 16:13:12.602 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:13:12.902 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:13:12.908 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:13:12.918 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:13:12.918 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:13:12.919 - chat_with_robot - audio_player.py - _play_single_audio - line 179 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
2025-07-11 16:13:12.920 - chat_with_robot - audio_player.py - _play_single_audio - line 183 - INFO - 大模型语音播放完成
2025-07-11 16:13:12.921 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:13:13.264 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:13:13.273 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:13:13.283 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:13:13.283 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:13:13.283 - chat_with_robot - audio_player.py - _play_single_audio - line 179 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
2025-07-11 16:13:13.285 - chat_with_robot - audio_player.py - _play_single_audio - line 183 - INFO - 大模型语音播放完成
2025-07-11 16:13:13.286 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:13:13.762 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:13:13.763 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:13:13.773 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:13:13.773 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:13:13.774 - chat_with_robot - audio_player.py - _play_single_audio - line 179 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
2025-07-11 16:13:13.775 - chat_with_robot - audio_player.py - _play_single_audio - line 183 - INFO - 大模型语音播放完成
2025-07-11 16:13:13.776 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:13:14.072 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:13:14.077 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:13:14.087 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:13:14.087 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:13:14.088 - chat_with_robot - audio_player.py - _play_single_audio - line 179 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
2025-07-11 16:13:14.089 - chat_with_robot - audio_player.py - _play_single_audio - line 183 - INFO - 大模型语音播放完成
2025-07-11 16:13:14.090 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:13:14.367 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:13:14.368 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:13:14.378 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:13:14.378 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:13:14.380 - chat_with_robot - audio_player.py - _play_single_audio - line 179 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
2025-07-11 16:13:14.380 - chat_with_robot - audio_player.py - _play_single_audio - line 183 - INFO - 大模型语音播放完成
2025-07-11 16:13:14.381 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:13:14.664 - chat_with_robot - websocket_client_thread.py - _on_message - line 305 - INFO - 收到TTS数据包，放入队列
2025-07-11 16:13:14.664 - chat_with_robot - websocket_client_thread.py - _on_message - line 202 - INFO - message response: COMPLETE
2025-07-11 16:13:14.675 - chat_with_robot - chat_with_robot.py - _task_worker - line 429 - INFO - 存入音频
2025-07-11 16:13:14.685 - chat_with_robot - audio_player.py - _play_loop - line 122 - INFO - 开始播放音频
2025-07-11 16:13:14.685 - chat_with_robot - audio_player.py - _play_single_audio - line 170 - INFO - 准备播放大模型合成的语音
2025-07-11 16:13:14.686 - chat_with_robot - chat_with_robot.py - _task_worker - line 383 - INFO - session_id: dce860d7-5e2e-11f0-a35c-dc4546c07870; requestId: 75975438-7596-40a5-aaa7-b6513f565df8_joyinside; asr: 我想知道成都天气; 响应时间: 0; JD机器人回复: 今天成都的天气是阵雨，最高气温30度，最低气温24度。体感温度为25度，空气湿度100%，风力为4级东北风，能见度10公里。今天白天有阵雨，夜晚多云，比昨天凉爽很多。出门记得带把伞，享受这凉爽的一天吧！
2025-07-11 16:13:14.686 - chat_with_robot - audio_player.py - _play_single_audio - line 179 - INFO - 使用 audio_action_controller 播放大模型语音: D:\prooject_code\Possessed_AI\util\..\temp_tts_audio.mp3
2025-07-11 16:13:14.686 - chat_with_robot - chat_with_robot.py - _task_worker - line 385 - INFO - 等待控制完成
2025-07-11 16:13:14.686 - chat_with_robot - chat_with_robot.py - _task_worker - line 390 - INFO - 等待音频播放完成
2025-07-11 16:13:14.686 - chat_with_robot - chat_with_robot.py - _task_worker - line 400 - INFO - 任务完成，继续
2025-07-11 16:13:14.687 - chat_with_robot - audio_player.py - _play_single_audio - line 183 - INFO - 大模型语音播放完成
2025-07-11 16:13:14.688 - chat_with_robot - audio_player.py - _play_loop - line 130 - INFO - 所有音频任务已完成
2025-07-11 16:14:41.066 - chat_with_robot - websocket_client_thread.py - _on_message - line 190 - INFO - message response: EMPTY_CONTENT
