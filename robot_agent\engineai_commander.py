"""
@Author: lidong<PERSON><EMAIL>
@Create Date: 2025.04.09
@Description: EngineAI机器人通信管理

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
"""
import socket
import time
import threading
from util.logger import logger

class RobotCommanderEngineAI:
    """EngineAI机器人通信管理类"""
    
    def __init__(self, tcp_ip="*************", tcp_port=30000):
        """初始化EngineAI机器人通信管理器
        
        Args:
            tcp_ip: EngineAI机器人IP地址
            tcp_port: EngineAI机器人通信端口
        """
        self.tcp_ip = tcp_ip
        self.tcp_port = tcp_port
        self.socket = None
        self.connected = False
        self.lock = threading.Lock()  # 线程锁，用于线程安全
        
        # 尝试建立初始连接
        self.connect()
    
    def connect(self):
        """建立与EngineAI机器人的连接"""
        try:
            with self.lock:
                if self.socket:
                    self.socket.close()
                
                self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.socket.settimeout(5)  # 5秒超时
                self.socket.connect((self.tcp_ip, self.tcp_port))
                self.connected = True
                logger.info(f"成功连接到EngineAI机器人 {self.tcp_ip}:{self.tcp_port}")
                return True
        except Exception as e:
            self.connected = False
            logger.error(f"连接EngineAI机器人失败: {str(e)}")
            return False
    
    def check_connection(self):
        """检查与EngineAI机器人的连接状态"""
        try:
            with self.lock:
                if not self.connected or not self.socket:
                    return self.connect()
                return True
        except Exception as e:
            logger.error(f"检查连接失败: {str(e)}")
            self.connected = False
            return False
    
    def send_command(self, command):
        """发送命令到EngineAI机器人"""
        try:
            with self.lock:
                if not self.connected:
                    if not self.connect():
                        return False
                
                self.socket.sendall(command.encode('utf-8'))
                logger.info(f"发送命令: {command} 到 {self.tcp_ip}:{self.tcp_port}")
                return True
        except Exception as e:
            logger.error(f"发送命令失败: {str(e)}")
            self.connected = False
            return False
    
    def run(self, command):
        """运行命令"""
        return self.send_command(command)
    
    def on_closing(self):
        """关闭连接"""
        try:
            with self.lock:
                if self.socket:
                    self.socket.close()
                    self.socket = None
                self.connected = False
                logger.info("已关闭与EngineAI机器人的连接")
        except Exception as e:
            logger.error(f"关闭连接时出错: {str(e)}")
