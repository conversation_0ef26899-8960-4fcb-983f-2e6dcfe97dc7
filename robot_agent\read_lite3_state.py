"""
@Author: <EMAIL> <EMAIL>
@Create Date: 2025.03.28
@Description: 

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
"""
from dataclasses import dataclass
from typing import List
import socket
import struct
from typing import <PERSON><PERSON>

@dataclass
class RobotStateRecive:
    robot_basic_state: int
    robot_gait_state: int
    rpy: List[float]  # 长度为3的数组
    rpy_vel: List[float]  # 长度为3的数组
    xyz_acc: List[float]  # 长度为3的数组
    pos_world: List[float]  # 长度为3的数组
    vel_world: List[float]  # 长度为3的数组
    vel_body: List[float]  # 长度为3的数组
    touch_down_and_stair_trot: int
    is_charging: bool
    error_state: int
    robot_motion_state: int
    battery_level: float
    task_state: int
    is_robot_need_move: bool
    zero_position_flag: bool
    ultrasound: List[float]  # 长度为2的数组

@dataclass
class RobotStateReceived:
    code: int
    size: int
    cons_code: int
    data: RobotStateRecive

def print_robot_state(state: RobotStateRecive) -> None:
    print(f"robot_basic_state: {state.robot_basic_state}")
    print(f"robot_gait_state: {state.robot_gait_state}")
    for i, val in enumerate(state.rpy):
        print(f"rpy[{i}]: {val}")
    for i, val in enumerate(state.rpy_vel):
        print(f"rpy_vel[{i}]: {val}")
    for i, val in enumerate(state.xyz_acc):
        print(f"xyz_acc[{i}]: {val}")
    for i, val in enumerate(state.pos_world):
        print(f"pos_world[{i}]: {val}")
    for i, val in enumerate(state.vel_world):
        print(f"vel_world[{i}]: {val}")
    for i, val in enumerate(state.vel_body):
        print(f"vel_body[{i}]: {val}")
    print(f"touch_down_and_stair_trot: {state.touch_down_and_stair_trot}")
    print(f"is_charging: {state.is_charging}")
    print(f"error_state: {state.error_state}")
    print(f"robot_motion_state: {state.robot_motion_state}")
    print(f"battery_level: {state.battery_level}")
    print(f"task_state: {state.task_state}")
    print(f"is_robot_need_move: {state.is_robot_need_move}")
    print(f"zero_position_flag: {state.zero_position_flag}")
    for i, val in enumerate(state.ultrasound):
        print(f"ultrasound[{i}]: {val}")

def print_robot_state_received(state_received: RobotStateReceived) -> None:
    print(f"Received Code: {hex(state_received.code)}")
    print(f"Received Size: {state_received.size}")
    print(f"Received Cons Code: {state_received.cons_code}")
    print_robot_state(state_received.data)

def main():
    # 创建UDP socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    server_address = ('*************', 43897)

    try:
        # 绑定socket到指定地址和端口
        sock.bind(server_address)
        print("Waiting for messages...")

        while True:
            buffer, client_address = sock.recvfrom(1024)
            
            # 打印接收到的数据大小，用于调试
            print(f"Received buffer size: {len(buffer)}")
            
            # 计算预期的结构体大小
            header_size = struct.calcsize('3i')  # code, size, cons_code
            data_size = struct.calcsize('2i18d1I1?1I1i1d1i2?2d')  # RobotStateRecive结构
            total_size = header_size + data_size
            
            print(f"Expected size: {total_size}")

            # 仅在接收数据大小等于结构体大小时处理
            if len(buffer) == total_size:
                # 解析头部
                code, size, cons_code = struct.unpack('3i', buffer[:header_size])
                
                # 解析RobotStateRecive数据
                data_format = '2i18d1I1?1I1i1d1i2?2d'
                try:
                    data = struct.unpack(data_format, buffer[header_size:])
                    
                    robot_state = RobotStateRecive(
                        robot_basic_state=data[0],
                        robot_gait_state=data[1],
                        rpy=list(data[2:5]),
                        rpy_vel=list(data[5:8]),
                        xyz_acc=list(data[8:11]),
                        pos_world=list(data[11:14]),
                        vel_world=list(data[14:17]),
                        vel_body=list(data[17:20]),
                        touch_down_and_stair_trot=data[20],  # unsigned int
                        is_charging=data[21],                # bool
                        error_state=data[22],                # unsigned int
                        robot_motion_state=data[23],         # int
                        battery_level=data[24],              # double
                        task_state=data[25],                 # int
                        is_robot_need_move=data[26],         # bool
                        zero_position_flag=data[27],         # bool
                        ultrasound=list(data[28:30])        # double[2]
                    )

                    state_received = RobotStateReceived(
                        code=code,
                        size=size,
                        cons_code=cons_code,
                        data=robot_state
                    )

                    if state_received.code == 0x0901:
                        print_robot_state_received(state_received)
                
                except struct.error as e:
                    print(f"Failed to unpack data: {e}")
                    print(f"Buffer length: {len(buffer)}")
                    print(f"Format size: {struct.calcsize(data_format)}")

    except Exception as e:
        print(f"Error occurred: {e}")
    finally:
        sock.close()

if __name__ == "__main__":
    main()