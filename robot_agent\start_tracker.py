import subprocess
import os
import threading
import time
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from util.logger import logger

class TrackingHelper:
    def __init__(self):
        self.tracker_process = None
        self.tracking_running = False
    
    def start_tracking(self, auto_select=False):
        """
        启动跟踪程序
        
        Args:
            auto_select (bool): 是否自动选择中间目标
        """
        if self.tracking_running:
            return True
            
        try:
            # 创建跟踪程序的工作目录
            track_dir = "/home/<USER>/lite_cog/track/src"
            if not os.path.exists(track_dir):
                return False
                
            # 启动跟踪程序，添加自动选择参数
            cmd = ["python3", "run_tracker_auto_select.py"]
            if auto_select:
                cmd.append("--auto_select")
                
            self.tracker_process = subprocess.Popen(
                cmd,
                cwd=track_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待程序初始化（约40秒）
            self.tracking_running = True
            logger.info("跟踪程序已启动，正在初始化，请等待约40秒...")
            return True
        except Exception as e:
            logger.error(f"启动跟踪程序失败: {e}")
            return False
    
    def stop_tracking(self):
        """停止跟踪程序"""
        if self.tracker_process:
            self.tracker_process.terminate()
            self.tracker_process = None
            self.tracking_running = False
            logger.info("跟踪程序已停止")