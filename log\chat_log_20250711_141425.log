2025-07-11 14:14:26.593 - chat_with_robot - chat_with_robot.py - <module> - line 640 - INFO - use_action: dont
2025-07-11 14:14:26.593 - chat_with_robot - chat_with_robot.py - <module> - line 641 - INFO - 
[启动HardwareAIAgent交互程序]

2025-07-11 14:14:26.593 - chat_with_robot - chat_with_robot.py - init_websocket - line 319 - INFO - url: wss://joyinside.jd.com/soulmate/voiceCall/v4?accessVersion=V2&accessTimestamp=1752214466593&accessNonce=249f9a95-08a2-45aa-a4e9-46b27bfed078&accessKeyId=cfa57b9ed4d740dd9128a211&botId=da6fecedd53343bc8d96fe04a000c41d&sessionId=4c055197-5e1e-11f0-9964-dc4546c07870&requestId=22737516-fffd-40df-a577-fc1fbb21e141_joyinside&accessSign=e3f6613daa8cf594c30641330751c4f7, request_id: 22737516-fffd-40df-a577-fc1fbb21e141_joyinside
2025-07-11 14:14:26.594 - chat_with_robot - websocket_client_thread.py - _cleanup - line 405 - INFO - 开始清理旧连接...
2025-07-11 14:14:26.595 - chat_with_robot - websocket_client_thread.py - _cleanup - line 427 - INFO - 清理完成
2025-07-11 14:14:26.991 - chat_with_robot - websocket_client_thread.py - _on_open - line 331 - INFO - WebSocket连接建立
2025-07-11 14:14:27.132 - chat_with_robot - audio_player.py - start - line 53 - INFO - 音频播放线程已启动
2025-07-11 14:14:27.158 - chat_with_robot - voice.py - init_wakeup - line 314 - ERROR - 初始化本地流式KWS失败: No module named 'sherpa_onnx'
2025-07-11 14:14:28.160 - chat_with_robot - chat_with_robot.py - play_audio - line 517 - INFO - 准备播放音频文件: ./asserts/ding.wav
2025-07-11 14:14:28.160 - chat_with_robot - chat_with_robot.py - play_audio - line 525 - INFO - 使用 audio_action_controller 播放: ./asserts/ding.wav
2025-07-11 14:14:29.214 - chat_with_robot - chat_with_robot.py - play_audio - line 527 - INFO - audio_action_controller 播放完成: ./asserts/ding.wav
2025-07-11 14:14:29.214 - chat_with_robot - chat_with_robot.py - play_tts_voice - line 84 - INFO - Windows 系统，使用 audio_action_controller 播放: asserts/tts/dog_ok.mp3
