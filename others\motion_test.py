"""
@Author: lid<PERSON><PERSON><EMAIL>
@Create Date: 2025.04.01
@Description: 用于测试机器人动作的命令行工具，只需在终端输入动作编号即可向机器人发送指令

Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
"""
import sys
import os
import time
import argparse

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from robot_agent.robot_commander import RobotCommander
from robot_agent.robot_config import ROBOT_CONFIG
from robot_agent.robot_controller import RobotController
from util.logger import logger

# Add this import for ROS functionality
try:
    import rospy
    from std_msgs.msg import String
    ROS_AVAILABLE = True
except ImportError:
    logger.warning("ROS not available, some functionality will be limited")
    ROS_AVAILABLE = False

# Add this function to create a mock ROS commander if needed
def create_ros_commander():
    """Create a ROS commander for publishing messages or a mock if ROS is not available"""
    if ROS_AVAILABLE:
        try:
            rospy.init_node('motion_test', anonymous=True)
            publisher = rospy.Publisher('/follow_cmd', String, queue_size=10)
            return publisher
        except Exception as e:
            logger.error(f"Error initializing ROS: {e}")
            return MockRosCommander()
    else:
        return MockRosCommander()

# Add a mock ROS commander class
class MockRosCommander:
    """Mock class for ROS commander when ROS is not available"""
    def publish(self, msg):
        logger.warning("ROS not available, cannot publish message")
        logger.info(f"Would have published: {msg.data if hasattr(msg, 'data') else msg}")

def check_robot_connection(ip="*************"):
    """检查机器人连接状态"""
    try:
        import subprocess
        logger.info(f"检查网络连接到机器人 {ip}...")
        ping_result = subprocess.run(['ping', '-c', '1', '-W', '1', ip], 
                                    stdout=subprocess.PIPE, 
                                    stderr=subprocess.PIPE)
        if ping_result.returncode == 0:
            logger.info(f"网络连接正常，可以ping通机器人 {ip}")
            return True
        else:
            logger.error(f"警告: 无法ping通机器人 {ip}，请检查网络连接")
            return False
    except Exception as e:
        logger.error(f"网络检查失败: {e}")
        return False

def print_skill_menu(controller):
    """打印技能菜单"""
    print("\n======== 机器狗动作测试工具 ========")
    print("可用动作列表:")
    for skill_id, skill_name in controller.skill_id_to_name.items():
        print(f"{skill_id:2d}. {skill_name}")
    print("0. 退出程序")
    print("====================================")

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="机器人动作测试工具")
    parser.add_argument('--ip', type=str, default=None,
                        help="机器人IP地址，默认使用配置文件中的IP")
    args = parser.parse_args()
    
    # 获取机器人IP
    robot_ip = args.ip if args.ip else ROBOT_CONFIG.get("ctl_ip", "*************")
    
    # 检查机器人连接
    logger.info(f"尝试连接机器人 {robot_ip}...")
    check_result = check_robot_connection(robot_ip)
    while not check_result:
        user_input = input("无法连接到机器人，是否重试? (y/n): ")
        if user_input.lower() != 'y':
            logger.error("退出程序")
            return
        check_result = check_robot_connection(robot_ip)
        time.sleep(1)
    
    # 初始化机器人控制器
    logger.info("初始化机器人控制器...")
    commander = RobotCommander()
    
    # 测试心跳连接
    result_heartbeat = commander.run('心跳')
    count = 0
    while not result_heartbeat and count < 5:
        logger.error("机器人心跳连接未建立，重试中...")
        result_heartbeat = commander.run('心跳')
        time.sleep(1)
        count += 1
    
    if not result_heartbeat:
        logger.error("无法建立机器人心跳连接，退出程序")
        return
    
    # 创建控制器
    logger.info("创建机器人控制器...")
    ros_commander = create_ros_commander()
    controller = RobotController(commander, ros_commander)
    
    # 初始化机器人状态
    logger.info("初始化机器人状态...")
    commander.run("回零")
    time.sleep(1.5)
    
    # 主循环
    try:
        while True:
            print_skill_menu(controller)
            user_input = input("请输入动作编号: ")
            
            try:
                skill_id = int(user_input)
                
                if skill_id == 0:
                    logger.info("退出程序")
                    break
                    
                if skill_id in controller.skill_id_to_name:
                    logger.info(f"执行动作: {controller.skill_id_to_name[skill_id]}")
                    controller.control_robot(skill_id)
                else:
                    logger.warning(f"无效的动作编号: {skill_id}")
                    
            except ValueError:
                logger.warning("请输入有效的数字")
                
    except KeyboardInterrupt:
        logger.info("用户中断，退出程序")
    finally:
        # 关闭连接
        logger.info("关闭连接...")
        commander.on_closing()

if __name__ == "__main__":
    main()